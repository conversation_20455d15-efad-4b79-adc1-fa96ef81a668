
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface RadarStats {
  phishing: {
    total: number;
    trend: number;
    topTargets: string[];
  };
  spoofing: {
    total: number;
    trend: number;
    topMethods: string[];
  };
  dmarc: {
    adoptionRate: number;
    compliance: number;
    trend: number;
  };
  industryRisks: Record<string, number>;
  lastUpdated: string;
  dataSource?: 'cloudflare_radar_api' | 'fallback_no_token' | 'fallback_api_error' | 'fallback_critical_error' | 'fallback';
  error?: string;
}

export const useCloudflareRadar = () => {
  const [radarData, setRadarData] = useState<RadarStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchRadarData = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: functionError } = await supabase.functions.invoke('cloudflare-radar-stats');

      if (functionError) {
        throw functionError;
      }

      setRadarData(data);
    } catch (err) {
      console.error('Error fetching Cloudflare Radar data:', err);
      setError('Failed to load threat intelligence data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRadarData();
    
    // Refresh data every 30 minutes
    const interval = setInterval(fetchRadarData, 30 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  return {
    radarData,
    loading,
    error,
    refetch: fetchRadarData
  };
};
