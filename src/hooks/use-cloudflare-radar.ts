
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface RadarStats {
  phishing: {
    total: number;
    trend: number;
    topTargets: string[];
    recentAnomalies?: any[]; // Enhanced MCP data
  };
  spoofing: {
    total: number;
    trend: number;
    topMethods: string[];
    detectionRate?: number; // Enhanced MCP data
  };
  dmarc: {
    adoptionRate: number;
    compliance: number;
    trend: number;
    encryptionRate?: number; // Enhanced MCP data
  };
  industryRisks: Record<string, number>;
  domainIntelligence?: { // Enhanced MCP data
    suspiciousDomains: string[];
    brandRisks: any[];
  };
  lastUpdated: string;
  dataSource?: 'cloudflare_radar_mcp_hybrid' | 'cloudflare_radar_api' | 'fallback_mcp_error' | 'fallback_mcp_unavailable' | 'fallback_no_token' | 'fallback_api_error' | 'fallback_critical_error' | 'fallback';
  error?: string;
  mcpMetadata?: { // Enhanced MCP metadata
    toolsUsed: string[];
    dataFreshness: string;
    fallbackMode?: string;
  };
}

export const useCloudflareRadar = () => {
  const [radarData, setRadarData] = useState<RadarStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchRadarData = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: functionError } = await supabase.functions.invoke('cloudflare-radar-stats');

      if (functionError) {
        throw functionError;
      }

      setRadarData(data);

      // Log MCP integration status
      if (data.dataSource === 'cloudflare_radar_mcp_hybrid') {
        console.log('✅ MCP Integration: Successfully loaded enhanced Cloudflare Radar data');
        if (data.mcpMetadata) {
          console.log('🛠️ MCP Tools Used:', data.mcpMetadata.toolsUsed);
          console.log('📊 Data Freshness:', data.mcpMetadata.dataFreshness);
        }
      } else {
        console.log('⚠️ MCP Integration: Using fallback data source:', data.dataSource);
        if (data.error) {
          console.log('❌ Error details:', data.error);
        }
      }

    } catch (err) {
      console.error('Error fetching Cloudflare Radar data:', err);
      setError('Failed to load threat intelligence data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRadarData();
    
    // Refresh data every 30 minutes
    const interval = setInterval(fetchRadarData, 30 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  // Helper function to check if we have enhanced MCP data
  const hasEnhancedData = radarData?.dataSource === 'cloudflare_radar_mcp_hybrid' && radarData?.mcpMetadata;

  // Helper function to check if data is real-time
  const isRealTimeData = radarData?.mcpMetadata?.dataFreshness === 'real-time';

  return {
    radarData,
    loading,
    error,
    refetch: fetchRadarData,
    hasEnhancedData,
    isRealTimeData,
    mcpStatus: {
      isActive: hasEnhancedData,
      dataSource: radarData?.dataSource || 'unknown',
      toolsUsed: radarData?.mcpMetadata?.toolsUsed || [],
      fallbackMode: radarData?.mcpMetadata?.fallbackMode
    }
  };
};
