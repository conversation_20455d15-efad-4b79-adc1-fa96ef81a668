
import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useABTesting } from "@/hooks/use-ab-testing";
import { TestTube, Play, Pause, BarChart3 } from "lucide-react";

interface ABTestConfig {
  name: string;
  description: string;
  test_type: string;
  hypothesis: string;
  success_metric: string;
}

export const ABTestingDashboard = () => {
  const { 
    tests, 
    loading, 
    error, 
    getTestResults, 
    createTest, 
    refreshData 
  } = useABTesting();

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [testConfig, setTestConfig] = useState<ABTestConfig>({
    name: '',
    description: '',
    test_type: 'cta_button',
    hypothesis: '',
    success_metric: 'conversion_rate'
  });

  const handleCreateTest = async () => {
    try {
      await createTest({
        ...testConfig,
        is_active: true,
        start_date: new Date().toISOString(),
      });
      setShowCreateForm(false);
      setTestConfig({
        name: '',
        description: '',
        test_type: 'cta_button',
        hypothesis: '',
        success_metric: 'conversion_rate'
      });
      refreshData();
    } catch (err) {
      console.error('Failed to create test:', err);
    }
  };

  const getActiveTests = () => {
    return tests.filter(test => test.is_active);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-muted-foreground">Loading A/B tests...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-red-600">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">A/B Testing Dashboard</h2>
        <Button onClick={() => setShowCreateForm(true)}>
          <TestTube className="h-4 w-4 mr-2" />
          Create New Test
        </Button>
      </div>

      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle>Create New A/B Test</CardTitle>
            <CardDescription>
              Set up a new A/B test to measure conversion improvements
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="test-name">Test Name</Label>
              <Input
                id="test-name"
                value={testConfig.name}
                onChange={(e) => setTestConfig(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., Homepage CTA Button Test"
              />
            </div>
            
            <div>
              <Label htmlFor="test-description">Description</Label>
              <Textarea
                id="test-description"
                value={testConfig.description}
                onChange={(e) => setTestConfig(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Brief description of what you're testing"
              />
            </div>

            <div>
              <Label htmlFor="test-type">Test Type</Label>
              <Select 
                value={testConfig.test_type} 
                onValueChange={(value) => setTestConfig(prev => ({ ...prev, test_type: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select test type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cta_button">CTA Button</SelectItem>
                  <SelectItem value="headline">Headline</SelectItem>
                  <SelectItem value="layout">Page Layout</SelectItem>
                  <SelectItem value="form">Form Design</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="hypothesis">Hypothesis</Label>
              <Textarea
                id="hypothesis"
                value={testConfig.hypothesis}
                onChange={(e) => setTestConfig(prev => ({ ...prev, hypothesis: e.target.value }))}
                placeholder="What do you expect to happen and why?"
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateTest}>
                Create Test
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {tests.length === 0 ? (
          <Card className="col-span-full">
            <CardContent className="p-8 text-center">
              <TestTube className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No A/B Tests Yet</h3>
              <p className="text-muted-foreground mb-4">
                Create your first A/B test to start measuring conversion improvements.
              </p>
              <Button onClick={() => setShowCreateForm(true)}>
                Create Your First Test
              </Button>
            </CardContent>
          </Card>
        ) : (
          tests.map((test) => {
            const results = getTestResults(test.id);
            
            return (
              <Card key={test.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{test.name}</CardTitle>
                      <CardDescription>{test.description}</CardDescription>
                    </div>
                    <Badge variant={test.is_active ? "default" : "secondary"}>
                      {test.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <div className="text-sm font-medium">Test Type</div>
                      <div className="text-sm text-muted-foreground capitalize">
                        {test.test_type.replace('_', ' ')}
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-sm font-medium">Success Metric</div>
                      <div className="text-sm text-muted-foreground">
                        {test.success_metric}
                      </div>
                    </div>

                    {results.length > 0 && (
                      <div>
                        <div className="text-sm font-medium mb-2">Results</div>
                        {results.map((result, index) => (
                          <div key={index} className="text-xs space-y-1">
                            <div className="flex justify-between">
                              <span>{result.variant.name}</span>
                              <span>{result.conversionRate}%</span>
                            </div>
                            <div className="text-muted-foreground">
                              {result.participations} participants, {result.conversions} conversions
                            </div>
                          </div>
                        ))}
                      </div>
                    )}

                    <div className="flex justify-between items-center pt-2">
                      <Button variant="outline" size="sm">
                        <BarChart3 className="h-3 w-3 mr-1" />
                        View Details
                      </Button>
                      <Button variant="outline" size="sm">
                        {test.is_active ? (
                          <>
                            <Pause className="h-3 w-3 mr-1" />
                            Pause
                          </>
                        ) : (
                          <>
                            <Play className="h-3 w-3 mr-1" />
                            Resume
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>

      {getActiveTests().length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Active Tests Summary</CardTitle>
            <CardDescription>
              Currently running {getActiveTests().length} active test{getActiveTests().length !== 1 ? 's' : ''}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-4">
              <div className="text-2xl font-bold text-blue-600">
                {getActiveTests().length}
              </div>
              <div className="text-sm text-muted-foreground">
                Active A/B Tests
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
