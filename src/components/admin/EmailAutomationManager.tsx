
import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Play, 
  Pause, 
  Mail, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  RefreshCw,
  Settings
} from 'lucide-react';

interface EmailQueueItem {
  id: string;
  email_type: string;
  recipient_email: string;
  recipient_name?: string;
  company_name?: string;
  status: string;
  scheduled_at: string;
  sent_at?: string;
  attempts: number;
  error_message?: string;
  created_at: string;
}

interface QueueStats {
  total_queued: number;
  pending: number;
  sent: number;
  failed: number;
  next_scheduled?: string;
}

export const EmailAutomationManager = () => {
  const [emailQueue, setEmailQueue] = useState<EmailQueueItem[]>([]);
  const [queueStats, setQueueStats] = useState<QueueStats | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const fetchEmailQueue = async () => {
    try {
      const { data: queueData, error: queueError } = await supabase
        .from('email_queue')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(50);

      if (queueError) throw queueError;
      setEmailQueue(queueData || []);

      // Get queue statistics
      const { data: statsData, error: statsError } = await supabase
        .rpc('get_email_queue_status');

      if (statsError) {
        console.error('Stats error:', statsError);
      } else if (statsData && statsData.length > 0) {
        setQueueStats(statsData[0]);
      }

    } catch (error) {
      console.error('Error fetching email queue:', error);
      toast({
        title: "Error",
        description: "Failed to fetch email queue data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const processEmailQueue = async () => {
    setIsProcessing(true);
    try {
      console.log('🔄 Manually triggering email queue processing...');
      
      const { data, error } = await supabase.functions.invoke('process-email-queue', {
        body: { manual: true }
      });

      if (error) throw error;

      console.log('✅ Email queue processing result:', data);
      
      toast({
        title: "Email Queue Processed",
        description: `Processed ${data.results?.processed || 0} emails. ${data.results?.successful || 0} successful, ${data.results?.failed || 0} failed.`,
      });

      // Refresh the queue data
      await fetchEmailQueue();

    } catch (error: any) {
      console.error('❌ Error processing email queue:', error);
      toast({
        title: "Processing Failed",
        description: error.message || "Failed to process email queue",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const testEmailSending = async (emailId: string) => {
    try {
      console.log(`🧪 Testing email send for ID: ${emailId}`);
      
      const { data, error } = await supabase.functions.invoke('send-lead-email', {
        body: { emailQueueId: emailId }
      });

      if (error) throw error;

      toast({
        title: "Test Email Sent",
        description: "Email delivery test completed successfully",
      });

      await fetchEmailQueue();

    } catch (error: any) {
      console.error('❌ Test email failed:', error);
      toast({
        title: "Test Failed",
        description: error.message || "Failed to send test email",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    fetchEmailQueue();
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchEmailQueue, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusBadge = (status: string, attempts: number) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary" className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          Pending
        </Badge>;
      case 'sent':
        return <Badge variant="default" className="flex items-center gap-1 bg-green-600">
          <CheckCircle className="h-3 w-3" />
          Sent
        </Badge>;
      case 'failed':
        return <Badge variant="destructive" className="flex items-center gap-1">
          <XCircle className="h-3 w-3" />
          Failed ({attempts}/3)
        </Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
          Loading email automation data...
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Email Automation</h2>
          <p className="text-muted-foreground">Monitor and manage automated email campaigns</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={fetchEmailQueue}
            variant="outline"
            size="sm"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            onClick={processEmailQueue}
            disabled={isProcessing}
            size="sm"
          >
            {isProcessing ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            Process Queue
          </Button>
        </div>
      </div>

      {/* Queue Statistics */}
      {queueStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="flex items-center p-6">
              <Mail className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <p className="text-2xl font-bold">{queueStats.total_queued}</p>
                <p className="text-xs text-muted-foreground">Total Queued</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="flex items-center p-6">
              <Clock className="h-8 w-8 text-yellow-600 mr-3" />
              <div>
                <p className="text-2xl font-bold">{queueStats.pending}</p>
                <p className="text-xs text-muted-foreground">Pending</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="flex items-center p-6">
              <CheckCircle className="h-8 w-8 text-green-600 mr-3" />
              <div>
                <p className="text-2xl font-bold">{queueStats.sent}</p>
                <p className="text-xs text-muted-foreground">Sent</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="flex items-center p-6">
              <XCircle className="h-8 w-8 text-red-600 mr-3" />
              <div>
                <p className="text-2xl font-bold">{queueStats.failed}</p>
                <p className="text-xs text-muted-foreground">Failed</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Email Queue Table */}
      <Card>
        <CardHeader>
          <CardTitle>Email Queue</CardTitle>
          <CardDescription>
            Recent emails in the automation queue
          </CardDescription>
        </CardHeader>
        <CardContent>
          {emailQueue.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No emails in queue
            </div>
          ) : (
            <div className="space-y-4">
              {emailQueue.map((email) => (
                <div
                  key={email.id}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{email.recipient_email}</h4>
                      {getStatusBadge(email.status, email.attempts)}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {email.company_name} • {email.email_type} • 
                      Scheduled: {formatDateTime(email.scheduled_at)}
                    </p>
                    {email.error_message && (
                      <p className="text-sm text-red-600 mt-1">
                        Error: {email.error_message}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {email.status === 'pending' && (
                      <Button
                        onClick={() => testEmailSending(email.id)}
                        variant="outline"
                        size="sm"
                      >
                        <Mail className="h-4 w-4 mr-1" />
                        Test Send
                      </Button>
                    )}
                    <div className="text-xs text-muted-foreground text-right">
                      {email.sent_at ? (
                        <div>Sent: {formatDateTime(email.sent_at)}</div>
                      ) : (
                        <div>Attempts: {email.attempts}/3</div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default EmailAutomationManager;
