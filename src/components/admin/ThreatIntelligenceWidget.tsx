
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Shield, AlertTriangle, Mail, Globe } from 'lucide-react';
import { useCloudflareRadar } from '@/hooks/use-cloudflare-radar';

export const ThreatIntelligenceWidget = () => {
  const { radarData, loading, error } = useCloudflareRadar();

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            <span>Failed to load threat intelligence data</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!radarData) return null;

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const getTrendIcon = (trend: number) => {
    if (trend > 0) {
      return <TrendingUp className="h-4 w-4 text-red-500" />;
    } else if (trend < 0) {
      return <TrendingDown className="h-4 w-4 text-green-500" />;
    }
    return null;
  };

  const getTrendColor = (trend: number) => {
    if (trend > 0) return 'text-red-600';
    if (trend < 0) return 'text-green-600';
    return 'text-gray-600';
  };

  return (
    <div className="space-y-6">
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Global Phishing</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(radarData.phishing.total)}</div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              {getTrendIcon(radarData.phishing.trend)}
              <span className={getTrendColor(radarData.phishing.trend)}>
                {Math.abs(radarData.phishing.trend)}% this week
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Spoofing Attempts</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(radarData.spoofing.total)}</div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              {getTrendIcon(radarData.spoofing.trend)}
              <span className={getTrendColor(radarData.spoofing.trend)}>
                {Math.abs(radarData.spoofing.trend)}% this week
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">DMARC Adoption</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{radarData.dmarc.adoptionRate}%</div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              {getTrendIcon(radarData.dmarc.trend)}
              <span className={getTrendColor(radarData.dmarc.trend)}>
                {Math.abs(radarData.dmarc.trend)}% this week
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">DMARC Compliance</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{radarData.dmarc.compliance}%</div>
            <div className="text-xs text-muted-foreground">
              Strict policy enforcement
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Threats and Industry Risks */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Top Phishing Targets</CardTitle>
            <CardDescription>Most targeted industries this week</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {radarData.phishing.topTargets.map((target, index) => (
                <div key={target} className="flex items-center justify-between">
                  <span className="text-sm font-medium">{target}</span>
                  <Badge variant={index === 0 ? "destructive" : index === 1 ? "secondary" : "outline"}>
                    #{index + 1}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Industry Risk Levels</CardTitle>
            <CardDescription>Risk scoring by industry sector</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(radarData.industryRisks)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 6)
                .map(([industry, risk]) => (
                  <div key={industry} className="flex items-center justify-between">
                    <span className="text-sm font-medium">{industry}</span>
                    <div className="flex items-center gap-2">
                      <div className="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div 
                          className={`h-full rounded-full ${
                            risk >= 8 ? 'bg-red-500' : 
                            risk >= 6 ? 'bg-orange-500' : 
                            'bg-yellow-500'
                          }`}
                          style={{ width: `${(risk / 10) * 100}%` }}
                        />
                      </div>
                      <span className="text-sm text-muted-foreground w-8">
                        {risk}/10
                      </span>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Spoofing Methods */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Common Spoofing Methods</CardTitle>
          <CardDescription>Most prevalent spoofing techniques detected globally</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {radarData.spoofing.topMethods.map((method, index) => (
              <div key={method} className="text-center p-3 border rounded-lg">
                <div className="text-sm font-medium">{method}</div>
                <Badge variant="outline" className="mt-1">
                  Top {index + 1}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Data timestamp */}
      <div className="text-xs text-muted-foreground text-center">
        Last updated: {new Date(radarData.lastUpdated).toLocaleString()}
      </div>
    </div>
  );
};
