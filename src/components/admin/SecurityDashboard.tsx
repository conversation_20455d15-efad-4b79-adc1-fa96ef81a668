
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { securityMonitor } from '@/lib/security-monitoring';
import { complianceManager } from '@/lib/compliance';
import { 
  Shield, 
  AlertTriangle, 
  Activity, 
  Clock, 
  Users, 
  FileText,
  TrendingUp,
  Database
} from 'lucide-react';

const SecurityDashboard = () => {
  const [metrics, setMetrics] = useState<any>(null);
  const [threats, setThreats] = useState<any[]>([]);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  const loadMetrics = () => {
    const securityMetrics = securityMonitor.getSecurityMetrics();
    const privacyNotice = complianceManager.generatePrivacyNotice();
    
    setMetrics(securityMetrics);
    setThreats(securityMetrics.threatAnalysis);
  };

  useEffect(() => {
    loadMetrics();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadMetrics, 30000);
    setRefreshInterval(interval);

    return () => {
      if (interval) clearInterval(interval);
    };
  }, []);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500 text-white';
      case 'high': return 'bg-orange-500 text-white';
      case 'medium': return 'bg-yellow-500 text-black';
      case 'low': return 'bg-blue-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  if (!metrics) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center gap-2 text-white/70">
          <Activity className="h-4 w-4 animate-pulse" />
          <span>Loading security metrics...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Security Dashboard</h2>
        <Button onClick={loadMetrics} variant="outline" size="sm">
          <Activity className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Security Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="cyber-gradient-card border border-green-muted/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Events (1h)</p>
                <p className="text-2xl font-bold text-white">{metrics.eventsLastHour}</p>
              </div>
              <Clock className="h-8 w-8 text-green-bright" />
            </div>
          </CardContent>
        </Card>

        <Card className="cyber-gradient-card border border-green-muted/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Events (24h)</p>
                <p className="text-2xl font-bold text-white">{metrics.eventsLastDay}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="cyber-gradient-card border border-red-muted/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Critical Events</p>
                <p className="text-2xl font-bold text-red-400">{metrics.criticalEvents}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="cyber-gradient-card border border-orange-muted/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">High Severity</p>
                <p className="text-2xl font-bold text-orange-400">{metrics.highSeverityEvents}</p>
              </div>
              <Shield className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Threat Analysis */}
      {threats.length > 0 && (
        <Card className="cyber-gradient-card border border-red-muted/30">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-white">
              <AlertTriangle className="h-5 w-5 text-red-400" />
              Active Threats
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {threats.map((threat, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-red-900/20 rounded-lg border border-red-500/30">
                  <div>
                    <div className="flex items-center gap-2">
                      <Badge className={getSeverityColor(threat.severity)}>
                        {threat.severity.toUpperCase()}
                      </Badge>
                      <span className="text-white font-medium">{threat.type.replace('_', ' ')}</span>
                    </div>
                    <p className="text-white/70 text-sm mt-1">{threat.details}</p>
                  </div>
                  <div className="text-red-400 font-bold text-lg">{threat.count}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Most Common Event Type */}
      <Card className="cyber-gradient-card border border-green-muted/30">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Activity className="h-5 w-5 text-green-bright" />
            Event Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 bg-black-soft rounded-lg border border-green-muted/30">
              <h4 className="text-white font-medium mb-2">Most Common Event</h4>
              <p className="text-green-bright text-lg">
                {metrics.mostCommonEventType.replace('_', ' ') || 'No events'}
              </p>
            </div>
            <div className="p-4 bg-black-soft rounded-lg border border-green-muted/30">
              <h4 className="text-white font-medium mb-2">Security Status</h4>
              <Badge className={
                metrics.criticalEvents > 0 ? 'bg-red-500 text-white' :
                metrics.highSeverityEvents > 5 ? 'bg-orange-500 text-white' :
                'bg-green-500 text-white'
              }>
                {metrics.criticalEvents > 0 ? 'CRITICAL' :
                 metrics.highSeverityEvents > 5 ? 'ELEVATED' : 'NORMAL'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Compliance Status */}
      <Card className="cyber-gradient-card border border-blue-muted/30">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <FileText className="h-5 w-5 text-blue-400" />
            Compliance Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-black-soft rounded-lg border border-blue-muted/30">
              <h4 className="text-white font-medium mb-2">GDPR Compliance</h4>
              <Badge className="bg-green-500 text-white">Active</Badge>
              <p className="text-white/70 text-sm mt-2">
                Privacy controls implemented
              </p>
            </div>
            <div className="p-4 bg-black-soft rounded-lg border border-blue-muted/30">
              <h4 className="text-white font-medium mb-2">Data Retention</h4>
              <Badge className="bg-green-500 text-white">Compliant</Badge>
              <p className="text-white/70 text-sm mt-2">
                Policies active and enforced
              </p>
            </div>
            <div className="p-4 bg-black-soft rounded-lg border border-blue-muted/30">
              <h4 className="text-white font-medium mb-2">Audit Trail</h4>
              <Badge className="bg-green-500 text-white">Enabled</Badge>
              <p className="text-white/70 text-sm mt-2">
                All activities logged
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Security Recommendations */}
      <Card className="cyber-gradient-card border border-yellow-muted/30">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Shield className="h-5 w-5 text-yellow-400" />
            Security Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="p-3 bg-yellow-900/20 rounded-lg border border-yellow-500/30">
              <p className="text-yellow-200 font-medium">Regular Security Reviews</p>
              <p className="text-yellow-200/70 text-sm">
                Schedule weekly security reviews to analyze trends and threats
              </p>
            </div>
            <div className="p-3 bg-blue-900/20 rounded-lg border border-blue-500/30">
              <p className="text-blue-200 font-medium">Enhanced Monitoring</p>
              <p className="text-blue-200/70 text-sm">
                Consider implementing real-time alerting for critical events
              </p>
            </div>
            <div className="p-3 bg-green-900/20 rounded-lg border border-green-500/30">
              <p className="text-green-200 font-medium">Backup Verification</p>
              <p className="text-green-200/70 text-sm">
                Regularly test data backup and recovery procedures
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SecurityDashboard;
