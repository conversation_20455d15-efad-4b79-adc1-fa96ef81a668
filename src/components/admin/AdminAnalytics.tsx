
import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { TrendingUp, Users, Target, AlertTriangle } from "lucide-react";

interface AnalyticsData {
  totalSubmissions: number;
  completedAssessments: number;
  highRiskLeads: number;
  conversionRate: number;
  industryBreakdown: { industry: string; count: number }[];
  riskDistribution: { risk_level: string; count: number }[];
  averageRiskScore: number;
  leadsByPriority: { priority: number; count: number }[];
  recentSubmissions: number;
  completionTrend: number;
}

const AdminAnalytics = () => {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const fetchAnalytics = async () => {
    try {
      // Get total submissions
      const { count: totalSubmissions, error: submissionsError } = await supabase
        .from('assessment_submissions')
        .select('*', { count: 'exact', head: true });

      if (submissionsError) {
        console.error('Error fetching submissions:', submissionsError);
        throw submissionsError;
      }

      // Get completed assessments
      const { count: completedAssessments, error: completedError } = await supabase
        .from('assessment_submissions')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'completed');

      if (completedError) {
        console.error('Error fetching completed assessments:', completedError);
        throw completedError;
      }

      // Get high risk leads with error handling
      const { count: highRiskLeads, error: riskError } = await supabase
        .from('lead_scores')
        .select('*', { count: 'exact', head: true })
        .eq('risk_level', 'HIGH');

      if (riskError) {
        console.warn('Lead scores table may not exist:', riskError.message);
      }

      // Get recent submissions (last 7 days)
      const { count: recentSubmissions, error: recentError } = await supabase
        .from('assessment_submissions')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());

      if (recentError) {
        console.error('Error fetching recent submissions:', recentError);
        throw recentError;
      }

      // Get industry breakdown
      const { data: industryData } = await supabase
        .from('assessment_submissions')
        .select('industry')
        .eq('status', 'completed');

      // Get risk distribution and average risk score
      const { data: riskData } = await supabase
        .from('lead_scores')
        .select('risk_level, risk_percentage, lead_priority');

      // Get completion trend (compare last 7 days vs previous 7 days)
      const { count: previousWeekSubmissions } = await supabase
        .from('assessment_submissions')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString())
        .lt('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());

      const industryBreakdown = industryData?.reduce((acc: { [key: string]: number }, item) => {
        acc[item.industry] = (acc[item.industry] || 0) + 1;
        return acc;
      }, {}) || {};

      const riskDistribution = riskData?.reduce((acc: { [key: string]: number }, item) => {
        acc[item.risk_level] = (acc[item.risk_level] || 0) + 1;
        return acc;
      }, {}) || {};

      const leadsByPriority = riskData?.reduce((acc: { [key: string]: number }, item) => {
        acc[item.lead_priority] = (acc[item.lead_priority] || 0) + 1;
        return acc;
      }, {}) || {};

      const averageRiskScore = riskData?.length > 0
        ? riskData.reduce((sum, item) => sum + (item.risk_percentage || 0), 0) / riskData.length
        : 0;

      const completionTrend = previousWeekSubmissions > 0
        ? Math.round(((recentSubmissions || 0) - previousWeekSubmissions) / previousWeekSubmissions * 100)
        : 0;

      setAnalytics({
        totalSubmissions: totalSubmissions || 0,
        completedAssessments: completedAssessments || 0,
        highRiskLeads: highRiskLeads || 0,
        conversionRate: totalSubmissions ? Math.round((completedAssessments || 0) / totalSubmissions * 100) : 0,
        industryBreakdown: Object.entries(industryBreakdown).map(([industry, count]) => ({ industry, count })),
        riskDistribution: Object.entries(riskDistribution).map(([risk_level, count]) => ({ risk_level, count })),
        averageRiskScore: Math.round(averageRiskScore * 100) / 100,
        leadsByPriority: Object.entries(leadsByPriority).map(([priority, count]) => ({ priority: parseInt(priority), count })),
        recentSubmissions: recentSubmissions || 0,
        completionTrend,
      });
    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card className="cyber-gradient-card border border-green-muted/30">
        <CardContent className="p-8 text-center">
          <div className="text-white">Loading analytics...</div>
        </CardContent>
      </Card>
    );
  }

  if (!analytics) {
    return (
      <Card className="cyber-gradient-card border border-green-muted/30">
        <CardContent className="p-8 text-center">
          <div className="text-red-400">Failed to load analytics</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <Card className="cyber-gradient-card border border-green-muted/30">
        <CardHeader>
          <CardTitle className="text-white">Key Metrics</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-3 p-3 bg-green-dark/20 rounded-lg">
            <Users className="h-8 w-8 text-green-bright" />
            <div>
              <div className="text-2xl font-bold text-white">{analytics.totalSubmissions}</div>
              <div className="text-white/60 text-sm">Total Submissions</div>
            </div>
          </div>

          <div className="flex items-center gap-3 p-3 bg-blue-500/20 rounded-lg">
            <Target className="h-8 w-8 text-blue-400" />
            <div>
              <div className="text-2xl font-bold text-white">{analytics.completedAssessments}</div>
              <div className="text-white/60 text-sm">Completed Assessments</div>
            </div>
          </div>

          <div className="flex items-center gap-3 p-3 bg-red-500/20 rounded-lg">
            <AlertTriangle className="h-8 w-8 text-red-400" />
            <div>
              <div className="text-2xl font-bold text-white">{analytics.highRiskLeads}</div>
              <div className="text-white/60 text-sm">High Risk Leads</div>
            </div>
          </div>

          <div className="flex items-center gap-3 p-3 bg-yellow-500/20 rounded-lg">
            <TrendingUp className="h-8 w-8 text-yellow-400" />
            <div>
              <div className="text-2xl font-bold text-white">{analytics.conversionRate}%</div>
              <div className="text-white/60 text-sm">Completion Rate</div>
            </div>
          </div>

          <div className="flex items-center gap-3 p-3 bg-purple-500/20 rounded-lg">
            <Target className="h-8 w-8 text-purple-400" />
            <div>
              <div className="text-2xl font-bold text-white">{analytics.averageRiskScore}%</div>
              <div className="text-white/60 text-sm">Average Risk Score</div>
            </div>
          </div>

          <div className="flex items-center gap-3 p-3 bg-cyan-500/20 rounded-lg">
            <Users className="h-8 w-8 text-cyan-400" />
            <div>
              <div className="text-2xl font-bold text-white">{analytics.recentSubmissions}</div>
              <div className="text-white/60 text-sm">Recent Submissions (7d)</div>
            </div>
          </div>

          <div className="flex items-center gap-3 p-3 bg-orange-500/20 rounded-lg">
            <TrendingUp className={`h-8 w-8 ${analytics.completionTrend >= 0 ? 'text-green-400' : 'text-red-400'}`} />
            <div>
              <div className={`text-2xl font-bold ${analytics.completionTrend >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {analytics.completionTrend >= 0 ? '+' : ''}{analytics.completionTrend}%
              </div>
              <div className="text-white/60 text-sm">Weekly Trend</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Industry Breakdown */}
      <Card className="cyber-gradient-card border border-green-muted/30">
        <CardHeader>
          <CardTitle className="text-white">Industry Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.industryBreakdown.map(({ industry, count }) => (
              <div key={industry} className="flex justify-between items-center">
                <span className="text-white capitalize">{industry}</span>
                <span className="text-green-bright font-semibold">{count}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Risk Distribution */}
      <Card className="cyber-gradient-card border border-green-muted/30">
        <CardHeader>
          <CardTitle className="text-white">Risk Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.riskDistribution.map(({ risk_level, count }) => (
              <div key={risk_level} className="flex justify-between items-center">
                <span className={`font-medium ${
                  risk_level === 'HIGH' ? 'text-red-400' :
                  risk_level === 'MEDIUM' ? 'text-yellow-400' :
                  'text-green-400'
                }`}>
                  {risk_level} Risk
                </span>
                <span className="text-white font-semibold">{count}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Lead Priority Breakdown */}
      <Card className="cyber-gradient-card border border-green-muted/30">
        <CardHeader>
          <CardTitle className="text-white">Lead Priority</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.leadsByPriority
              .sort((a, b) => b.priority - a.priority)
              .map(({ priority, count }) => (
              <div key={priority} className="flex justify-between items-center">
                <span className={`font-medium ${
                  priority >= 4 ? 'text-red-400' :
                  priority >= 3 ? 'text-orange-400' :
                  priority >= 2 ? 'text-yellow-400' :
                  'text-green-400'
                }`}>
                  Priority {priority} {priority >= 4 ? '(Urgent)' : priority >= 3 ? '(High)' : priority >= 2 ? '(Medium)' : '(Low)'}
                </span>
                <span className="text-white font-semibold">{count}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminAnalytics;
