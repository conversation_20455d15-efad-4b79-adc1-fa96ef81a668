
import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";
import { format } from "date-fns";
import { Search, Mail, Phone, Building, Users, AlertTriangle, TrendingUp } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Lead {
  id: string;
  company_name: string;
  contact_name: string;
  email: string;
  phone?: string;
  industry: string;
  employee_count: string;
  status: string;
  created_at: string;
  completed_at?: string;
  assessment_type_id?: string;
  lead_scores?: {
    risk_level: string;
    risk_percentage: number;
    lead_priority: number;
    follow_up_urgency: string;
    total_risk_score: number;
    max_possible_score: number;
  };
  assessment_types?: {
    title: string;
    name: string;
  };
}

const AdminLeadsList = () => {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [riskFilter, setRiskFilter] = useState<string>("all");
  const { toast } = useToast();

  useEffect(() => {
    fetchLeads();
  }, []);

  const fetchLeads = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase
        .from('assessment_submissions')
        .select(`
          id,
          company_name,
          contact_name,
          email,
          phone,
          industry,
          employee_count,
          status,
          created_at,
          completed_at,
          assessment_type_id,
          lead_scores (
            risk_level,
            risk_percentage,
            lead_priority,
            follow_up_urgency,
            total_risk_score,
            max_possible_score
          ),
          assessment_types (
            title,
            name
          )
        `)
        .order('created_at', { ascending: false });

      if (fetchError) {
        console.error('Error fetching leads:', fetchError);
        setError(`Failed to load leads: ${fetchError.message}`);
        toast({
          title: "Error",
          description: "Failed to load leads. Please try again.",
          variant: "destructive",
        });
        return;
      }

      setLeads(data || []);
    } catch (err) {
      console.error('Unexpected error:', err);
      setError('An unexpected error occurred while loading leads.');
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredLeads = leads.filter(lead => {
    const matchesSearch = 
      lead.company_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.contact_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.industry.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === "all" || lead.status === statusFilter;
    
    const matchesRisk = riskFilter === "all" || 
      (lead.lead_scores?.risk_level?.toLowerCase() === riskFilter.toLowerCase());

    return matchesSearch && matchesStatus && matchesRisk;
  });

  const getRiskBadgeColor = (riskLevel?: string) => {
    switch (riskLevel?.toLowerCase()) {
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'outline';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'default';
      case 'in_progress': return 'secondary';
      case 'started': return 'outline';
      default: return 'outline';
    }
  };

  const getPriorityLabel = (priority?: number) => {
    if (!priority) return 'Pending';
    if (priority <= 3) return 'High';
    if (priority <= 6) return 'Medium';
    return 'Low';
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Loading Leads...
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Error Loading Leads
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchLeads} variant="outline">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Lead Management ({filteredLeads.length})
        </CardTitle>
        
        <div className="flex flex-col sm:flex-row gap-4 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search leads..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-40">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="started">Started</SelectItem>
            </SelectContent>
          </Select>

          <Select value={riskFilter} onValueChange={setRiskFilter}>
            <SelectTrigger className="w-full sm:w-40">
              <SelectValue placeholder="Risk Level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Risk</SelectItem>
              <SelectItem value="high">High Risk</SelectItem>
              <SelectItem value="medium">Medium Risk</SelectItem>
              <SelectItem value="low">Low Risk</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      
      <CardContent>
        {filteredLeads.length === 0 ? (
          <div className="text-center py-12">
            <Users className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold mb-2">No leads found</h3>
            <p className="text-gray-600">
              {searchTerm || statusFilter !== "all" || riskFilter !== "all" 
                ? "Try adjusting your filters" 
                : "Leads will appear here once assessments are completed"}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredLeads.map((lead) => (
              <div key={lead.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-3 flex-wrap">
                      <h3 className="font-semibold text-lg">{lead.company_name}</h3>
                      <Badge variant={getStatusBadgeColor(lead.status)}>
                        {lead.status.replace('_', ' ')}
                      </Badge>
                      {lead.lead_scores?.risk_level && (
                        <Badge variant={getRiskBadgeColor(lead.lead_scores.risk_level)}>
                          {lead.lead_scores.risk_level} Risk
                        </Badge>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        {lead.contact_name} - {lead.email}
                      </div>
                      {lead.phone && (
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4" />
                          {lead.phone}
                        </div>
                      )}
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4" />
                        {lead.industry}
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        {lead.employee_count} employees
                      </div>
                    </div>

                    {lead.assessment_types && (
                      <div className="text-sm text-gray-600">
                        Assessment: {lead.assessment_types.title}
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col items-end gap-2">
                    <div className="text-sm text-gray-500">
                      {format(new Date(lead.created_at), 'MMM dd, yyyy')}
                    </div>
                    
                    {lead.lead_scores && (
                      <div className="text-right space-y-1">
                        <div className="flex items-center gap-2">
                          <TrendingUp className="h-4 w-4" />
                          <span className="text-sm font-medium">
                            {lead.lead_scores.risk_percentage}% Risk
                          </span>
                        </div>
                        <div className="text-xs text-gray-600">
                          Priority: {getPriorityLabel(lead.lead_scores.lead_priority)}
                        </div>
                        {lead.lead_scores.total_risk_score && lead.lead_scores.max_possible_score && (
                          <div className="text-xs text-gray-600">
                            Score: {lead.lead_scores.total_risk_score}/{lead.lead_scores.max_possible_score}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AdminLeadsList;
