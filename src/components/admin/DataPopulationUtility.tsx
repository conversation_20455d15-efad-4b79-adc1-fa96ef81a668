
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Database } from "@/integrations/supabase/types";

type AssessmentType = Database['public']['Tables']['assessment_types']['Row'];

const DataPopulationUtility = () => {
  const [isPopulating, setIsPopulating] = useState(false);
  const { toast } = useToast();

  const createSampleAssessmentType = async (): Promise<string> => {
    const { data, error } = await supabase
      .from('assessment_types')
      .insert({
        name: 'Cybersecurity Risk Assessment',
        title: 'Comprehensive Cybersecurity Risk Assessment',
        description: 'Evaluate your organization\'s cybersecurity posture and identify potential vulnerabilities.',
        category: 'Security',
        slug: 'cybersecurity-risk-assessment',
        estimated_time_minutes: 15,
        is_active: true,
        order_index: 1
      })
      .select()
      .single();

    if (error) throw error;
    return data.id;
  };

  const createSampleQuestions = async (assessmentTypeId: string) => {
    const questions = [
      {
        assessment_type_id: assessmentTypeId,
        question_text: "Does your organization have a formal cybersecurity policy?",
        category: "Policy & Governance",
        order_index: 1,
        is_active: true
      },
      {
        assessment_type_id: assessmentTypeId,
        question_text: "Do you use multi-factor authentication for critical systems?",
        category: "Access Control",
        order_index: 2,
        is_active: true
      },
      {
        assessment_type_id: assessmentTypeId,
        question_text: "How frequently do you backup your critical data?",
        category: "Data Protection",
        order_index: 3,
        is_active: true
      },
      {
        assessment_type_id: assessmentTypeId,
        question_text: "Do you conduct regular security awareness training?",
        category: "Training & Awareness",
        order_index: 4,
        is_active: true
      },
      {
        assessment_type_id: assessmentTypeId,
        question_text: "Do you have an incident response plan?",
        category: "Incident Management",
        order_index: 5,
        is_active: true
      }
    ];

    // Get next available IDs for questions
    const { data: maxIdData } = await supabase
      .from('assessment_questions')
      .select('id')
      .order('id', { ascending: false })
      .limit(1);
    
    let nextId = maxIdData && maxIdData.length > 0 ? maxIdData[0].id + 1 : 1;
    
    const questionsWithIds = questions.map((q, index) => ({
      ...q,
      id: nextId + index
    }));

    const { data, error } = await supabase
      .from('assessment_questions')
      .insert(questionsWithIds)
      .select();

    if (error) throw error;
    return data;
  };

  const createSampleOptions = async (questions: any[]) => {
    const allOptions: any[] = [];

    questions.forEach((question, qIndex) => {
      let options: any[] = [];
      
      switch (qIndex) {
        case 0: // Cybersecurity policy
          options = [
            { option_text: "Yes, comprehensive and regularly updated", risk_score: 1, order_index: 1 },
            { option_text: "Yes, but needs updating", risk_score: 3, order_index: 2 },
            { option_text: "Basic policy exists", risk_score: 5, order_index: 3 },
            { option_text: "No formal policy", risk_score: 8, order_index: 4 }
          ];
          break;
        case 1: // Multi-factor authentication
          options = [
            { option_text: "Yes, for all critical systems", risk_score: 1, order_index: 1 },
            { option_text: "Yes, for some systems", risk_score: 4, order_index: 2 },
            { option_text: "Planning to implement", risk_score: 6, order_index: 3 },
            { option_text: "No MFA implemented", risk_score: 9, order_index: 4 }
          ];
          break;
        case 2: // Data backup
          options = [
            { option_text: "Daily automated backups", risk_score: 1, order_index: 1 },
            { option_text: "Weekly backups", risk_score: 3, order_index: 2 },
            { option_text: "Monthly backups", risk_score: 5, order_index: 3 },
            { option_text: "No regular backups", risk_score: 8, order_index: 4 }
          ];
          break;
        case 3: // Security training
          options = [
            { option_text: "Quarterly training sessions", risk_score: 1, order_index: 1 },
            { option_text: "Annual training", risk_score: 3, order_index: 2 },
            { option_text: "Ad-hoc training", risk_score: 5, order_index: 3 },
            { option_text: "No formal training", risk_score: 7, order_index: 4 }
          ];
          break;
        case 4: // Incident response
          options = [
            { option_text: "Comprehensive tested plan", risk_score: 1, order_index: 1 },
            { option_text: "Plan exists, not tested", risk_score: 4, order_index: 2 },
            { option_text: "Basic plan in development", risk_score: 6, order_index: 3 },
            { option_text: "No incident response plan", risk_score: 8, order_index: 4 }
          ];
          break;
      }

      options.forEach(option => {
        allOptions.push({
          ...option,
          question_id: question.id
        });
      });
    });

    const { data, error } = await supabase
      .from('assessment_question_options')
      .insert(allOptions);

    if (error) throw error;
    return data;
  };

  const createSampleSubmissions = async (assessmentTypeId: string) => {
    const submissions = [
      {
        assessment_type_id: assessmentTypeId,
        company_name: "TechCorp Ltd",
        industry: "Technology",
        employee_count: "50-100",
        contact_name: "John Smith",
        email: "<EMAIL>",
        phone: "+64 21 123 4567",
        status: "completed" as const
      },
      {
        assessment_type_id: assessmentTypeId,
        company_name: "HealthPlus Medical",
        industry: "Healthcare",
        employee_count: "100-500",
        contact_name: "Sarah Johnson",
        email: "<EMAIL>",
        phone: "+64 21 987 6543",
        status: "completed" as const
      },
      {
        assessment_type_id: assessmentTypeId,
        company_name: "RetailMax",
        industry: "Retail",
        employee_count: "10-50",
        contact_name: "Mike Brown",
        email: "<EMAIL>",
        phone: "+64 21 555 0123",
        status: "in_progress" as const
      }
    ];

    const { data, error } = await supabase
      .from('assessment_submissions')
      .insert(submissions)
      .select();

    if (error) throw error;
    return data;
  };

  const createSampleLeadScores = async (submissions: any[]) => {
    const leadScores = submissions.map((submission, index) => {
      const riskLevels = ['HIGH', 'MEDIUM', 'LOW'] as const;
      const riskLevel = riskLevels[index % 3];
      
      return {
        submission_id: submission.id,
        risk_level: riskLevel,
        risk_percentage: riskLevel === 'HIGH' ? 85 : riskLevel === 'MEDIUM' ? 55 : 25,
        lead_priority: riskLevel === 'HIGH' ? 1 : riskLevel === 'MEDIUM' ? 3 : 5,
        total_risk_score: riskLevel === 'HIGH' ? 34 : riskLevel === 'MEDIUM' ? 22 : 10,
        max_possible_score: 40,
        follow_up_urgency: riskLevel === 'HIGH' ? 'Immediate' : riskLevel === 'MEDIUM' ? 'Within 24 hours' : 'Within 1 week'
      };
    });

    const { data, error } = await supabase
      .from('lead_scores')
      .insert(leadScores);

    if (error) throw error;
    return data;
  };

  const populateData = async () => {
    setIsPopulating(true);
    try {
      // Check if data already exists
      const { data: existingTypes } = await supabase
        .from('assessment_types')
        .select('*')
        .limit(1);

      if (existingTypes && existingTypes.length > 0) {
        toast({
          title: "Data Already Exists",
          description: "Sample data has already been populated.",
          variant: "default",
        });
        return;
      }

      // Create assessment type
      const assessmentTypeId = await createSampleAssessmentType();
      
      // Create questions
      const questions = await createSampleQuestions(assessmentTypeId);
      
      // Create options
      await createSampleOptions(questions);
      
      // Create submissions
      const submissions = await createSampleSubmissions(assessmentTypeId);
      
      // Create lead scores
      await createSampleLeadScores(submissions);

      toast({
        title: "Success",
        description: "Sample data has been populated successfully!",
        variant: "default",
      });
    } catch (error) {
      console.error('Error populating data:', error);
      toast({
        title: "Error",
        description: "Failed to populate sample data. Check console for details.",
        variant: "destructive",
      });
    } finally {
      setIsPopulating(false);
    }
  };

  const clearData = async () => {
    setIsPopulating(true);
    try {
      // Clear in reverse order of dependencies
      await supabase.from('lead_scores').delete().neq('id', '00000000-0000-0000-0000-000000000000');
      await supabase.from('assessment_question_options').delete().neq('id', '00000000-0000-0000-0000-000000000000');
      await supabase.from('assessment_questions').delete().neq('id', 0);
      await supabase.from('assessment_submissions').delete().neq('id', '00000000-0000-0000-0000-000000000000');
      await supabase.from('assessment_types').delete().neq('id', '00000000-0000-0000-0000-000000000000');

      toast({
        title: "Success",
        description: "All sample data has been cleared.",
        variant: "default",
      });
    } catch (error) {
      console.error('Error clearing data:', error);
      toast({
        title: "Error",
        description: "Failed to clear sample data. Check console for details.",
        variant: "destructive",
      });
    } finally {
      setIsPopulating(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Data Population Utility</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground">
          This utility helps you populate the database with sample data for testing purposes.
        </p>
        
        <div className="flex gap-4">
          <Button 
            onClick={populateData} 
            disabled={isPopulating}
            variant="default"
          >
            {isPopulating ? "Populating..." : "Populate Sample Data"}
          </Button>
          
          <Button 
            onClick={clearData} 
            disabled={isPopulating}
            variant="destructive"
          >
            {isPopulating ? "Clearing..." : "Clear All Data"}
          </Button>
        </div>

        <div className="text-sm text-muted-foreground">
          <strong>What this creates:</strong>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>1 Assessment Type (Cybersecurity Risk Assessment)</li>
            <li>5 Sample Questions with multiple choice options</li>
            <li>3 Sample Company Submissions</li>
            <li>Lead Scores for each submission</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default DataPopulationUtility;
