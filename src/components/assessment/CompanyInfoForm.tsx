
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Building2, Mail, AlertCircle } from "lucide-react";

interface CompanyInfo {
  companyName: string;
  industry: string;
  employeeCount: string;
  contactName: string;
  email: string;
  phone: string;
}

interface Props {
  companyInfo: CompanyInfo;
  onUpdateCompanyInfo: (field: keyof CompanyInfo, value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  isSubmitting: boolean;
  currentStep: string;
}

const CompanyInfoForm = ({ companyInfo, onUpdateCompanyInfo, onSubmit, isSubmitting, currentStep }: Props) => {
  return (
    <div className="max-w-2xl mx-auto">
      <Card className="cyber-gradient-card border border-green-muted/30">
        <CardContent className="p-8">
          <div className="text-center mb-8">
            <Building2 className="h-12 w-12 text-green-bright mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-2">Get Started with Your Security Check</h2>
            <p className="text-white/70 mb-4">
              Just 3 quick details to provide personalized security recommendations for your business.
            </p>

            {/* Value proposition */}
            <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4 mb-6">
              <h3 className="text-blue-200 font-medium mb-2">Why we need this information:</h3>
              <div className="grid md:grid-cols-3 gap-3 text-sm">
                <div className="flex items-center gap-2 text-blue-100/80">
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  Personalized security recommendations
                </div>
                <div className="flex items-center gap-2 text-blue-100/80">
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  Industry-specific risk assessment
                </div>
                <div className="flex items-center gap-2 text-blue-100/80">
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  Compliance requirements for your business
                </div>
              </div>
            </div>
          </div>

          {/* Debug info for development */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mb-6 p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg">
              <div className="flex items-center gap-2 text-blue-400 text-sm">
                <AlertCircle className="h-4 w-4" />
                <span>Debug Info: Company form ready, step = {currentStep}</span>
              </div>
            </div>
          )}

          <form onSubmit={onSubmit} className="space-y-6">
            {/* Essential Fields - Required */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-white border-b border-green-muted/30 pb-2">
                Essential Information
              </h3>

              <div>
                <Label htmlFor="companyName" className="text-white mb-2 block">
                  Company Name *
                </Label>
                <Input
                  id="companyName"
                  type="text"
                  required
                  value={companyInfo.companyName}
                  onChange={(e) => onUpdateCompanyInfo('companyName', e.target.value)}
                  className="bg-black-soft border-green-muted/30 text-white min-h-[44px]"
                  placeholder="Your company name"
                />
              </div>

              <div>
                <Label htmlFor="industry" className="text-white mb-2 block">
                  Industry *
                </Label>
                <select
                  id="industry"
                  required
                  value={companyInfo.industry}
                  onChange={(e) => onUpdateCompanyInfo('industry', e.target.value)}
                  className="w-full p-3 bg-black-soft border border-green-muted/30 rounded-md text-white min-h-[44px]"
                  title="Select your industry"
                >
                  <option value="">Select your industry</option>
                  <option value="healthcare">Healthcare</option>
                  <option value="finance">Financial Services</option>
                  <option value="education">Education</option>
                  <option value="government">Government</option>
                  <option value="manufacturing">Manufacturing</option>
                  <option value="retail">Retail</option>
                  <option value="technology">Technology</option>
                  <option value="professional-services">Professional Services</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <Label htmlFor="email" className="text-white mb-2 block">
                  Email Address for Results *
                </Label>
                <Input
                  id="email"
                  type="email"
                  required
                  value={companyInfo.email}
                  onChange={(e) => onUpdateCompanyInfo('email', e.target.value)}
                  className="bg-black-soft border-green-muted/30 text-white min-h-[44px]"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            {/* Optional Fields - Collapsible */}
            <div className="space-y-6 pt-6 border-t border-green-muted/20">
              <h3 className="text-lg font-semibold text-white/80">
                Additional Details (Optional)
              </h3>
              <p className="text-white/60 text-sm">
                These details help us provide more specific recommendations, but you can skip them for now.
              </p>

              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="employeeCount" className="text-white mb-2 block">
                    Team Size
                  </Label>
                  <select
                    id="employeeCount"
                    value={companyInfo.employeeCount}
                    onChange={(e) => onUpdateCompanyInfo('employeeCount', e.target.value)}
                    className="w-full p-3 bg-black-soft border border-green-muted/30 rounded-md text-white min-h-[44px]"
                    title="Select your team size"
                  >
                    <option value="">Select team size (optional)</option>
                    <option value="1-10">1-10 employees</option>
                    <option value="11-50">11-50 employees</option>
                    <option value="51-200">51-200 employees</option>
                    <option value="201-500">201-500 employees</option>
                    <option value="500+">500+ employees</option>
                  </select>
                </div>

                <div>
                  <Label htmlFor="contactName" className="text-white mb-2 block">
                    Your Name
                  </Label>
                  <Input
                    id="contactName"
                    type="text"
                    value={companyInfo.contactName}
                    onChange={(e) => onUpdateCompanyInfo('contactName', e.target.value)}
                    className="bg-black-soft border-green-muted/30 text-white min-h-[44px]"
                    placeholder="Your full name (optional)"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="phone" className="text-white mb-2 block">
                  Phone Number
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  value={companyInfo.phone}
                  onChange={(e) => onUpdateCompanyInfo('phone', e.target.value)}
                  className="bg-black-soft border-green-muted/30 text-white min-h-[44px]"
                  placeholder="Your phone number (optional)"
                />
              </div>
            </div>

            <div className="bg-green-dark/20 p-4 rounded-lg border border-green-muted/30">
              <div className="flex items-start gap-3">
                <Mail className="h-5 w-5 text-green-bright mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-white font-medium mb-1">Privacy Commitment</p>
                  <p className="text-white/70 text-sm">
                    Your information is secure and will only be used to provide your assessment results 
                    and relevant cybersecurity insights. We never share your data with third parties.
                  </p>
                </div>
              </div>
            </div>

            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="w-full bg-green-bright hover:bg-green-muted text-black font-semibold py-3"
            >
              {isSubmitting ? "Starting Assessment..." : "Start Security Assessment"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default CompanyInfoForm;
