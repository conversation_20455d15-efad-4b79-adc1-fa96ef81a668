import React, { useEffect, useState } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Shield, TrendingUp, <PERSON><PERSON><PERSON>riangle, CheckCircle, ExternalLink } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface AssessmentResultsProps {
  submissionId: string;
  onStartNew: () => void;
}

interface LeadScore {
  risk_level: 'LOW' | 'MEDIUM' | 'HIGH';
  risk_percentage: number;
  lead_priority: number;
  top_recommendations: string[];
  follow_up_urgency: string;
}

const AssessmentResults = ({ submissionId, onStartNew }: AssessmentResultsProps) => {
  const [leadScore, setLeadScore] = useState<LeadScore | null>(null);
  const [loading, setLoading] = useState(true);
  const [emailSent, setEmailSent] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchResults();
  }, [submissionId]);

  const fetchResults = async () => {
    try {
      console.log('Fetching results for submission:', submissionId);
      
      const { data: leadScoreData, error } = await supabase
        .from('lead_scores')
        .select('*')
        .eq('submission_id', submissionId)
        .maybeSingle();

      if (error) {
        console.error('Error fetching lead score:', error);
        throw error;
      }

      if (leadScoreData) {
        console.log('Lead score found:', leadScoreData);
        
        // Convert the database result to our LeadScore interface
        const convertedLeadScore: LeadScore = {
          risk_level: leadScoreData.risk_level,
          risk_percentage: leadScoreData.risk_percentage,
          lead_priority: leadScoreData.lead_priority,
          follow_up_urgency: leadScoreData.follow_up_urgency || '',
          top_recommendations: Array.isArray(leadScoreData.top_recommendations) 
            ? leadScoreData.top_recommendations as string[]
            : []
        };
        
        setLeadScore(convertedLeadScore);
        await sendEmailResults();
      } else {
        console.log('No lead score found, waiting for calculation...');
        // If no lead score yet, try again in a moment
        setTimeout(() => {
          fetchResults();
        }, 2000);
      }
    } catch (error) {
      console.error('Error fetching results:', error);
      toast({
        title: "Error",
        description: "Failed to load assessment results. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const sendEmailResults = async () => {
    if (emailSent) return;

    try {
      console.log('Sending email results for submission:', submissionId);
      
      const { error } = await supabase.functions.invoke('send-assessment-results', {
        body: { submissionId }
      });

      if (error) {
        console.error('Error sending email:', error);
        throw error;
      }

      setEmailSent(true);
      console.log('Email sent successfully');
    } catch (error) {
      console.error('Error sending email results:', error);
      // Don't show error to user for email sending failures
    }
  };

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'LOW': return 'text-green-400';
      case 'MEDIUM': return 'text-yellow-400';
      case 'HIGH': return 'text-red-400';
      default: return 'text-white';
    }
  };

  const getRiskIcon = (level: string) => {
    switch (level) {
      case 'LOW': return CheckCircle;
      case 'MEDIUM': return AlertTriangle;
      case 'HIGH': return Shield;
      default: return Shield;
    }
  };

  const getControlStats = (riskPercentage: number) => {
    // Calculate dynamic stats based on actual risk level
    const baseControls = Math.max(5, Math.floor((100 - riskPercentage) / 10));
    const missingControls = Math.floor(riskPercentage / 15) + 2;
    const criticalGaps = riskPercentage > 70 ? Math.floor(riskPercentage / 25) : 0;

    return {
      implementedControls: baseControls,
      missingControls,
      criticalGaps
    };
  };

  if (loading) {
    return (
      <Card className="cyber-gradient-card border border-green-muted/30">
        <CardContent className="p-8 text-center">
          <div className="w-16 h-16 border-4 border-t-green-bright border-r-green-muted/30 border-b-green-muted/30 border-l-green-muted/30 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white mb-2">Calculating your security risk assessment...</p>
          <p className="text-white/60 text-sm">This may take a few moments</p>
        </CardContent>
      </Card>
    );
  }

  if (!leadScore) {
    return (
      <Card className="cyber-gradient-card border border-red-500/30">
        <CardContent className="p-8 text-center">
          <AlertTriangle className="h-16 w-16 text-red-400 mx-auto mb-4" />
          <p className="text-white mb-4">Unable to calculate assessment results</p>
          <Button onClick={onStartNew} className="bg-green-bright hover:bg-green-muted text-black">
            Start New Assessment
          </Button>
        </CardContent>
      </Card>
    );
  }

  const RiskIcon = getRiskIcon(leadScore.risk_level);
  const controlStats = getControlStats(leadScore.risk_percentage);
  const riskReduction = Math.min(85, Math.round(leadScore.risk_percentage * 0.8));

  return (
    <div className="space-y-6">
      {/* Risk Score Card */}
      <Card className="cyber-gradient-card border border-green-muted/30">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center mb-4">
            <RiskIcon className={`h-16 w-16 ${getRiskColor(leadScore.risk_level)}`} />
          </div>
          <CardTitle className="text-2xl text-white">
            Assessment Complete
          </CardTitle>
          <div className="flex items-center justify-center gap-2 mt-2">
            <Badge 
              variant="outline" 
              className={`${getRiskColor(leadScore.risk_level)} border-current text-lg px-4 py-1`}
            >
              {leadScore.risk_level} RISK
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="text-center">
          <div className="text-4xl font-bold text-white mb-2">
            {leadScore.risk_percentage}%
          </div>
          <p className="text-white/70 mb-6">
            Overall Security Risk Score
          </p>
          
          {emailSent && (
            <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4 mb-6">
              <p className="text-green-400 text-sm">
                ✅ Detailed results have been sent to your email
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Risk Reduction Potential */}
      <Card className="cyber-gradient-card border border-green-muted/30">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-green-bright" />
            Risk Reduction Potential
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-bright mb-2">
              Up to {riskReduction}%
            </div>
            <p className="text-white/70 text-sm">
              Potential risk reduction with recommended security improvements
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Security Control Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="cyber-gradient-card border border-green-muted/30">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-bright mb-1">
              {controlStats.implementedControls}
            </div>
            <div className="text-white/70 text-sm">Controls in Place</div>
          </CardContent>
        </Card>
        
        <Card className="cyber-gradient-card border border-yellow-500/30">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-400 mb-1">
              {controlStats.missingControls}
            </div>
            <div className="text-white/70 text-sm">Areas for Improvement</div>
          </CardContent>
        </Card>
        
        <Card className="cyber-gradient-card border border-red-500/30">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-400 mb-1">
              {controlStats.criticalGaps}
            </div>
            <div className="text-white/70 text-sm">Critical Gaps</div>
          </CardContent>
        </Card>
      </div>

      {/* Top Recommendations */}
      {leadScore.top_recommendations && leadScore.top_recommendations.length > 0 && (
        <Card className="cyber-gradient-card border border-green-muted/30">
          <CardHeader>
            <CardTitle className="text-white">Priority Recommendations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {leadScore.top_recommendations.map((recommendation, index) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-green-dark/20 rounded-lg">
                  <div className="bg-green-bright text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mt-0.5">
                    {index + 1}
                  </div>
                  <p className="text-white text-sm">{recommendation}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Button 
          onClick={onStartNew}
          variant="outline"
          className="flex-1 border-green-muted text-green-bright hover:bg-green-dark/20"
        >
          Take Another Assessment
        </Button>
        <Button 
          onClick={() => window.open('/contact', '_blank')}
          className="flex-1 bg-green-bright hover:bg-green-muted text-black"
        >
          <ExternalLink className="w-4 h-4 mr-2" />
          Get Professional Help
        </Button>
      </div>
    </div>
  );
};

export default AssessmentResults;
