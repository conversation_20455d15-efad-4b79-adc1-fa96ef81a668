
import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { CheckCircle, AlertTriangle, Shield, AlertCircle, RefreshCw } from "lucide-react";
import { useDynamicAssessmentSubmission } from "@/hooks/use-dynamic-assessment-submission";
import { useToast } from "@/hooks/use-toast";
import { Database } from "@/integrations/supabase/types";

type AssessmentType = Database['public']['Tables']['assessment_types']['Row'];
type Question = Database['public']['Tables']['assessment_questions']['Row'];
type QuestionOption = Database['public']['Tables']['assessment_question_options']['Row'];

interface QuestionWithOptions extends Question {
  options: QuestionOption[];
}

interface Props {
  questions: QuestionWithOptions[];
  onComplete: (answers: number[]) => void;
  assessmentType: AssessmentType;
}

const AssessmentQuestions = ({ questions, onComplete, assessmentType }: Props) => {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);

  const { saveAnswer, calculateAndSaveScores } = useDynamicAssessmentSubmission(assessmentType.id);
  const { toast } = useToast();

  useEffect(() => {
    console.log('🎯 AssessmentQuestions component state:', {
      questionsCount: questions.length,
      currentQuestion,
      answersCount: answers.length,
      assessmentType: assessmentType.title
    });
  }, [questions.length, currentQuestion, answers.length, assessmentType.title]);

  const handleRetry = () => {
    console.log('🔄 User clicked retry, reloading page...');
    window.location.reload();
  };

  if (questions.length === 0) {
    console.error('❌ No questions available for assessment:', assessmentType.title);
    return (
      <Card className="cyber-gradient-card border border-red-500/30">
        <CardContent className="p-8 text-center">
          <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">
            Assessment Questions Not Available
          </h3>
          <p className="text-red-400 mb-4">
            The questions for "{assessmentType.title}" are not yet available.
          </p>
          <div className="space-y-2 text-white/70 text-sm mb-6">
            <p>This might be due to:</p>
            <ul className="list-disc list-inside space-y-1">
              <li>Assessment still being configured</li>
              <li>Temporary system issues</li>
              <li>Database connectivity problems</li>
            </ul>
          </div>
          <Button 
            onClick={handleRetry}
            className="bg-green-bright hover:bg-green-muted text-black font-semibold"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  const handleAnswer = async (optionId: string, risk: number) => {
    console.log('📝 User answered question', currentQuestion + 1, 'with option:', optionId, 'risk score:', risk);
    
    const newAnswers = [...answers, risk];
    const newSelectedOptions = [...selectedOptions, optionId];
    setAnswers(newAnswers);
    setSelectedOptions(newSelectedOptions);

    // Save answer to database
    try {
      console.log('💾 Saving answer to database...');
      await saveAnswer(questions[currentQuestion].id, risk, optionId);
      console.log('✅ Answer saved successfully');
    } catch (error) {
      console.error('❌ Failed to save answer:', error);
      toast({
        title: "Save Error",
        description: "Your answer was recorded but not saved. Continuing assessment...",
        variant: "destructive",
      });
    }

    if (currentQuestion < questions.length - 1) {
      console.log('➡️ Moving to next question:', currentQuestion + 2);
      setCurrentQuestion(prev => prev + 1);
    } else {
      console.log('🏁 Assessment complete! Calculating final scores...');
      
      // Calculate and save final scores
      try {
        console.log('📊 Calculating scores for answers:', newAnswers);
        await calculateAndSaveScores(newAnswers);
        console.log('✅ Scores calculated and saved successfully');
        onComplete(newAnswers);
      } catch (error) {
        console.error('❌ Failed to calculate scores:', error);
        console.log('⚠️ Completing assessment with local data only');
        // Still complete the assessment with local data
        onComplete(newAnswers);
      }
    }
  };

  const progress = ((currentQuestion + 1) / questions.length) * 100;
  const question = questions[currentQuestion];

  console.log('🎪 Rendering question', currentQuestion + 1, 'of', questions.length, ':', question?.question_text);

  return (
    <Card className="cyber-gradient-card border border-green-muted/30">
      <CardContent className="p-8">
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <span className="text-green-bright font-medium">
              Question {currentQuestion + 1} of {questions.length}
            </span>
            <span className="text-white/70">{question.category}</span>
          </div>
          <Progress value={progress} className="mb-6" />
        </div>

        <div className="mb-8">
          <h2 className="text-xl md:text-2xl font-bold mb-6 text-white leading-relaxed">
            {question.question_text}
          </h2>

          <div className="space-y-4">
            {question.options.map((option) => (
              <Button
                key={option.id}
                variant="outline"
                onClick={() => handleAnswer(option.id, option.risk_score)}
                className="w-full text-left p-4 h-auto border-green-muted/30 hover:border-green-bright/50 hover:bg-green-dark/20 transition-all duration-200"
              >
                <div className="flex items-start gap-3">
                  <div className={`p-1 rounded-full flex-shrink-0 mt-1 ${
                    option.risk_score <= 2 ? 'bg-green-dark/40' : 
                    option.risk_score <= 3 ? 'bg-yellow-500/20' : 'bg-red-500/20'
                  }`}>
                    {option.risk_score <= 2 ? (
                      <CheckCircle className="h-4 w-4 text-green-bright" />
                    ) : option.risk_score <= 3 ? (
                      <AlertTriangle className="h-4 w-4 text-yellow-400" />
                    ) : (
                      <Shield className="h-4 w-4 text-red-400" />
                    )}
                  </div>
                  <span className="text-white">{option.option_text}</span>
                </div>
              </Button>
            ))}
          </div>
        </div>

        <div className="text-center text-white/60 text-sm">
          Click on the option that best describes your current situation
        </div>
      </CardContent>
    </Card>
  );
};

export default AssessmentQuestions;
