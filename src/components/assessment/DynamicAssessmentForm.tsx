import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Building2, ArrowLeft, AlertCircle } from "lucide-react";
import { Link, useParams } from "react-router-dom";
import { useAssessmentBySlug } from "@/hooks/use-assessment-by-slug";
import { useDynamicAssessmentSubmission } from "@/hooks/use-dynamic-assessment-submission";
import { useToast } from "@/hooks/use-toast";
import AssessmentQuestions from "./AssessmentQuestions";
import AssessmentResults from "./AssessmentResults";

interface CompanyInfo {
  companyName: string;
  industry: string;
  employeeCount: string;
  contactName: string;
  email: string;
  phone: string;
}

const DynamicAssessmentForm = () => {
  const { slug } = useParams<{ slug: string }>();
  const { assessmentType, questions, loading, error } = useAssessmentBySlug(slug || '');
  const { createSubmission, isSubmitting, submissionId } = useDynamicAssessmentSubmission(assessmentType?.id || '');
  const { toast } = useToast();

  const [currentStep, setCurrentStep] = useState<'info' | 'questions' | 'results'>('info');
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    companyName: '',
    industry: '',
    employeeCount: '',
    contactName: '',
    email: '',
    phone: ''
  });

  console.log('📍 DynamicAssessmentForm - Current submission ID:', submissionId);
  console.log('📍 DynamicAssessmentForm - Current step:', currentStep);

  const handleInfoSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('🚀 Starting assessment submission for:', assessmentType?.title);
    
    try {
      const newSubmissionId = await createSubmission(companyInfo);
      console.log('✅ Submission created successfully with ID:', newSubmissionId);
      setCurrentStep('questions');
      toast({
        title: "Assessment Started",
        description: `Your ${assessmentType?.title} has been started. Let's continue with the questions.`,
      });
    } catch (error) {
      console.error('❌ Failed to create submission:', error);
      toast({
        title: "Error",
        description: "Failed to start assessment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleQuestionsComplete = () => {
    console.log('🎉 Questions completed');
    console.log('📧 Setting up results with submission ID:', submissionId);
    setCurrentStep('results');
  };

  const updateCompanyInfo = (field: keyof CompanyInfo, value: string) => {
    setCompanyInfo(prev => ({ ...prev, [field]: value }));
  };

  if (loading) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="cyber-gradient-card border border-green-muted/30">
          <CardContent className="p-8 text-center">
            <div className="animate-pulse">
              <div className="h-8 bg-green-muted/20 rounded w-3/4 mx-auto mb-4"></div>
              <div className="h-4 bg-green-muted/20 rounded w-full mb-2"></div>
              <div className="h-4 bg-green-muted/20 rounded w-2/3 mx-auto"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !assessmentType) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="cyber-gradient-card border border-red-500/30">
          <CardContent className="p-8 text-center">
            <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">
              Assessment Not Available
            </h3>
            <p className="text-red-400 mb-6">
              {error || 'The requested assessment could not be found.'}
            </p>
            <Button asChild>
              <Link to="/assessments">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Assessment Library
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (currentStep === 'questions') {
    return (
      <div className="max-w-4xl mx-auto">
        <AssessmentQuestions 
          questions={questions} 
          onComplete={handleQuestionsComplete}
          assessmentType={assessmentType}
        />
      </div>
    );
  }

  if (currentStep === 'results') {
    return (
      <div className="max-w-4xl mx-auto">
        <AssessmentResults 
          submissionId={submissionId!}
          onStartNew={() => setCurrentStep('info')}
        />
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <Card className="cyber-gradient-card border border-green-muted/30">
        <CardContent className="p-8">
          <div className="text-center mb-8">
            <Building2 className="h-12 w-12 text-green-bright mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-2">{assessmentType.title}</h2>
            <p className="text-white/70 mb-4">
              {assessmentType.description}
            </p>
            <div className="inline-flex items-center gap-2 bg-green-dark/30 px-3 py-1 rounded-full text-sm text-green-bright">
              Estimated time: {assessmentType.estimated_time_minutes} minutes
            </div>
          </div>

          <form onSubmit={handleInfoSubmit} className="space-y-6">
            
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="companyName" className="text-white mb-2 block">
                  Company Name *
                </Label>
                <Input
                  id="companyName"
                  type="text"
                  required
                  value={companyInfo.companyName}
                  onChange={(e) => updateCompanyInfo('companyName', e.target.value)}
                  className="bg-black-soft border-green-muted/30 text-white"
                  placeholder="Your company name"
                />
              </div>

              <div>
                <Label htmlFor="industry" className="text-white mb-2 block">
                  Industry *
                </Label>
                <select
                  id="industry"
                  required
                  value={companyInfo.industry}
                  onChange={(e) => updateCompanyInfo('industry', e.target.value)}
                  className="w-full p-3 bg-black-soft border border-green-muted/30 rounded-md text-white"
                >
                  <option value="">Select your industry</option>
                  <option value="healthcare">Healthcare</option>
                  <option value="finance">Financial Services</option>
                  <option value="education">Education</option>
                  <option value="government">Government</option>
                  <option value="manufacturing">Manufacturing</option>
                  <option value="retail">Retail</option>
                  <option value="technology">Technology</option>
                  <option value="professional-services">Professional Services</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>

            <div>
              <Label htmlFor="employeeCount" className="text-white mb-2 block">
                Number of Employees *
              </Label>
              <select
                id="employeeCount"
                required
                value={companyInfo.employeeCount}
                onChange={(e) => updateCompanyInfo('employeeCount', e.target.value)}
                className="w-full p-3 bg-black-soft border border-green-muted/30 rounded-md text-white"
              >
                <option value="">Select employee count</option>
                <option value="1-10">1-10 employees</option>
                <option value="11-50">11-50 employees</option>
                <option value="51-200">51-200 employees</option>
                <option value="201-500">201-500 employees</option>
                <option value="500+">500+ employees</option>
              </select>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="contactName" className="text-white mb-2 block">
                  Your Name *
                </Label>
                <Input
                  id="contactName"
                  type="text"
                  required
                  value={companyInfo.contactName}
                  onChange={(e) => updateCompanyInfo('contactName', e.target.value)}
                  className="bg-black-soft border-green-muted/30 text-white"
                  placeholder="Your full name"
                />
              </div>

              <div>
                <Label htmlFor="phone" className="text-white mb-2 block">
                  Phone Number
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  value={companyInfo.phone}
                  onChange={(e) => updateCompanyInfo('phone', e.target.value)}
                  className="bg-black-soft border-green-muted/30 text-white"
                  placeholder="Your phone number"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="email" className="text-white mb-2 block">
                Email Address *
              </Label>
              <Input
                id="email"
                type="email"
                required
                value={companyInfo.email}
                onChange={(e) => updateCompanyInfo('email', e.target.value)}
                className="bg-black-soft border-green-muted/30 text-white"
                placeholder="<EMAIL>"
              />
            </div>

            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="w-full bg-green-bright hover:bg-green-muted text-black font-semibold py-3"
            >
              {isSubmitting ? "Starting Assessment..." : `Start ${assessmentType.title}`}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default DynamicAssessmentForm;
