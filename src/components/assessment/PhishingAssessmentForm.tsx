
import React, { useState } from "react";
import AssessmentQuestions from "./AssessmentQuestions";
import AssessmentResults from "./AssessmentResults";
import CompanyInfoForm from "./CompanyInfoForm";
import AssessmentLoadingState from "./AssessmentLoadingState";
import { useDynamicAssessmentSubmission } from "@/hooks/use-dynamic-assessment-submission";
import { useAssessmentBySlug } from "@/hooks/use-assessment-by-slug";
import { useToast } from "@/hooks/use-toast";

interface CompanyInfo {
  companyName: string;
  industry: string;
  employeeCount: string;
  contactName: string;
  email: string;
  phone: string;
}

const PhishingAssessmentForm = () => {
  const [currentStep, setCurrentStep] = useState<'info' | 'questions' | 'results'>('info');
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    companyName: '',
    industry: '',
    employeeCount: '',
    contactName: '',
    email: '',
    phone: ''
  });

  // Fetch phishing assessment data
  const { assessmentType, questions, loading, error } = useAssessmentBySlug('phishing-risk');
  const { createSubmission, isSubmitting, submissionId } = useDynamicAssessmentSubmission(assessmentType?.id || '');
  const { toast } = useToast();

  console.log('📍 PhishingAssessmentForm - Current submission ID:', submissionId);
  console.log('📍 PhishingAssessmentForm - Current step:', currentStep);

  const handleInfoSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('🚀 Starting assessment submission with company info:', companyInfo);
    
    try {
      console.log('💼 Creating submission in database...');
      const newSubmissionId = await createSubmission(companyInfo);
      console.log('✅ Submission created successfully with ID:', newSubmissionId);
      
      setCurrentStep('questions');
      toast({
        title: "Assessment Started",
        description: "Your information has been saved. Let's continue with the security questions.",
      });
    } catch (error) {
      console.error('❌ Failed to create submission:', error);
      toast({
        title: "Error",
        description: "Failed to start assessment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleQuestionsComplete = () => {
    console.log('🎉 Questions completed');
    console.log('📧 Setting up results with submission ID:', submissionId);
    setCurrentStep('results');
  };

  const updateCompanyInfo = (field: keyof CompanyInfo, value: string) => {
    setCompanyInfo(prev => ({ ...prev, [field]: value }));
  };

  const handleRetry = () => {
    console.log('🔄 User clicked retry, reloading page...');
    window.location.reload();
  };

  // Show loading or error state
  if (loading || error || !assessmentType) {
    return (
      <AssessmentLoadingState 
        loading={loading}
        error={error || (!assessmentType ? 'Unable to load the phishing assessment.' : null)}
        onRetry={handleRetry}
      />
    );
  }

  if (currentStep === 'questions') {
    console.log('🎯 Rendering questions step');
    return (
      <div className="max-w-4xl mx-auto">
        <AssessmentQuestions 
          questions={questions} 
          assessmentType={assessmentType}
          onComplete={handleQuestionsComplete} 
        />
      </div>
    );
  }

  if (currentStep === 'results') {
    console.log('📊 Rendering results step with submission ID:', submissionId);
    return (
      <div className="max-w-4xl mx-auto">
        <AssessmentResults 
          submissionId={submissionId!}
          onStartNew={() => setCurrentStep('info')}
        />
      </div>
    );
  }

  console.log('📝 Rendering company info form step');
  return (
    <CompanyInfoForm
      companyInfo={companyInfo}
      onUpdateCompanyInfo={updateCompanyInfo}
      onSubmit={handleInfoSubmit}
      isSubmitting={isSubmitting}
      currentStep={currentStep}
    />
  );
};

export default PhishingAssessmentForm;
