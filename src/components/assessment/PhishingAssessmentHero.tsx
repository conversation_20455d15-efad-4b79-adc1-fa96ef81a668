
import React from "react";
import { Shield, Target, Clock, CheckCircle } from "lucide-react";

const PhishingAssessmentHero = () => {
  return (
    <div className="text-center max-w-4xl mx-auto">
      <div className="inline-flex items-center gap-2 bg-green-dark/20 border border-green-muted/30 px-4 py-2 rounded-full mb-6">
        <Target className="h-5 w-5 text-green-bright" />
        <span className="text-green-bright font-medium">Free Security Assessment</span>
      </div>

      <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
        How Vulnerable is Your Business to <span className="text-green-bright">Phishing Attacks?</span>
      </h1>

      <p className="text-xl text-white/80 mb-8 leading-relaxed">
        Take our comprehensive 5-minute assessment to discover your organization's phishing risk level 
        and get personalized recommendations to protect your New Zealand business.
      </p>

      <div className="grid md:grid-cols-3 gap-6 mb-8">
        <div className="flex items-center gap-3 p-4 bg-green-dark/20 rounded-lg border border-green-muted/30">
          <Clock className="h-6 w-6 text-green-bright flex-shrink-0" />
          <div className="text-left">
            <div className="font-semibold text-white">5 Minutes</div>
            <div className="text-white/70 text-sm">Quick assessment</div>
          </div>
        </div>
        <div className="flex items-center gap-3 p-4 bg-green-dark/20 rounded-lg border border-green-muted/30">
          <Shield className="h-6 w-6 text-green-bright flex-shrink-0" />
          <div className="text-left">
            <div className="font-semibold text-white">Expert Analysis</div>
            <div className="text-white/70 text-sm">Professional insights</div>
          </div>
        </div>
        <div className="flex items-center gap-3 p-4 bg-green-dark/20 rounded-lg border border-green-muted/30">
          <CheckCircle className="h-6 w-6 text-green-bright flex-shrink-0" />
          <div className="text-left">
            <div className="font-semibold text-white">Actionable Results</div>
            <div className="text-white/70 text-sm">Clear next steps</div>
          </div>
        </div>
      </div>

      <div className="bg-orange-900/20 border border-orange-500/30 p-4 rounded-lg">
        <p className="text-orange-200 font-medium">
          ⚠️ <strong>Over 60% of NZ businesses</strong> report annual phishing attempts according to recent cybersecurity reports. 
          Don't wait until you're the next victim.
        </p>
      </div>
    </div>
  );
};

export default PhishingAssessmentHero;
