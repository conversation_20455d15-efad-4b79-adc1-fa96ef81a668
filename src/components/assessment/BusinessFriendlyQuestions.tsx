import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  CheckCircle, 
  AlertTriangle, 
  Shield, 
  AlertCircle, 
  Info,
  HelpCircle
} from "lucide-react";

interface BusinessFriendlyOption {
  id: string;
  text: string;
  businessContext: string;
  risk_score: number;
  order_index: number;
}

interface BusinessFriendlyQuestion {
  id: string;
  question_text: string;
  business_explanation: string;
  why_it_matters: string;
  category: string;
  options: BusinessFriendlyOption[];
}

interface Props {
  questions: BusinessFriendlyQuestion[];
  onComplete: (answers: number[]) => void;
  assessmentTitle: string;
}

const BusinessFriendlyQuestions = ({ questions, onComplete, assessmentTitle }: Props) => {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [showExplanation, setShowExplanation] = useState(false);

  const handleAnswer = (optionId: string, riskScore: number) => {
    const newAnswers = [...answers, riskScore];
    setAnswers(newAnswers);

    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(prev => prev + 1);
      setShowExplanation(false);
    } else {
      onComplete(newAnswers);
    }
  };

  const progress = ((currentQuestion + 1) / questions.length) * 100;
  const question = questions[currentQuestion];

  const getRiskColor = (riskScore: number) => {
    if (riskScore <= 2) return "text-green-400 bg-green-500/20 border-green-500/30";
    if (riskScore <= 3) return "text-yellow-400 bg-yellow-500/20 border-yellow-500/30";
    return "text-red-400 bg-red-500/20 border-red-500/30";
  };

  const getRiskIcon = (riskScore: number) => {
    if (riskScore <= 2) return CheckCircle;
    if (riskScore <= 3) return AlertTriangle;
    return AlertCircle;
  };

  const getRiskLabel = (riskScore: number) => {
    if (riskScore <= 2) return "Low Risk";
    if (riskScore <= 3) return "Medium Risk";
    return "High Risk";
  };

  return (
    <Card className="cyber-gradient-card border border-green-muted/30 max-w-4xl mx-auto">
      <CardContent className="p-6 md:p-8">
        {/* Progress Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-xl md:text-2xl font-bold text-white mb-1">
                {assessmentTitle}
              </h2>
              <span className="text-green-bright font-medium">
                Question {currentQuestion + 1} of {questions.length}
              </span>
            </div>
            <Badge variant="outline" className="border-green-muted/50 text-green-bright">
              {question.category}
            </Badge>
          </div>
          <Progress value={progress} className="mb-2" />
          <p className="text-white/60 text-sm">
            {Math.round(progress)}% complete • About {Math.ceil((questions.length - currentQuestion - 1) * 0.5)} minutes remaining
          </p>
        </div>

        {/* Question Content */}
        <div className="space-y-6">
          {/* Main Question */}
          <div>
            <h3 className="text-lg md:text-xl font-semibold text-white mb-3 leading-relaxed">
              {question.question_text}
            </h3>
            
            {/* Business Explanation */}
            <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4 mb-4">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-blue-200 text-sm font-medium mb-1">What this means:</p>
                  <p className="text-blue-100/80 text-sm leading-relaxed">
                    {question.business_explanation}
                  </p>
                </div>
              </div>
            </div>

            {/* Why It Matters - Expandable */}
            <div className="mb-6">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowExplanation(!showExplanation)}
                className="text-white/70 hover:text-white p-0 h-auto font-normal"
              >
                <HelpCircle className="h-4 w-4 mr-2" />
                Why does this matter for my business?
              </Button>
              
              {showExplanation && (
                <div className="mt-3 bg-green-500/10 border border-green-500/30 rounded-lg p-4">
                  <p className="text-green-100/80 text-sm leading-relaxed">
                    {question.why_it_matters}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Answer Options */}
          <div className="space-y-3">
            <h4 className="text-white font-medium mb-3">Choose the option that best describes your business:</h4>
            
            {question.options.map((option) => {
              const RiskIcon = getRiskIcon(option.risk_score);
              
              return (
                <Button
                  key={option.id}
                  variant="outline"
                  onClick={() => handleAnswer(option.id, option.risk_score)}
                  className="w-full text-left p-4 h-auto border-green-muted/30 hover:border-green-bright/50 hover:bg-green-dark/20 transition-all duration-200 group"
                >
                  <div className="flex items-start gap-4 w-full">
                    {/* Risk Indicator */}
                    <div className={`p-2 rounded-full flex-shrink-0 mt-1 ${getRiskColor(option.risk_score)}`}>
                      <RiskIcon className="h-4 w-4" />
                    </div>
                    
                    {/* Option Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-2">
                        <p className="text-white font-medium text-base leading-relaxed group-hover:text-green-bright transition-colors">
                          {option.text}
                        </p>
                        <Badge 
                          variant="outline" 
                          className={`ml-3 flex-shrink-0 text-xs ${getRiskColor(option.risk_score)}`}
                        >
                          {getRiskLabel(option.risk_score)}
                        </Badge>
                      </div>
                      
                      {/* Business Context */}
                      <p className="text-white/60 text-sm leading-relaxed">
                        {option.businessContext}
                      </p>
                    </div>
                  </div>
                </Button>
              );
            })}
          </div>

          {/* Help Text */}
          <div className="bg-gray-800/50 border border-gray-600/30 rounded-lg p-4 mt-6">
            <div className="flex items-start gap-3">
              <Shield className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-gray-300 text-sm font-medium mb-1">Not sure about your answer?</p>
                <p className="text-gray-400 text-sm">
                  Choose the option that most closely matches your current situation. 
                  You can always improve your security after seeing the results.
                </p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Sample business-friendly questions data
export const sampleBusinessFriendlyQuestions: BusinessFriendlyQuestion[] = [
  {
    id: "1",
    question_text: "Does your email system verify that messages are actually from your company?",
    business_explanation: "Email verification helps prevent scammers from sending fake emails that appear to come from your business, protecting both you and your customers.",
    why_it_matters: "Without email verification, cybercriminals can easily impersonate your business to steal customer information, damage your reputation, or trick people into sending money to fake accounts. This is one of the most common ways businesses are attacked.",
    category: "Email Security",
    options: [
      {
        id: "1a",
        text: "Yes, we have comprehensive email verification set up",
        businessContext: "Your IT team or email provider has configured SPF, DKIM, and DMARC records to verify your emails.",
        risk_score: 1,
        order_index: 1
      },
      {
        id: "1b", 
        text: "We have some email verification, but I'm not sure how complete it is",
        businessContext: "You might have basic email security, but it may not be fully configured to prevent all impersonation attempts.",
        risk_score: 2,
        order_index: 2
      },
      {
        id: "1c",
        text: "I'm not sure about our email verification setup",
        businessContext: "You're unsure about your current email security measures - this is common for many businesses.",
        risk_score: 3,
        order_index: 3
      },
      {
        id: "1d",
        text: "No, we don't have email verification set up",
        businessContext: "Your emails aren't protected against impersonation, making it easy for scammers to fake messages from your business.",
        risk_score: 4,
        order_index: 4
      }
    ]
  },
  {
    id: "2",
    question_text: "How often do you train your team about email security and phishing attacks?",
    business_explanation: "Regular security training helps your employees recognize and avoid email scams, which are the most common way cybercriminals attack businesses.",
    why_it_matters: "95% of successful cyber attacks start with a phishing email. Your employees are your first line of defense, but they need to know what to look for. Even one employee falling for a scam can compromise your entire business.",
    category: "Security Awareness",
    options: [
      {
        id: "2a",
        text: "We provide regular training and run practice phishing tests",
        businessContext: "Your team receives ongoing security education and practice identifying fake emails through simulated attacks.",
        risk_score: 1,
        order_index: 1
      },
      {
        id: "2b",
        text: "We provide annual security training to all employees",
        businessContext: "Your team gets yearly security education, which is good but may not be frequent enough for rapidly changing threats.",
        risk_score: 2,
        order_index: 2
      },
      {
        id: "2c",
        text: "We've mentioned email security, but no formal training",
        businessContext: "Security awareness exists informally, but employees haven't received structured training on identifying threats.",
        risk_score: 3,
        order_index: 3
      },
      {
        id: "2d",
        text: "We haven't provided any email security training",
        businessContext: "Your employees haven't been trained to recognize phishing emails, making them vulnerable to scams.",
        risk_score: 4,
        order_index: 4
      }
    ]
  }
];

export default BusinessFriendlyQuestions;
