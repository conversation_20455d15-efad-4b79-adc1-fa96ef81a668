
import React from "react";
import Section from "@/components/Section";
import { Separator } from "@/components/ui/separator";
import { Shield, Search, Zap } from "lucide-react";
const Evolution = () => {
  return <Section background="soft" className="py-16">
      <div className="max-w-4xl mx-auto">
        <h2 className="text-2xl md:text-3xl font-bold mb-8 text-center">The Birth of Blackvault</h2>
        
        <div className="cyber-card p-8 mb-12 bg-black-DEFAULT">
          <div className="flex flex-col md:flex-row items-center mb-8">
            <div className="mb-6 md:mb-0 md:mr-8">
              <div className="w-32 h-32 flex items-center justify-center bg-black-muted rounded-md border border-green-muted cyber-glow">
                <Shield className="w-20 h-20 text-green-bright/80" />
              </div>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-3">BlackVault: The Flagship Product</h3>
              <p className="text-white/80">
                We started building and iterating on "BlackVault" - a comprehensive email security auditing application 
                under the BlackVeil brand. BlackVault provides a protective layer against email-based cyber threats, 
                offering businesses the tools they need to secure their communications.
              </p>
            </div>
          </div>
          
          <Separator className="cyber-divider" />
          
          <div className="grid md:grid-cols-3 gap-6 mt-8">
            <div className="p-4 bg-black-muted rounded-lg border border-green-muted/30 hover:border-green-muted/70 transition-all duration-300">
              <Shield className="text-green-bright mb-3" />
              <h4 className="font-bold mb-2">Protection</h4>
              <p className="text-white/70 text-sm">
                Email authentication to prevent spoofing and phishing attacks
              </p>
            </div>
            
            <div className="p-4 bg-black-muted rounded-lg border border-green-muted/30 hover:border-green-muted/70 transition-all duration-300">
              <Search className="text-green-bright mb-3" />
              <h4 className="font-bold mb-2">Scanning</h4>
              <p className="text-white/70 text-sm">
                Continuous monitoring of email security configurations
              </p>
            </div>
            
            <div className="p-4 bg-black-muted rounded-lg border border-green-muted/30 hover:border-green-muted/70 transition-all duration-300">
              <Zap className="text-green-bright mb-3" />
              <h4 className="font-bold mb-2">Automation</h4>
              <p className="text-white/70 text-sm">
                Streamlined processes to implement security protocols
              </p>
            </div>
          </div>
        </div>
      </div>
    </Section>;
};
export default Evolution;
