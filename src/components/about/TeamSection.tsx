
import React, { useState, useEffect, useRef } from "react";
import { useInView } from "@/hooks/use-reveal-animation";
import Section from "@/components/Section";
import { Card, CardContent } from "@/components/ui/card";

const TeamSection = () => {
  const [loaded, setLoaded] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, threshold: 0.2 });
  
  useEffect(() => {
    setLoaded(true);
  }, []);

  return (
    <Section className="py-20 sm:py-24 md:py-28 relative" id="team">
      <div ref={sectionRef} className="max-w-5xl mx-auto">
        <div className="text-center mb-12 sm:mb-16">
          <span className={`font-mono uppercase tracking-wider text-xs bg-green-dark/70 text-green-light 
            px-4 py-1.5 rounded-full cyber-glow inline-block mb-4 opacity-0 ${isInView ? 'animate-fade-in' : ''}`}>
            Our Team
          </span>
          
          <h2 className={`text-3xl md:text-4xl font-bold mb-4 cyber-glow-text opacity-0 ${isInView ? 'animate-fade-in' : ''}`} 
            style={{ animationDelay: '0.2s' }}>
            Meet The Experts
          </h2>
          
          <div className={`w-0 h-1 bg-gradient-to-r from-green-bright to-transparent mx-auto rounded-full mb-6 
            transition-all duration-1000 opacity-0 ${isInView ? 'w-24 opacity-100' : ''}`}>
          </div>
          
          <p className={`text-white/80 max-w-2xl mx-auto leading-relaxed mb-6 opacity-0 ${isInView ? 'animate-fade-in' : ''}`}
            style={{ animationDelay: '0.3s' }}>
            Our team combines expertise in cybersecurity, software development, and compliance to deliver innovative email security solutions.
          </p>
        </div>
        
        <Card className="cyber-gradient-card border-green-muted/40 overflow-hidden">
          <CardContent className="p-8">
            <div className="flex flex-col items-center mb-8">
              <div className="inline-block p-4 bg-green-dark/40 rounded-full mb-6">
                <span role="img" aria-label="team" className="text-green-bright text-3xl">👥</span>
              </div>
              <h3 className="text-xl font-bold mb-4 text-center">A Team of Security Experts</h3>
              <p className="text-white/80 max-w-2xl mx-auto mb-4 text-center">
                Our team combines expertise in cybersecurity, software development, and data analysis to 
                deliver cutting-edge protection for businesses of all sizes.
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div className="p-5 bg-black-soft rounded-lg border border-green-muted/30 hover:border-green-muted/60 transition-all duration-300">
                <h4 className="font-bold text-green-light mb-2">Research Team</h4>
                <p className="text-white/70 text-sm">
                  Dedicated professionals constantly monitoring emerging threats and improving detection algorithms
                </p>
              </div>
              
              <div className="p-5 bg-black-soft rounded-lg border border-green-muted/30 hover:border-green-muted/60 transition-all duration-300">
                <h4 className="font-bold text-green-light mb-2">Development Team</h4>
                <p className="text-white/70 text-sm">
                  Expert engineers building robust and scalable security solutions
                </p>
              </div>
            </div>
            
            <div className="mt-8 text-center">
              <div className="w-16 h-px bg-green-muted/50 mx-auto my-6"></div>
              <p className="text-white/60 text-sm italic">
                Our complete team roster will be updated soon
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </Section>
  );
};

export default TeamSection;
