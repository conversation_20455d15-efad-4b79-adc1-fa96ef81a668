
import React from "react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import { Linkedin, Mail } from "lucide-react";

interface TeamMemberProps {
  name: string;
  position: string;
  bio: string;
  imageSrc?: string;
  email?: string;
  linkedin?: string;
  level: "leadership" | "director";
  index: number;
  loaded: boolean;
}

const TeamMemberCard = ({ 
  name, 
  position, 
  bio, 
  imageSrc, 
  email, 
  linkedin, 
  level, 
  index, 
  loaded 
}: TeamMemberProps) => {
  const initials = name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase();
    
  const emailAddress = email || `${name.split(" ")[0].toLowerCase()}@blackveil.co.nz`;
  
  const animationDelay = `${0.15 * (index + 1)}s`;

  return (
    <div 
      className={`transform transition-all duration-700 w-64 opacity-0 ${
        loaded ? 'opacity-100 translate-y-0' : 'translate-y-8'
      }`} 
      style={{ transitionDelay: animationDelay }}
    >
      <Card className={`
        backdrop-blur-lg bg-black-soft/70 overflow-hidden relative
        border border-green-muted/40 h-full w-full
        transform transition-all duration-500 hover:translate-y-[-5px] 
        hover:shadow-[0_8px_30px_rgba(0,255,140,0.3)] group
        before:absolute before:inset-0 before:bg-gradient-to-br 
        before:from-green-muted/10 before:to-transparent before:opacity-0
        before:transition-opacity before:duration-500 before:z-0
        group-hover:before:opacity-100 rounded-lg
      `}>
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-bright/30 via-green-bright to-green-bright/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
        
        <CardContent className="p-6 relative z-10">
          <div className="flex flex-col items-center text-center">
            <div className="relative">
              <div className="absolute inset-0 bg-green-bright/20 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-500 scale-110"></div>
              <Avatar className="h-20 w-20 mb-4 border-2 border-green-muted group-hover:border-green-bright transition-colors duration-500">
                {imageSrc ? (
                  <AvatarImage src={imageSrc} alt={`Photo of ${name}, ${position} at BlackVeil`} className="scale-110 group-hover:scale-125 transition-transform duration-700" />
                ) : (
                  <AvatarFallback className="bg-green-dark text-green-bright text-xl">
                    {initials}
                  </AvatarFallback>
                )}
              </Avatar>
            </div>
            
            <h3 className="text-xl font-bold mb-1 text-white group-hover:text-green-bright transition-colors duration-300">{name}</h3>
            <p className="text-sm font-mono text-green-muted mb-4">{position}</p>
            
            <div className="h-px w-0 bg-gradient-to-r from-transparent via-green-bright to-transparent opacity-0 group-hover:w-full group-hover:opacity-100 transition-all duration-700 my-4"></div>
            
            {level === "leadership" && (
              <p className="text-white/80 text-sm mb-5 opacity-90 group-hover:opacity-100 transition-opacity duration-300">{bio}</p>
            )}
            
            <div className="flex space-x-3 mt-auto">
              <a 
                href={`mailto:${emailAddress}`} 
                className="text-green-muted hover:text-green-bright transition-colors p-2 rounded-full hover:bg-green-dark/30"
              >
                <Mail size={18} className="transform group-hover:scale-110 transition-transform duration-300" />
              </a>
              <a 
                href="#" 
                className="text-green-muted hover:text-green-bright transition-colors p-2 rounded-full hover:bg-green-dark/30"
              >
                <Linkedin size={18} className="transform group-hover:scale-110 transition-transform duration-300" />
              </a>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TeamMemberCard;
