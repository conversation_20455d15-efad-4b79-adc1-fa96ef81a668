
import React from "react";
import Section from "@/components/Section";

const CompanyStory = () => {
  return (
    <Section className="py-12 xs:py-16">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <span className="font-mono uppercase tracking-wider text-xs bg-green-dark/70 text-green-light px-4 py-1.5 rounded-full">
            Our Journey
          </span>
          <h2 className="text-2xl xs:text-3xl font-bold mt-5 mb-4">The BlackVeil Story</h2>
          <p className="text-white/70">
            From research to revolution: How we're changing the landscape of email security.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 gap-8">
          <div className="cyber-gradient-card p-8 rounded-lg">
            <h3 className="text-xl font-bold mb-4">The Beginning</h3>
            <p className="text-white/80 leading-relaxed">
              Our journey began with a simple question: How vulnerable are New Zealand businesses to email fraud? 
              This led us to develop specialized crawlers to analyze email security across thousands of domains.
            </p>
          </div>
          
          <div className="cyber-gradient-card p-8 rounded-lg">
            <h3 className="text-xl font-bold mb-4">The Discovery</h3>
            <p className="text-white/80 leading-relaxed">
              The results were startling. Our research revealed widespread vulnerabilities in email systems, 
              with many businesses completely unprotected against sophisticated impersonation attacks.
            </p>
          </div>
        </div>
      </div>
    </Section>
  );
};

export default CompanyStory;
