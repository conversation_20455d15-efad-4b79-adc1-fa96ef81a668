
import React from 'react';
import TeamMemberCard from './TeamMemberCard';
import { useImagePreloader } from '@/hooks/use-image-preload';
import { leadershipTeam, directorTeam } from '@/data/team-data';
import Section from '@/components/Section';

const TeamOrgChart = () => {
  const imageUrls = [...leadershipTeam, ...directorTeam]
    .map(member => member.imageSrc)
    .filter((imageSrc): imageSrc is string => imageSrc !== undefined);

  const { loaded: loadedImages } = useImagePreloader(imageUrls);

  const renderLeadershipTeam = () => {
    return leadershipTeam.map((member, index) => (
      <TeamMemberCard
        key={member.name}
        level="leadership"
        index={index}
        loaded={loadedImages[member.name]}
        name={member.name}
        position={member.position}
        bio={member.bio}
        imageSrc={member.imageSrc}
        email={member.email}
        linkedin={member.linkedin}
      />
    ));
  };

  const renderDirectorTeam = () => {
    return directorTeam.map((member, index) => (
      <TeamMemberCard
        key={member.name}
        level="director"
        index={index}
        loaded={loadedImages[member.name]}
        name={member.name}
        position={member.position}
        bio={member.bio}
        imageSrc={member.imageSrc}
        email={member.email}
        linkedin={member.linkedin}
      />
    ));
  };

  return (
    <Section 
      id="team"
      className="bg-black/40"
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-2xl font-bold text-white mb-6 text-center">Meet Our Team</h2>
        <p className="text-center text-white/70 mb-10">Our leadership and directors bring a wealth of experience and a shared commitment to excellence in cybersecurity.</p>
        
        <h3 className="text-2xl font-bold text-white mb-6 text-center">Leadership</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {renderLeadershipTeam()}
        </div>

        <h3 className="text-2xl font-bold text-white mt-12 mb-6 text-center">Directors</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {renderDirectorTeam()}
        </div>
      </div>
    </Section>
  );
};

export default TeamOrgChart;
