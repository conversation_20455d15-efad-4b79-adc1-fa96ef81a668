
import React from "react";
import Section from "@/components/Section";
import { Card, CardContent } from "@/components/ui/card";
import { Search, Database } from "lucide-react";

const ProblemDiscovery = () => {
  return (
    <Section className="py-20">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 cyber-glow-text">The Problem & Discovery</h2>
          <div className="w-24 h-1 bg-gradient-to-r from-green-bright to-transparent mx-auto rounded-full mb-6"></div>
          <p className="text-white/70 max-w-2xl mx-auto">
            How <PERSON><PERSON><PERSON> identified and addressed critical email security vulnerabilities
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 gap-10 mb-16">
          <Card className="cyber-gradient-card border-green-muted/40 overflow-hidden transform transition-all duration-500 hover:translate-y-[-5px]">
            <CardContent className="p-8">
              <div className="flex items-start mb-6">
                <div className="p-3 bg-green-dark/40 rounded-lg mr-4">
                  <Search className="text-green-bright h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-xl font-bold mb-2 cyber-glow-text">The Research</h3>
                  <p className="text-white/80 text-left">After the data was collated and reported on, it became obvious that a specialized product was required to help fix this systemic issue with email security.</p>
                </div>
              </div>
              <div className="cyber-line my-6"></div>
              <p className="text-white/80 text-left">
                The majority of businesses worldwide lacked proper email authentication protocols, leaving 
                them vulnerable to phishing and spoofing attacks.
              </p>
            </CardContent>
          </Card>
          
          <Card className="cyber-gradient-card border-green-muted/40 overflow-hidden transform transition-all duration-500 hover:translate-y-[-5px]">
            <CardContent className="p-8">
              <div className="flex items-start mb-6">
                <div className="p-3 bg-green-dark/40 rounded-lg mr-4">
                  <Database className="text-green-bright h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-xl font-bold mb-2 cyber-glow-text">The Global Issue</h3>
                  <p className="text-white/80 text-left">
                    We extended our research and discovered that the issue extends across borders, 
                    affecting businesses of all sizes globally.
                  </p>
                </div>
              </div>
              <div className="cyber-line my-6"></div>
              <p className="text-white/80 text-left">
                Even some Fortune 500 companies weren't properly implementing email security standards, highlighting 
                a widespread vulnerability in digital infrastructure.
              </p>
            </CardContent>
          </Card>
        </div>
        
        <div className="text-center bg-black-soft p-8 rounded-lg border border-green-muted/20 shadow-[0_0_30px_rgba(0,0,0,0.5)]">
          <div className="inline-block py-2 px-6 bg-green-dark/50 border border-green-muted/60 rounded-full text-green-bright font-mono text-sm mb-6 cyber-glow">
            The Realization
          </div>
          <p className="text-white/90 text-lg max-w-2xl mx-auto leading-relaxed">
            Once we realized the scale of this issue, we went on a mission to develop a solution that could
            help businesses of all sizes protect their digital communications.
          </p>
        </div>
      </div>
    </Section>
  );
};

export default ProblemDiscovery;
