
import React from "react";
import Section from "@/components/Section";
import { ArrowRight } from "lucide-react";
import { <PERSON> } from "react-router-dom";

const FutureVision = () => {
  return (
    <Section className="py-16 xs:py-20">
      <div className="max-w-4xl mx-auto text-center">
        <span className="font-mono uppercase tracking-wider text-xs bg-green-dark/70 text-green-light px-4 py-1.5 rounded-full">
          Looking Forward
        </span>
        <h2 className="text-2xl xs:text-3xl font-bold mt-5 mb-4">Our Vision for the Future</h2>
        <p className="text-white/70 max-w-2xl mx-auto mb-8">
          We're building a future where businesses can communicate with confidence, 
          free from the threat of email fraud and impersonation.
        </p>
        
        <Link 
          to="/services" 
          className="cyber-button inline-flex items-center gap-2"
        >
          <span>Explore Our Services</span>
          <ArrowRight className="w-4 h-4" />
        </Link>
      </div>
    </Section>
  );
};

export default FutureVision;
