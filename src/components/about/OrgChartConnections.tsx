
import React from "react";

interface Position {
  x: number;
  y: number;
}

interface OrgChartConnectionsProps {
  ceoPosition: Position;
  teamPositions: Position[];
  chartWidth: number;
  chartHeight: number;
  loaded: boolean;
}

const OrgChartConnections = ({ 
  ceoPosition, 
  teamPositions, 
  chartWidth, 
  chartHeight, 
  loaded 
}: OrgChartConnectionsProps) => {
  const renderConnections = () => {
    const paths = teamPositions.map((pos, index) => {
      const startX = ceoPosition.x;
      const startY = ceoPosition.y + 100;
      const endX = pos.x;
      const endY = pos.y - 50;
      
      const controlPoint1X = startX;
      const controlPoint1Y = startY + 50;
      const controlPoint2X = endX;
      const controlPoint2Y = startY + 50;
      
      const pathData = `M ${startX} ${startY} C ${controlPoint1X} ${controlPoint1Y}, ${controlPoint2X} ${controlPoint2Y}, ${endX} ${endY}`;
      
      return (
        <g key={`connection-${index}`} className="connection-path">
          <path 
            d={pathData} 
            stroke="rgba(0, 255, 140, 0.2)" 
            strokeWidth="6" 
            fill="none" 
            className="opacity-0 animate-fade-in"
            style={{ animationDelay: `${0.5 + (index * 0.2)}s` }}
            filter="url(#glow)"
          />
          
          <path 
            d={pathData} 
            stroke="rgba(0, 255, 140, 0.7)" 
            strokeWidth="2" 
            strokeDasharray="5,3"
            fill="none" 
            className={`opacity-0 ${loaded ? 'animate-dash-flow' : ''}`}
            style={{ animationDelay: `${0.7 + (index * 0.2)}s` }}
          />
          
          <circle 
            r="4" 
            fill="rgba(0, 255, 140, 0.9)"
            className={`opacity-0 ${loaded ? 'animate-pulse' : ''}`}
            style={{ animationDuration: '3s', animationDelay: `${1 + (index * 0.3)}s` }}
          >
            <animateMotion 
              path={pathData} 
              dur="6s"
              begin={`${1 + (index * 0.3)}s`}
              repeatCount="indefinite"
              rotate="auto"
            />
          </circle>
        </g>
      );
    });
    
    return (
      <svg 
        className={`absolute top-0 left-0 w-full h-full opacity-0 ${loaded ? 'opacity-100' : ''} transition-opacity duration-1000 delay-500 z-0`}
        viewBox={`0 0 ${chartWidth} ${chartHeight}`}
        preserveAspectRatio="none"
      >
        <defs>
          <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur stdDeviation="4" result="blur" />
            <feComposite in="SourceGraphic" in2="blur" operator="over" />
          </filter>
          
          <linearGradient id="pathGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="rgba(0, 255, 140, 0.1)" />
            <stop offset="50%" stopColor="rgba(0, 255, 140, 0.8)" />
            <stop offset="100%" stopColor="rgba(0, 255, 140, 0.1)" />
          </linearGradient>
        </defs>
        
        {paths}
      </svg>
    );
  };

  return (
    <div className="absolute inset-0 pointer-events-none">
      {renderConnections()}
      <style>{`
        @keyframes dash-flow {
          0% {
            stroke-dashoffset: 100;
            opacity: 0;
          }
          20% {
            opacity: 1;
          }
          100% {
            stroke-dashoffset: 0;
            opacity: 1;
          }
        }
        .animate-dash-flow {
          animation: dash-flow 2s forwards;
        }
        
        @keyframes fade-in {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        
        .animate-fade-in {
          animation: fade-in 0.5s forwards ease-out;
        }
      `}</style>
    </div>
  );
};

export default OrgChartConnections;
