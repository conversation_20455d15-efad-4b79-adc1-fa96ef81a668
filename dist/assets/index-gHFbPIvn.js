const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/browser-ClODhbXO.js","assets/vendor-TvU2VNGP.js","assets/AboutPage-mPJmZo0Y.js","assets/core-BZ2etCil.js","assets/ui-qGggI9tr.js","assets/ServicesPage-C-yqMHYG.js","assets/separator-FVlK6acV.js","assets/cyber-card-HVPusBzs.js","assets/ServiceSchema-B5-db_oF.js","assets/ContactPage-DgJZBVvg.js","assets/label-qGISAqow.js","assets/security-D6XyL6Yo.js","assets/input-wyDUQPna.js","assets/textarea-LL_Wfanq.js","assets/ContactPage-6Tu2b2-r.css","assets/PortfolioPage-LVmrSn5C.js","assets/animated-reveal-BIFYrcd3.js","assets/ProductPage-DOGKd3WR.js","assets/BlogPage-BJI_7Ip3.js","assets/index-RABeG8Z6.js","assets/post-8-C3aRwc2Q.js","assets/BlogPostPage-bgzhMLHW.js","assets/BlogPostPage-CkxdVhCY.css","assets/PrivacyPolicyPage-CG9L2_ze.js","assets/LegalPageHeader-BTVep3BB.js","assets/TermsOfServicePage-B8AHJvR8.js","assets/CookiePolicyPage-7fTgCI27.js","assets/PhishingSimulationPage-D79IpelA.js","assets/NotFound-2MhZjmJl.js","assets/PhishingAssessmentPage-CBwJ6FZe.js","assets/use-assessment-by-slug-YCcU9fqT.js","assets/AuthPage-DpESnke1.js","assets/AdminDashboardPage-BokzDjxX.js","assets/AssessmentLibraryPage-COEOtXJh.js","assets/DynamicAssessmentPage-Cf3KY6Ef.js"])))=>i.map(i=>d[i]);
import{a as e,j as t,L as s,r,h as n,E as i,R as a,H as o,I as l,B as c,T as d,S as u,i as h,k as m,F as p,l as f,m as g,u as x,P as b,U as y,X as v,M as w,n as j,o as N,p as k,A as _,q as T,e as S,C as E,s as A,t as C,v as P,w as O,x as R,y as I,d as L,z as $,N as D,D as M,J as U,K as B,O as z,Q as F,V as q,Y as V,W as H,f as W,_ as K,$ as G,a0 as J,a1 as Y,a2 as Z,a3 as X,a4 as Q,a5 as ee,a6 as te,g as se,a7 as re,a8 as ne}from"./vendor-TvU2VNGP.js";import{m as ie,c as ae,A as oe,S as le,H as ce}from"./core-BZ2etCil.js";import{S as de,c as ue,a as he,u as me,B as pe,P as fe,V as ge,b as xe,d as be,e as ye,f as ve,R as we,g as je,h as Ne,i as ke,O as _e,C as Te,j as Se,T as Ee,D as Ae,k as Ce,l as Pe,I as Oe,H as Re,m as Ie,n as Le,o as $e,L as De,p as Me,q as Ue,r as Be}from"./ui-qGggI9tr.js";var ze;!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const s of e)if("childList"===s.type)for(const e of s.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var Fe=e;ze=Fe.createRoot,Fe.hydrateRoot;const qe=()=>t.jsx(s,{to:"/",className:"flex items-center","aria-label":"BlackVeil Home",children:t.jsxs("span",{className:"font-bold text-base xs:text-lg md:text-xl",children:[t.jsx("span",{className:"text-white",children:"BLACK"}),t.jsx("span",{className:"text-green-bright",children:"V"}),t.jsx("span",{className:"text-white",children:"EIL"})]})}),Ve="Enter",He=" ",We="Escape",Ke="ArrowDown",Ge="ArrowUp",Je="Tab",Ye=({triggerLabel:e,triggerIcon:a,groups:o,menuId:l,ariaLabel:c,className:d=""})=>{const[u,h]=r.useState(!1),m=r.useRef(null),p=r.useRef(null);r.useEffect((()=>{if(u&&p.current){const e=p.current.querySelector('[role="menuitem"]:not([tabindex="-1"])');e?.focus()}}),[u]),r.useEffect((()=>{if(!u)return;const e=e=>{p.current?.contains(e.target)||m.current?.contains(e.target)||h(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)}),[u]);const[f,g]=r.useState({});r.useEffect((()=>{if(u&&m.current&&p.current){const e=m.current;if(e){const t=e.offsetLeft+e.offsetWidth/2;g({left:`${t}px`,transform:"translateX(-50%)",top:"calc(100% + 0.5rem)",minWidth:400,position:"absolute",zIndex:1e3})}}}),[u]);return t.jsxs("div",{className:`relative inline-block ${d}`,role:"presentation",children:[t.jsxs("button",{ref:m,"aria-haspopup":"menu","aria-expanded":u,"aria-controls":l,className:"flex items-center gap-2 px-2 lg:px-3 py-2 text-sm lg:text-base font-medium bg-transparent hover:text-green-bright focus:outline-none focus-visible:ring-2 focus-visible:ring-green-bright transition-colors navigation-menu-trigger",onClick:()=>h((e=>!e)),onKeyDown:e=>{e.key!==Ve&&e.key!==He&&e.key!==Ke||(e.preventDefault(),h(!0))},tabIndex:0,type:"button",id:`${l}-trigger`,"aria-label":c||e,children:[a,t.jsx("span",{children:e}),t.jsx(n,{className:"ml-1 h-4 w-4 transition-transform duration-200 "+(u?"rotate-180":""),"aria-hidden":"true"})]}),u&&t.jsx("div",{ref:p,id:l,role:"menu","aria-label":c||e,"aria-labelledby":`${l}-trigger`,tabIndex:-1,style:f,className:"bg-black-soft/95 backdrop-blur-md border border-green-muted/20 rounded-lg shadow-2xl overflow-hidden animate-scale-in",onKeyDown:e=>{const t=Array.from(p.current?.querySelectorAll('[role="menuitem"]')||[]),s=t.indexOf(document.activeElement);switch(e.key){case We:h(!1),m.current?.focus();break;case Ke:e.preventDefault(),s<t.length-1?t[s+1].focus():t[0].focus();break;case Ge:e.preventDefault(),s>0?t[s-1].focus():t[t.length-1].focus();break;case Je:h(!1)}},children:t.jsx("div",{className:"p-2",children:o.map(((e,r)=>t.jsxs("div",{className:""+(r>0?"border-t border-white/10 pt-2 mt-2":""),children:[e.label&&t.jsx("div",{className:"text-xs uppercase text-green-muted/70 font-mono tracking-wider mb-2 px-4 py-1",children:e.label}),t.jsx("div",{className:"space-y-1",role:"none",children:e.items.map(((e,n)=>((e,r,n)=>{const a=t.jsxs("div",{className:"flex items-start gap-3 w-full",children:[e.icon&&t.jsx("span",{className:"text-green-bright text-lg mt-0.5 flex-shrink-0","aria-hidden":"true",children:e.icon}),t.jsxs("div",{className:"flex-1 min-w-0",children:[t.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[t.jsx("span",{className:"font-medium text-white truncate",children:e.label}),e.isNew&&t.jsx("span",{className:"bg-green-bright text-black text-xs px-1.5 py-0.5 rounded-full font-bold",children:"NEW"}),e.isPopular&&t.jsx("span",{className:"bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded-full font-bold",children:"POPULAR"}),e.external&&t.jsx(i,{className:"h-3 w-3 text-white/60","aria-hidden":"true"})]}),e.description&&t.jsx("p",{className:"text-xs text-white/70 leading-relaxed",children:e.description}),e.badge&&t.jsx("span",{className:"inline-block mt-1 text-xs bg-orange-600/20 text-orange-300 px-2 py-0.5 rounded",children:e.badge})]})]}),o={role:"menuitem",tabIndex:0,className:"block w-full text-left px-4 py-3 rounded-lg text-white/90 hover:bg-green-bright/10 hover:text-green-bright focus:bg-green-bright/20 focus:text-green-bright transition-all duration-200 group","aria-label":e.description?`${e.label}: ${e.description}`:e.label,onClick:()=>{e.onClick&&e.onClick(),h(!1)}};return e.external?t.jsx("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",...o,children:a},`${r}-${n}`):t.jsx(s,{to:e.href,...o,children:a},`${r}-${n}`)})(e,r,n)))})]},r)))})})]})},Ze=[{label:"About Us",items:[{label:"Home",href:"/",icon:a.createElement(o),description:"Welcome to BlackVeil - Your cybersecurity partner"},{label:"About BlackVeil",href:"/about",icon:a.createElement(l),description:"Learn about our mission and team"}]}],Xe=[{label:"Security Services",items:[{label:"All Services",href:"/services",icon:a.createElement(c),description:"Comprehensive cybersecurity solutions for your business"},{label:"Free Phishing Risk Assessment",href:"/assessment/phishing-risk",icon:a.createElement(d),description:"Discover your organization's vulnerability to phishing attacks",badge:"FREE",isNew:!0},{label:"DMARC Implementation",href:"/services#dmarc",icon:a.createElement(u),description:"Protect against email spoofing and phishing",badge:"Urgent - 2025 Deadline",isPopular:!0}]}],Qe=[{label:"Security Products",items:[{label:"BlackVault Lite",href:"/products/blackvault-lite",icon:a.createElement(h),description:"Automated domain security scanning and monitoring for global organizations",isNew:!0}]}],et=[{label:"Learning Center",items:[{label:"Security Resources",href:"/resources",icon:a.createElement(m),description:"Guides, whitepapers, and security best practices"},{label:"Blog & Insights",href:"/blog",icon:a.createElement(p),description:"Latest cybersecurity news and expert analysis"}]},{label:"Quick Actions",items:[{label:"Browse All Assessments",href:"/assessments",icon:a.createElement(d),description:"View our complete library of security assessments",badge:"Free"},{label:"Free Security Assessment",href:"/assessment/phishing-risk",icon:a.createElement(d),description:"Test your organization's phishing vulnerability",badge:"5 Minutes"},{label:"Search Blog",href:"/blog?search=true",icon:a.createElement(f),description:"Find specific security topics and solutions"},{label:"Emergency Response",href:"/contact?urgency=high",icon:a.createElement(g),description:"Immediate assistance for security incidents",badge:"24/7 Available"}]}],tt=()=>{const e=x(),r=t=>e.pathname===t?"text-green-bright":"text-white/80";return t.jsxs("nav",{className:"hidden md:flex items-center space-x-1 lg:space-x-2","aria-label":"Main navigation",role:"menubar",children:[t.jsx(Ye,{triggerLabel:"Company",groups:Ze,menuId:"company-mega-menu",ariaLabel:"Company menu"}),t.jsx(Ye,{triggerLabel:"Services",groups:Xe,menuId:"services-mega-menu",ariaLabel:"Services menu"}),t.jsx(Ye,{triggerLabel:"Products",groups:Qe,menuId:"products-mega-menu",ariaLabel:"Products menu"}),t.jsx(s,{to:"/portfolio",className:`${r("/portfolio")} relative font-medium text-sm lg:text-base px-2 lg:px-3 py-1.5 hover:text-green-bright transition-colors navigation-menu-link`,"aria-current":"/portfolio"===e.pathname?"page":void 0,role:"menuitem",children:"Portfolio"}),t.jsx(Ye,{triggerLabel:"Resources",groups:et,menuId:"resources-mega-menu",ariaLabel:"Resources menu"}),t.jsx(s,{to:"/contact",className:`${r("/contact")} relative font-medium text-sm lg:text-base px-2 lg:px-3 py-1.5 hover:text-green-bright transition-colors navigation-menu-link`,"aria-current":"/contact"===e.pathname?"page":void 0,role:"menuitem",children:"Contact"}),t.jsx("a",{href:"https://discord.gg/MQ4B9jT4hU",target:"_blank",rel:"noopener noreferrer",className:"relative font-medium text-sm lg:text-base px-2 lg:px-3 py-1.5 text-white/80 hover:text-green-bright transition-colors navigation-menu-link","aria-label":"Join us on Discord",role:"menuitem",children:"Discord"})]})},st=({onClick:e})=>{const r=x(),n=e=>r.pathname===e?"text-green-bright":"text-white/80",i=(t,s,r=!1)=>{if(r){t.preventDefault();const e=s.replace("#",""),r=document.getElementById(e);if(r){const e=r.offsetTop;window.scrollTo({top:e-80,behavior:"smooth"})}}e&&e()};return t.jsxs("div",{className:"md:hidden flex flex-col space-y-4",children:[t.jsxs("div",{className:"space-y-1",children:[t.jsxs("h3",{className:"text-xs uppercase text-green-muted/70 font-mono tracking-wider mb-2 flex items-center",children:[t.jsx(l,{className:"h-3 w-3 mr-1"}),"Company"]}),Ze[0].items.map((e=>t.jsx(ie.div,{whileTap:{scale:.97},className:"w-full",children:t.jsxs(s,{to:e.href,onClick:t=>i(t,e.href),className:`block py-2.5 ${n(e.href)} text-base hover:text-green-bright transition-colors duration-300 hover:pl-2 border-l-2 border-transparent hover:border-green-muted/50`,children:[t.jsxs("div",{className:"flex items-center",children:[e.icon&&t.jsx("span",{className:"mr-2",children:e.icon}),e.label]}),e.description&&t.jsx("p",{className:"text-xs text-white/60 mt-0.5 ml-6",children:e.description})]})},e.href)))]}),t.jsxs("div",{className:"space-y-1",children:[t.jsxs("h3",{className:"text-xs uppercase text-green-muted/70 font-mono tracking-wider mb-2 flex items-center",children:[t.jsx(u,{className:"h-3 w-3 mr-1"}),"Services"]}),Xe[0].items.map((e=>t.jsx(ie.div,{whileTap:{scale:.97},className:"w-full",children:t.jsxs(s,{to:e.href,onClick:t=>i(t,e.href),className:`block py-2.5 ${n(e.href)} text-base hover:text-green-bright transition-colors duration-300 hover:pl-2 border-l-2 border-transparent hover:border-green-muted/50`,children:[t.jsxs("div",{className:"flex items-center",children:[e.icon&&t.jsx("span",{className:"mr-2",children:e.icon}),e.label,e.badge&&t.jsx("span",{className:"ml-2 text-xs bg-orange-600/20 text-orange-300 px-1.5 py-0.5 rounded",children:e.badge}),e.isNew&&t.jsx("span",{className:"ml-2 bg-green-bright text-black text-xs px-1.5 py-0.5 rounded-full font-bold",children:"NEW"})]}),e.description&&t.jsx("p",{className:"text-xs text-white/60 mt-0.5 ml-6",children:e.description})]})},e.href)))]}),t.jsxs("div",{className:"space-y-1",children:[t.jsxs("h3",{className:"text-xs uppercase text-green-muted/70 font-mono tracking-wider mb-2 flex items-center",children:[t.jsx(h,{className:"h-3 w-3 mr-1"}),"Products"]}),Qe[0].items.map((e=>t.jsx(ie.div,{whileTap:{scale:.97},className:"w-full",children:t.jsxs(s,{to:e.href,onClick:t=>i(t,e.href),className:`block py-2.5 ${n(e.href)} text-base hover:text-green-bright transition-colors duration-300 hover:pl-2 border-l-2 border-transparent hover:border-green-muted/50`,children:[t.jsxs("div",{className:"flex items-center",children:[e.icon&&t.jsx("span",{className:"mr-2",children:e.icon}),e.label,e.isNew&&t.jsx("span",{className:"ml-2 bg-green-bright text-black text-xs px-1.5 py-0.5 rounded-full font-bold",children:"NEW"})]}),e.description&&t.jsx("p",{className:"text-xs text-white/60 mt-0.5 ml-6",children:e.description})]})},e.href)))]}),t.jsxs("div",{className:"space-y-1",children:[t.jsxs("h3",{className:"text-xs uppercase text-green-muted/70 font-mono tracking-wider mb-2 flex items-center",children:[t.jsx(c,{className:"h-3 w-3 mr-1"}),"Portfolio"]}),t.jsx(ie.div,{whileTap:{scale:.97},className:"w-full",children:t.jsxs(s,{to:"/portfolio",onClick:e=>i(e,"/portfolio"),className:`block py-2.5 ${n("/portfolio")} text-base hover:text-green-bright transition-colors duration-300 hover:pl-2 border-l-2 border-transparent hover:border-green-muted/50`,children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(c,{className:"mr-2 h-4 w-4"}),"Portfolio"]}),t.jsx("p",{className:"text-xs text-white/60 mt-0.5 ml-6",children:"Our successful security implementations"})]})})]}),t.jsxs("div",{className:"space-y-1",children:[t.jsxs("h3",{className:"text-xs uppercase text-green-muted/70 font-mono tracking-wider mb-2 flex items-center",children:[t.jsx(m,{className:"h-3 w-3 mr-1"}),"Resources"]}),et.flatMap((e=>e.items)).map((e=>t.jsx(ie.div,{whileTap:{scale:.97},className:"w-full",children:t.jsxs(s,{to:e.href,onClick:t=>i(t,e.href),className:`block py-2.5 ${n(e.href)} text-base hover:text-green-bright transition-colors duration-300 hover:pl-2 border-l-2 border-transparent hover:border-green-muted/50`,children:[t.jsxs("div",{className:"flex items-center",children:[e.icon&&t.jsx("span",{className:"mr-2",children:e.icon}),e.label,e.badge&&t.jsx("span",{className:"ml-2 text-xs bg-orange-600/20 text-orange-300 px-1.5 py-0.5 rounded",children:e.badge})]}),e.description&&t.jsx("p",{className:"text-xs text-white/60 mt-0.5 ml-6",children:e.description})]})},e.href)))]}),t.jsxs("div",{className:"space-y-1",children:[t.jsxs("h3",{className:"text-xs uppercase text-green-muted/70 font-mono tracking-wider mb-2 flex items-center",children:[t.jsx(b,{className:"h-3 w-3 mr-1"}),"Support"]}),t.jsx(ie.div,{whileTap:{scale:.97},className:"w-full",children:t.jsxs(s,{to:"/contact",onClick:e=>i(e,"/contact"),className:`block py-2.5 ${n("/contact")} text-base hover:text-green-bright transition-colors duration-300 hover:pl-2 border-l-2 border-transparent hover:border-green-muted/50`,children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(b,{className:"mr-2 h-4 w-4"}),"Contact"]}),t.jsx("p",{className:"text-xs text-white/60 mt-0.5 ml-6",children:"Get in touch with our security experts"})]})})]}),t.jsxs("div",{className:"space-y-1",children:[t.jsxs("h3",{className:"text-xs uppercase text-green-muted/70 font-mono tracking-wider mb-2 flex items-center",children:[t.jsx(y,{className:"h-3 w-3 mr-1"}),"Community"]}),t.jsx(ie.div,{whileTap:{scale:.97},className:"w-full",children:t.jsxs("a",{href:"https://discord.gg/MQ4B9jT4hU",target:"_blank",rel:"noopener noreferrer",className:"block py-2.5 text-white/80 text-base hover:text-green-bright transition-colors duration-300 hover:pl-2 border-l-2 border-transparent hover:border-green-muted/50","aria-label":"Join us on Discord",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(y,{className:"mr-2 h-4 w-4"}),"Discord"]}),t.jsx("p",{className:"text-xs text-white/60 mt-0.5 ml-6",children:"Join our cybersecurity community"})]})})]})]})},rt=({onClick:e})=>t.jsxs(t.Fragment,{children:[t.jsx(tt,{}),t.jsx(st,{onClick:e})]});rt.displayName="NavLinks";const nt=({isOpen:e,onClick:s})=>t.jsx("button",{className:"md:hidden p-2 text-white hover:text-green-bright transition-colors",onClick:s,"aria-label":e?"Close menu":"Open menu","aria-expanded":e,"aria-controls":"mobile-menu",type:"button",children:e?t.jsx(v,{className:"h-6 w-6"}):t.jsx(w,{className:"h-6 w-6"})}),it={},at=function(e,t,s){let r=Promise.resolve();if(t&&t.length>0){document.getElementsByTagName("link");const e=document.querySelector("meta[property=csp-nonce]"),s=e?.nonce||e?.getAttribute("nonce");r=Promise.allSettled(t.map((e=>{if((e=function(e){return"/"+e}(e))in it)return;it[e]=!0;const t=e.endsWith(".css"),r=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${r}`))return;const n=document.createElement("link");return n.rel=t?"stylesheet":"modulepreload",t||(n.as="script"),n.crossOrigin="",n.href=e,s&&n.setAttribute("nonce",s),document.head.appendChild(n),t?new Promise(((t,s)=>{n.addEventListener("load",t),n.addEventListener("error",(()=>s(new Error(`Unable to preload CSS for ${e}`))))})):void 0})))}function n(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return r.then((t=>{for(const e of t||[])"rejected"===e.status&&n(e.reason);return e().catch(n)}))};class ot extends Error{constructor(e,t="FunctionsError",s){super(e),this.name=t,this.context=s}}class lt extends ot{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class ct extends ot{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class dt extends ot{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var ut;!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(ut||(ut={}));var ht=function(e,t,s,r){return new(s||(s=Promise))((function(n,i){function a(e){try{l(r.next(e))}catch(t){i(t)}}function o(e){try{l(r.throw(e))}catch(t){i(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(a,o)}l((r=r.apply(e,t||[])).next())}))};class mt{constructor(e,{headers:t={},customFetch:s,region:r=ut.Any}={}){this.url=e,this.headers=t,this.region=r,this.fetch=(e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>at((async()=>{const{default:e}=await Promise.resolve().then((()=>Tt));return{default:e}}),void 0).then((({default:t})=>t(...e))):fetch),(...e)=>t(...e)})(s)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var s;return ht(this,void 0,void 0,(function*(){try{const{headers:r,method:n,body:i}=t;let a,o={},{region:l}=t;l||(l=this.region),l&&"any"!==l&&(o["x-region"]=l),i&&(r&&!Object.prototype.hasOwnProperty.call(r,"Content-Type")||!r)&&("undefined"!=typeof Blob&&i instanceof Blob||i instanceof ArrayBuffer?(o["Content-Type"]="application/octet-stream",a=i):"string"==typeof i?(o["Content-Type"]="text/plain",a=i):"undefined"!=typeof FormData&&i instanceof FormData?a=i:(o["Content-Type"]="application/json",a=JSON.stringify(i)));const c=yield this.fetch(`${this.url}/${e}`,{method:n||"POST",headers:Object.assign(Object.assign(Object.assign({},o),this.headers),r),body:a}).catch((e=>{throw new lt(e)})),d=c.headers.get("x-relay-error");if(d&&"true"===d)throw new ct(c);if(!c.ok)throw new dt(c);let u,h=(null!==(s=c.headers.get("Content-Type"))&&void 0!==s?s:"text/plain").split(";")[0].trim();return u="application/json"===h?yield c.json():"application/octet-stream"===h?yield c.blob():"text/event-stream"===h?c:"multipart/form-data"===h?yield c.formData():yield c.text(),{data:u,error:null}}catch(r){return{data:null,error:r}}}))}}var pt={},ft={},gt={},xt={},bt={},yt={},vt=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}();const wt=vt.fetch,jt=vt.fetch.bind(vt),Nt=vt.Headers,kt=vt.Request,_t=vt.Response,Tt=Object.freeze(Object.defineProperty({__proto__:null,Headers:Nt,Request:kt,Response:_t,default:jt,fetch:wt},Symbol.toStringTag,{value:"Module"})),St=j(Tt);var Et={};Object.defineProperty(Et,"__esModule",{value:!0});let At=class extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}};Et.default=At;var Ct=N&&N.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(yt,"__esModule",{value:!0});const Pt=Ct(St),Ot=Ct(Et);yt.default=class{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=Pt.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let s=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then((async e=>{var t,s,r;let n=null,i=null,a=null,o=e.status,l=e.statusText;if(e.ok){if("HEAD"!==this.method){const t=await e.text();""===t||(i="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}const r=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),c=null===(s=e.headers.get("content-range"))||void 0===s?void 0:s.split("/");r&&c&&c.length>1&&(a=parseInt(c[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(i)&&(i.length>1?(n={code:"PGRST116",details:`Results contain ${i.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},i=null,a=null,o=406,l="Not Acceptable"):i=1===i.length?i[0]:null)}else{const t=await e.text();try{n=JSON.parse(t),Array.isArray(n)&&404===e.status&&(i=[],n=null,o=200,l="OK")}catch(c){404===e.status&&""===t?(o=204,l="No Content"):n={message:t}}if(n&&this.isMaybeSingle&&(null===(r=null==n?void 0:n.details)||void 0===r?void 0:r.includes("0 rows"))&&(n=null,o=200,l="OK"),n&&this.shouldThrowOnError)throw new Ot.default(n)}return{error:n,data:i,count:a,status:o,statusText:l}}));return this.shouldThrowOnError||(s=s.catch((e=>{var t,s,r;return{error:{message:`${null!==(t=null==e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!==(s=null==e?void 0:e.stack)&&void 0!==s?s:""}`,hint:"",code:`${null!==(r=null==e?void 0:e.code)&&void 0!==r?r:""}`},data:null,count:null,status:0,statusText:""}}))),s.then(e,t)}returns(){return this}overrideTypes(){return this}};var Rt=N&&N.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(bt,"__esModule",{value:!0});const It=Rt(yt);let Lt=class extends It.default{select(e){let t=!1;const s=(null!=e?e:"*").split("").map((e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e))).join("");return this.url.searchParams.set("select",s),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:s,foreignTable:r,referencedTable:n=r}={}){const i=n?`${n}.order`:"order",a=this.url.searchParams.get(i);return this.url.searchParams.set(i,`${a?`${a},`:""}${e}.${t?"asc":"desc"}${void 0===s?"":s?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:s=t}={}){const r=void 0===s?"limit":`${s}.limit`;return this.url.searchParams.set(r,`${e}`),this}range(e,t,{foreignTable:s,referencedTable:r=s}={}){const n=void 0===r?"offset":`${r}.offset`,i=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(n,`${e}`),this.url.searchParams.set(i,""+(t-e+1)),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:s=!1,buffers:r=!1,wal:n=!1,format:i="text"}={}){var a;const o=[e?"analyze":null,t?"verbose":null,s?"settings":null,r?"buffers":null,n?"wal":null].filter(Boolean).join("|"),l=null!==(a=this.headers.Accept)&&void 0!==a?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${i}; for="${l}"; options=${o};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};bt.default=Lt;var $t=N&&N.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(xt,"__esModule",{value:!0});const Dt=$t(bt);let Mt=class extends Dt.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){const s=Array.from(new Set(t)).map((e=>"string"==typeof e&&new RegExp("[,()]").test(e)?`"${e}"`:`${e}`)).join(",");return this.url.searchParams.append(e,`in.(${s})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:s,type:r}={}){let n="";"plain"===r?n="pl":"phrase"===r?n="ph":"websearch"===r&&(n="w");const i=void 0===s?"":`(${s})`;return this.url.searchParams.append(e,`${n}fts${i}.${t}`),this}match(e){return Object.entries(e).forEach((([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)})),this}not(e,t,s){return this.url.searchParams.append(e,`not.${t}.${s}`),this}or(e,{foreignTable:t,referencedTable:s=t}={}){const r=s?`${s}.or`:"or";return this.url.searchParams.append(r,`(${e})`),this}filter(e,t,s){return this.url.searchParams.append(e,`${t}.${s}`),this}};xt.default=Mt;var Ut=N&&N.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(gt,"__esModule",{value:!0});const Bt=Ut(xt);gt.default=class{constructor(e,{headers:t={},schema:s,fetch:r}){this.url=e,this.headers=t,this.schema=s,this.fetch=r}select(e,{head:t=!1,count:s}={}){const r=t?"HEAD":"GET";let n=!1;const i=(null!=e?e:"*").split("").map((e=>/\s/.test(e)&&!n?"":('"'===e&&(n=!n),e))).join("");return this.url.searchParams.set("select",i),s&&(this.headers.Prefer=`count=${s}`),new Bt.default({method:r,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:s=!0}={}){const r=[];if(this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),s||r.push("missing=default"),this.headers.Prefer=r.join(","),Array.isArray(e)){const t=e.reduce(((e,t)=>e.concat(Object.keys(t))),[]);if(t.length>0){const e=[...new Set(t)].map((e=>`"${e}"`));this.url.searchParams.set("columns",e.join(","))}}return new Bt.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:s=!1,count:r,defaultToNull:n=!0}={}){const i=[`resolution=${s?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&i.push(this.headers.Prefer),r&&i.push(`count=${r}`),n||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(e)){const t=e.reduce(((e,t)=>e.concat(Object.keys(t))),[]);if(t.length>0){const e=[...new Set(t)].map((e=>`"${e}"`));this.url.searchParams.set("columns",e.join(","))}}return new Bt.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){const s=[];return this.headers.Prefer&&s.push(this.headers.Prefer),t&&s.push(`count=${t}`),this.headers.Prefer=s.join(","),new Bt.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){const t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new Bt.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};var zt={},Ft={};Object.defineProperty(Ft,"__esModule",{value:!0}),Ft.version=void 0,Ft.version="0.0.0-automated",Object.defineProperty(zt,"__esModule",{value:!0}),zt.DEFAULT_HEADERS=void 0;const qt=Ft;zt.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${qt.version}`};var Vt=N&&N.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ft,"__esModule",{value:!0});const Ht=Vt(gt),Wt=Vt(xt),Kt=zt;ft.default=class e{constructor(e,{headers:t={},schema:s,fetch:r}={}){this.url=e,this.headers=Object.assign(Object.assign({},Kt.DEFAULT_HEADERS),t),this.schemaName=s,this.fetch=r}from(e){const t=new URL(`${this.url}/${e}`);return new Ht.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new e(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(e,t={},{head:s=!1,get:r=!1,count:n}={}){let i;const a=new URL(`${this.url}/rpc/${e}`);let o;s||r?(i=s?"HEAD":"GET",Object.entries(t).filter((([e,t])=>void 0!==t)).map((([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`])).forEach((([e,t])=>{a.searchParams.append(e,t)}))):(i="POST",o=t);const l=Object.assign({},this.headers);return n&&(l.Prefer=`count=${n}`),new Wt.default({method:i,url:a,headers:l,schema:this.schemaName,body:o,fetch:this.fetch,allowEmpty:!1})}};var Gt=N&&N.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(pt,"__esModule",{value:!0}),pt.PostgrestError=pt.PostgrestBuilder=pt.PostgrestTransformBuilder=pt.PostgrestFilterBuilder=pt.PostgrestQueryBuilder=pt.PostgrestClient=void 0;const Jt=Gt(ft);pt.PostgrestClient=Jt.default;const Yt=Gt(gt);pt.PostgrestQueryBuilder=Yt.default;const Zt=Gt(xt);pt.PostgrestFilterBuilder=Zt.default;const Xt=Gt(bt);pt.PostgrestTransformBuilder=Xt.default;const Qt=Gt(yt);pt.PostgrestBuilder=Qt.default;const es=Gt(Et);pt.PostgrestError=es.default;var ts=pt.default={PostgrestClient:Jt.default,PostgrestQueryBuilder:Yt.default,PostgrestFilterBuilder:Zt.default,PostgrestTransformBuilder:Xt.default,PostgrestBuilder:Qt.default,PostgrestError:es.default};const{PostgrestClient:ss,PostgrestQueryBuilder:rs,PostgrestFilterBuilder:ns,PostgrestTransformBuilder:is,PostgrestBuilder:as,PostgrestError:os}=ts,ls={"X-Client-Info":"realtime-js/2.11.2"};var cs,ds,us,hs,ms,ps;!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(cs||(cs={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(ds||(ds={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(us||(us={})),function(e){e.websocket="websocket"}(hs||(hs={})),function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(ms||(ms={}));class fs{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t("string"==typeof e?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),s=new TextDecoder;return this._decodeBroadcast(e,t,s)}_decodeBroadcast(e,t,s){const r=t.getUint8(1),n=t.getUint8(2);let i=this.HEADER_LENGTH+2;const a=s.decode(e.slice(i,i+r));i+=r;const o=s.decode(e.slice(i,i+n));i+=n;return{ref:null,topic:a,event:o,payload:JSON.parse(s.decode(e.slice(i,e.byteLength)))}}}class gs{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout((()=>{this.tries=this.tries+1,this.callback()}),this.timerCalc(this.tries+1))}}!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(ps||(ps={}));const xs=(e,t,s={})=>{var r;const n=null!==(r=s.skipTypes)&&void 0!==r?r:[];return Object.keys(t).reduce(((s,r)=>(s[r]=bs(r,e,t,n),s)),{})},bs=(e,t,s,r)=>{const n=t.find((t=>t.name===e)),i=null==n?void 0:n.type,a=s[e];return i&&!r.includes(i)?ys(i,a):vs(a)},ys=(e,t)=>{if("_"===e.charAt(0)){const s=e.slice(1,e.length);return ks(t,s)}switch(e){case ps.bool:return ws(t);case ps.float4:case ps.float8:case ps.int2:case ps.int4:case ps.int8:case ps.numeric:case ps.oid:return js(t);case ps.json:case ps.jsonb:return Ns(t);case ps.timestamp:return _s(t);case ps.abstime:case ps.date:case ps.daterange:case ps.int4range:case ps.int8range:case ps.money:case ps.reltime:case ps.text:case ps.time:case ps.timestamptz:case ps.timetz:case ps.tsrange:case ps.tstzrange:default:return vs(t)}},vs=e=>e,ws=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},js=e=>{if("string"==typeof e){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},Ns=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(t){return e}return e},ks=(e,t)=>{if("string"!=typeof e)return e;const s=e.length-1,r=e[s];if("{"===e[0]&&"}"===r){let r;const i=e.slice(1,s);try{r=JSON.parse("["+i+"]")}catch(n){r=i?i.split(","):[]}return r.map((e=>ys(t,e)))}return e},_s=e=>"string"==typeof e?e.replace(" ","T"):e,Ts=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class Ss{constructor(e,t,s={},r=1e4){this.channel=e,this.event=t,this.payload=s,this.timeout=r,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var s;return this._hasReceived(e)&&t(null===(s=this.receivedResp)||void 0===s?void 0:s.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);this.channel._on(this.refEvent,{},(e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)})),this.timeoutTimer=setTimeout((()=>{this.trigger("timeout",{})}),this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter((t=>t.status===e)).forEach((e=>e.callback(t)))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var Es,As,Cs,Ps;!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(Es||(Es={}));class Os{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const s=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(s.state,{},(e=>{const{onJoin:t,onLeave:s,onSync:r}=this.caller;this.joinRef=this.channel._joinRef(),this.state=Os.syncState(this.state,e,t,s),this.pendingDiffs.forEach((e=>{this.state=Os.syncDiff(this.state,e,t,s)})),this.pendingDiffs=[],r()})),this.channel._on(s.diff,{},(e=>{const{onJoin:t,onLeave:s,onSync:r}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=Os.syncDiff(this.state,e,t,s),r())})),this.onJoin(((e,t,s)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:s})})),this.onLeave(((e,t,s)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:s})})),this.onSync((()=>{this.channel._trigger("presence",{event:"sync"})}))}static syncState(e,t,s,r){const n=this.cloneDeep(e),i=this.transformState(t),a={},o={};return this.map(n,((e,t)=>{i[e]||(o[e]=t)})),this.map(i,((e,t)=>{const s=n[e];if(s){const r=t.map((e=>e.presence_ref)),n=s.map((e=>e.presence_ref)),i=t.filter((e=>n.indexOf(e.presence_ref)<0)),l=s.filter((e=>r.indexOf(e.presence_ref)<0));i.length>0&&(a[e]=i),l.length>0&&(o[e]=l)}else a[e]=t})),this.syncDiff(n,{joins:a,leaves:o},s,r)}static syncDiff(e,t,s,r){const{joins:n,leaves:i}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return s||(s=()=>{}),r||(r=()=>{}),this.map(n,((t,r)=>{var n;const i=null!==(n=e[t])&&void 0!==n?n:[];if(e[t]=this.cloneDeep(r),i.length>0){const s=e[t].map((e=>e.presence_ref)),r=i.filter((e=>s.indexOf(e.presence_ref)<0));e[t].unshift(...r)}s(t,i,r)})),this.map(i,((t,s)=>{let n=e[t];if(!n)return;const i=s.map((e=>e.presence_ref));n=n.filter((e=>i.indexOf(e.presence_ref)<0)),e[t]=n,r(t,n,s),0===n.length&&delete e[t]})),e}static map(e,t){return Object.getOwnPropertyNames(e).map((s=>t(s,e[s])))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce(((t,s)=>{const r=e[s];return t[s]="metas"in r?r.metas.map((e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e))):r,t}),{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(As||(As={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(Cs||(Cs={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(Ps||(Ps={}));class Rs{constructor(e,t={config:{}},s){this.topic=e,this.params=t,this.socket=s,this.bindings={},this.state=ds.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new Ss(this,us.join,this.params,this.timeout),this.rejoinTimer=new gs((()=>this._rejoinUntilConnected()),this.socket.reconnectAfterMs),this.joinPush.receive("ok",(()=>{this.state=ds.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach((e=>e.send())),this.pushBuffer=[]})),this._onClose((()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=ds.closed,this.socket._remove(this)})),this._onError((e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=ds.errored,this.rejoinTimer.scheduleTimeout())})),this.joinPush.receive("timeout",(()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=ds.errored,this.rejoinTimer.scheduleTimeout())})),this._on(us.reply,{},((e,t)=>{this._trigger(this._replyEventName(t),e)})),this.presence=new Os(this),this.broadcastEndpointURL=Ts(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var s,r;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{const{config:{broadcast:n,presence:i,private:a}}=this.params;this._onError((t=>null==e?void 0:e(Ps.CHANNEL_ERROR,t))),this._onClose((()=>null==e?void 0:e(Ps.CLOSED)));const o={},l={broadcast:n,presence:i,postgres_changes:null!==(r=null===(s=this.bindings.postgres_changes)||void 0===s?void 0:s.map((e=>e.filter)))&&void 0!==r?r:[],private:a};this.socket.accessTokenValue&&(o.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},o)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",(async({postgres_changes:t})=>{var s;if(this.socket.setAuth(),void 0!==t){const r=this.bindings.postgres_changes,n=null!==(s=null==r?void 0:r.length)&&void 0!==s?s:0,i=[];for(let s=0;s<n;s++){const n=r[s],{filter:{event:a,schema:o,table:l,filter:c}}=n,d=t&&t[s];if(!d||d.event!==a||d.schema!==o||d.table!==l||d.filter!==c)return this.unsubscribe(),void(null==e||e(Ps.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes")));i.push(Object.assign(Object.assign({},n),{id:d.id}))}return this.bindings.postgres_changes=i,void(e&&e(Ps.SUBSCRIBED))}null==e||e(Ps.SUBSCRIBED)})).receive("error",(t=>{null==e||e(Ps.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(t).join(", ")||"error")))})).receive("timeout",(()=>{null==e||e(Ps.TIMED_OUT)}))}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,s){return this._on(e,t,s)}async send(e,t={}){var s,r;if(this._canPush()||"broadcast"!==e.type)return new Promise((s=>{var r,n,i;const a=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(i=null===(n=null===(r=this.params)||void 0===r?void 0:r.config)||void 0===n?void 0:n.broadcast)||void 0===i?void 0:i.ack)||s("ok"),a.receive("ok",(()=>s("ok"))),a.receive("error",(()=>s("error"))),a.receive("timeout",(()=>s("timed out")))}));{const{event:i,payload:a}=e,o={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:a,private:this.private}]})};try{const e=await this._fetchWithTimeout(this.broadcastEndpointURL,o,null!==(s=t.timeout)&&void 0!==s?s:this.timeout);return await(null===(r=e.body)||void 0===r?void 0:r.cancel()),e.ok?"ok":"error"}catch(n){return"AbortError"===n.name?"timed out":"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=ds.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(us.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise((s=>{const r=new Ss(this,us.leave,{},e);r.receive("ok",(()=>{t(),s("ok")})).receive("timeout",(()=>{t(),s("timed out")})).receive("error",(()=>{s("error")})),r.send(),this._canPush()||r.trigger("ok",{})}))}async _fetchWithTimeout(e,t,s){const r=new AbortController,n=setTimeout((()=>r.abort()),s),i=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:r.signal}));return clearTimeout(n),i}_push(e,t,s=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let r=new Ss(this,e,t,s);return this._canPush()?r.send():(r.startTimeout(),this.pushBuffer.push(r)),r}_onMessage(e,t,s){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,s){var r,n;const i=e.toLocaleLowerCase(),{close:a,error:o,leave:l,join:c}=us;if(s&&[a,o,l,c].indexOf(i)>=0&&s!==this._joinRef())return;let d=this._onMessage(i,t,s);if(t&&!d)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(i)?null===(r=this.bindings.postgres_changes)||void 0===r||r.filter((e=>{var t,s,r;return"*"===(null===(t=e.filter)||void 0===t?void 0:t.event)||(null===(r=null===(s=e.filter)||void 0===s?void 0:s.event)||void 0===r?void 0:r.toLocaleLowerCase())===i})).map((e=>e.callback(d,s))):null===(n=this.bindings[i])||void 0===n||n.filter((e=>{var s,r,n,a,o,l;if(["broadcast","presence","postgres_changes"].includes(i)){if("id"in e){const i=e.id,a=null===(s=e.filter)||void 0===s?void 0:s.event;return i&&(null===(r=t.ids)||void 0===r?void 0:r.includes(i))&&("*"===a||(null==a?void 0:a.toLocaleLowerCase())===(null===(n=t.data)||void 0===n?void 0:n.type.toLocaleLowerCase()))}{const s=null===(o=null===(a=null==e?void 0:e.filter)||void 0===a?void 0:a.event)||void 0===o?void 0:o.toLocaleLowerCase();return"*"===s||s===(null===(l=null==t?void 0:t.event)||void 0===l?void 0:l.toLocaleLowerCase())}}return e.type.toLocaleLowerCase()===i})).map((e=>{if("object"==typeof d&&"ids"in d){const e=d.data,{schema:t,table:s,commit_timestamp:r,type:n,errors:i}=e,a={schema:t,table:s,commit_timestamp:r,eventType:n,new:{},old:{},errors:i};d=Object.assign(Object.assign({},a),this._getPayloadRecords(e))}e.callback(d,s)}))}_isClosed(){return this.state===ds.closed}_isJoined(){return this.state===ds.joined}_isJoining(){return this.state===ds.joining}_isLeaving(){return this.state===ds.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,s){const r=e.toLocaleLowerCase(),n={type:r,filter:t,callback:s};return this.bindings[r]?this.bindings[r].push(n):this.bindings[r]=[n],this}_off(e,t){const s=e.toLocaleLowerCase();return this.bindings[s]=this.bindings[s].filter((e=>{var r;return!((null===(r=e.type)||void 0===r?void 0:r.toLocaleLowerCase())===s&&Rs.isEqual(e.filter,t))})),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e[s]!==t[s])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(us.close,{},e)}_onError(e){this._on(us.error,{},(t=>e(t)))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=ds.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return"INSERT"!==e.type&&"UPDATE"!==e.type||(t.new=xs(e.columns,e.record)),"UPDATE"!==e.type&&"DELETE"!==e.type||(t.old=xs(e.columns,e.old_record)),t}}const Is=()=>{},Ls="undefined"!=typeof WebSocket;class $s{constructor(e,t){var s;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=ls,this.params={},this.timeout=1e4,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=Is,this.conn=null,this.sendBuffer=[],this.serializer=new fs,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>at((async()=>{const{default:e}=await Promise.resolve().then((()=>Tt));return{default:e}}),void 0).then((({default:t})=>t(...e))):fetch),(...e)=>t(...e)},this.endPoint=`${e}/${hs.websocket}`,this.httpEndpoint=Ts(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);const r=null===(s=null==t?void 0:t.params)||void 0===s?void 0:s.apikey;if(r&&(this.accessTokenValue=r,this.apiKey=r),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new gs((async()=>{this.disconnect(),this.connect()}),this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn)if(this.transport)this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});else{if(Ls)return this.conn=new WebSocket(this.endpointURL()),void this.setupConnection();this.conn=new Ds(this.endpointURL(),void 0,{close:()=>{this.conn=null}}),at((async()=>{const{default:e}=await import("./browser-ClODhbXO.js").then((e=>e.b));return{default:e}}),__vite__mapDeps([0,1])).then((({default:e})=>{this.conn=new e(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection()}))}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}async removeChannel(e){const t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){const e=await Promise.all(this.channels.map((e=>e.unsubscribe())));return this.disconnect(),e}log(e,t,s){this.logger(e,t,s)}connectionState(){switch(this.conn&&this.conn.readyState){case cs.connecting:return ms.Connecting;case cs.open:return ms.Open;case cs.closing:return ms.Closing;default:return ms.Closed}}isConnected(){return this.connectionState()===ms.Open}channel(e,t={config:{}}){const s=new Rs(`realtime:${e}`,t,this);return this.channels.push(s),s}push(e){const{topic:t,event:s,payload:r,ref:n}=e,i=()=>{this.encode(e,(e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)}))};this.log("push",`${t} ${s} (${n})`,r),this.isConnected()?i():this.sendBuffer.push(i)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;if(t){let e=null;try{e=JSON.parse(atob(t.split(".")[1]))}catch(s){}if(e&&e.exp){if(!(Math.floor(Date.now()/1e3)-e.exp<0))return this.log("auth",`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${e.exp}`),Promise.reject(`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${e.exp}`)}this.accessTokenValue=t,this.channels.forEach((e=>{t&&e.updateJoinPayload({access_token:t}),e.joinedOnce&&e._isJoined()&&e._push(us.access_token,{access_token:t})}))}}async sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef)return this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),void(null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout"));this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth()}}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach((e=>e())),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find((t=>t.topic===e&&(t._isJoined()||t._isJoining())));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter((t=>t._joinRef()!==e._joinRef()))}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,(e=>{let{topic:t,event:s,payload:r,ref:n}=e;n&&n===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${r.status||""} ${t} ${s} ${n&&"("+n+")"||""}`,r),this.channels.filter((e=>e._isMember(t))).forEach((e=>e._trigger(s,r,n))),this.stateChangeCallbacks.message.forEach((t=>t(e)))}))}async _onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval((()=>this.sendHeartbeat()),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach((e=>e()))}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach((t=>t(e)))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach((t=>t(e)))}_triggerChanError(){this.channels.forEach((e=>e._trigger(us.error)))}_appendParams(e,t){if(0===Object.keys(t).length)return e;const s=e.match(/\?/)?"&":"?";return`${e}${s}${new URLSearchParams(t)}`}_workerObjectUrl(e){let t;if(e)t=e;else{const e=new Blob(['\n  addEventListener("message", (e) => {\n    if (e.data.event === "start") {\n      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);\n    }\n  });'],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class Ds{constructor(e,t,s){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=cs.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=s.close}}class Ms extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function Us(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class Bs extends Ms{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class zs extends Ms{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var Fs=function(e,t,s,r){return new(s||(s=Promise))((function(n,i){function a(e){try{l(r.next(e))}catch(t){i(t)}}function o(e){try{l(r.throw(e))}catch(t){i(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(a,o)}l((r=r.apply(e,t||[])).next())}))};const qs=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>at((async()=>{const{default:e}=await Promise.resolve().then((()=>Tt));return{default:e}}),void 0).then((({default:t})=>t(...e))):fetch),(...e)=>t(...e)},Vs=e=>{if(Array.isArray(e))return e.map((e=>Vs(e)));if("function"==typeof e||e!==Object(e))return e;const t={};return Object.entries(e).forEach((([e,s])=>{const r=e.replace(/([-_][a-z])/gi,(e=>e.toUpperCase().replace(/[-_]/g,"")));t[r]=Vs(s)})),t};var Hs=function(e,t,s,r){return new(s||(s=Promise))((function(n,i){function a(e){try{l(r.next(e))}catch(t){i(t)}}function o(e){try{l(r.throw(e))}catch(t){i(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(a,o)}l((r=r.apply(e,t||[])).next())}))};const Ws=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Ks=(e,t,s)=>Hs(void 0,void 0,void 0,(function*(){const r=yield Fs(void 0,void 0,void 0,(function*(){return"undefined"==typeof Response?(yield at((()=>Promise.resolve().then((()=>Tt))),void 0)).Response:Response}));e instanceof r&&!(null==s?void 0:s.noResolveJson)?e.json().then((s=>{t(new Bs(Ws(s),e.status||500))})).catch((e=>{t(new zs(Ws(e),e))})):t(new zs(Ws(e),e))}));function Gs(e,t,s,r,n,i){return Hs(this,void 0,void 0,(function*(){return new Promise(((a,o)=>{e(s,((e,t,s,r)=>{const n={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?n:(n.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),r&&(n.body=JSON.stringify(r)),Object.assign(Object.assign({},n),s))})(t,r,n,i)).then((e=>{if(!e.ok)throw e;return(null==r?void 0:r.noResolveJson)?e:e.json()})).then((e=>a(e))).catch((e=>Ks(e,o,r)))}))}))}function Js(e,t,s,r){return Hs(this,void 0,void 0,(function*(){return Gs(e,"GET",t,s,r)}))}function Ys(e,t,s,r,n){return Hs(this,void 0,void 0,(function*(){return Gs(e,"POST",t,r,n,s)}))}function Zs(e,t,s,r,n){return Hs(this,void 0,void 0,(function*(){return Gs(e,"DELETE",t,r,n,s)}))}var Xs=function(e,t,s,r){return new(s||(s=Promise))((function(n,i){function a(e){try{l(r.next(e))}catch(t){i(t)}}function o(e){try{l(r.throw(e))}catch(t){i(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(a,o)}l((r=r.apply(e,t||[])).next())}))};const Qs={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},er={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class tr{constructor(e,t={},s,r){this.url=e,this.headers=t,this.bucketId=s,this.fetch=qs(r)}uploadOrUpdate(e,t,s,r){return Xs(this,void 0,void 0,(function*(){try{let n;const i=Object.assign(Object.assign({},er),r);let a=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(i.upsert)});const o=i.metadata;"undefined"!=typeof Blob&&s instanceof Blob?(n=new FormData,n.append("cacheControl",i.cacheControl),o&&n.append("metadata",this.encodeMetadata(o)),n.append("",s)):"undefined"!=typeof FormData&&s instanceof FormData?(n=s,n.append("cacheControl",i.cacheControl),o&&n.append("metadata",this.encodeMetadata(o))):(n=s,a["cache-control"]=`max-age=${i.cacheControl}`,a["content-type"]=i.contentType,o&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(o)))),(null==r?void 0:r.headers)&&(a=Object.assign(Object.assign({},a),r.headers));const l=this._removeEmptyFolders(t),c=this._getFinalPath(l),d=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:e,body:n,headers:a},(null==i?void 0:i.duplex)?{duplex:i.duplex}:{})),u=yield d.json();if(d.ok)return{data:{path:l,id:u.Id,fullPath:u.Key},error:null};return{data:null,error:u}}catch(n){if(Us(n))return{data:null,error:n};throw n}}))}upload(e,t,s){return Xs(this,void 0,void 0,(function*(){return this.uploadOrUpdate("POST",e,t,s)}))}uploadToSignedUrl(e,t,s,r){return Xs(this,void 0,void 0,(function*(){const n=this._removeEmptyFolders(e),i=this._getFinalPath(n),a=new URL(this.url+`/object/upload/sign/${i}`);a.searchParams.set("token",t);try{let e;const t=Object.assign({upsert:er.upsert},r),i=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&s instanceof Blob?(e=new FormData,e.append("cacheControl",t.cacheControl),e.append("",s)):"undefined"!=typeof FormData&&s instanceof FormData?(e=s,e.append("cacheControl",t.cacheControl)):(e=s,i["cache-control"]=`max-age=${t.cacheControl}`,i["content-type"]=t.contentType);const o=yield this.fetch(a.toString(),{method:"PUT",body:e,headers:i}),l=yield o.json();if(o.ok)return{data:{path:n,fullPath:l.Key},error:null};return{data:null,error:l}}catch(o){if(Us(o))return{data:null,error:o};throw o}}))}createSignedUploadUrl(e,t){return Xs(this,void 0,void 0,(function*(){try{let s=this._getFinalPath(e);const r=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(r["x-upsert"]="true");const n=yield Ys(this.fetch,`${this.url}/object/upload/sign/${s}`,{},{headers:r}),i=new URL(this.url+n.url),a=i.searchParams.get("token");if(!a)throw new Ms("No token returned by API");return{data:{signedUrl:i.toString(),path:e,token:a},error:null}}catch(s){if(Us(s))return{data:null,error:s};throw s}}))}update(e,t,s){return Xs(this,void 0,void 0,(function*(){return this.uploadOrUpdate("PUT",e,t,s)}))}move(e,t,s){return Xs(this,void 0,void 0,(function*(){try{return{data:yield Ys(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==s?void 0:s.destinationBucket},{headers:this.headers}),error:null}}catch(r){if(Us(r))return{data:null,error:r};throw r}}))}copy(e,t,s){return Xs(this,void 0,void 0,(function*(){try{return{data:{path:(yield Ys(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==s?void 0:s.destinationBucket},{headers:this.headers})).Key},error:null}}catch(r){if(Us(r))return{data:null,error:r};throw r}}))}createSignedUrl(e,t,s){return Xs(this,void 0,void 0,(function*(){try{let r=this._getFinalPath(e),n=yield Ys(this.fetch,`${this.url}/object/sign/${r}`,Object.assign({expiresIn:t},(null==s?void 0:s.transform)?{transform:s.transform}:{}),{headers:this.headers});const i=(null==s?void 0:s.download)?`&download=${!0===s.download?"":s.download}`:"";return n={signedUrl:encodeURI(`${this.url}${n.signedURL}${i}`)},{data:n,error:null}}catch(r){if(Us(r))return{data:null,error:r};throw r}}))}createSignedUrls(e,t,s){return Xs(this,void 0,void 0,(function*(){try{const r=yield Ys(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),n=(null==s?void 0:s.download)?`&download=${!0===s.download?"":s.download}`:"";return{data:r.map((e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${n}`):null}))),error:null}}catch(r){if(Us(r))return{data:null,error:r};throw r}}))}download(e,t){return Xs(this,void 0,void 0,(function*(){const s=void 0!==(null==t?void 0:t.transform)?"render/image/authenticated":"object",r=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),n=r?`?${r}`:"";try{const t=this._getFinalPath(e),r=yield Js(this.fetch,`${this.url}/${s}/${t}${n}`,{headers:this.headers,noResolveJson:!0});return{data:yield r.blob(),error:null}}catch(i){if(Us(i))return{data:null,error:i};throw i}}))}info(e){return Xs(this,void 0,void 0,(function*(){const t=this._getFinalPath(e);try{const e=yield Js(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:Vs(e),error:null}}catch(s){if(Us(s))return{data:null,error:s};throw s}}))}exists(e){return Xs(this,void 0,void 0,(function*(){const t=this._getFinalPath(e);try{return yield function(e,t,s,r){return Hs(this,void 0,void 0,(function*(){return Gs(e,"HEAD",t,Object.assign(Object.assign({},s),{noResolveJson:!0}),r)}))}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(s){if(Us(s)&&s instanceof zs){const e=s.originalError;if([400,404].includes(null==e?void 0:e.status))return{data:!1,error:s}}throw s}}))}getPublicUrl(e,t){const s=this._getFinalPath(e),r=[],n=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==n&&r.push(n);const i=void 0!==(null==t?void 0:t.transform)?"render/image":"object",a=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==a&&r.push(a);let o=r.join("&");return""!==o&&(o=`?${o}`),{data:{publicUrl:encodeURI(`${this.url}/${i}/public/${s}${o}`)}}}remove(e){return Xs(this,void 0,void 0,(function*(){try{return{data:yield Zs(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(Us(t))return{data:null,error:t};throw t}}))}list(e,t,s){return Xs(this,void 0,void 0,(function*(){try{const r=Object.assign(Object.assign(Object.assign({},Qs),t),{prefix:e||""});return{data:yield Ys(this.fetch,`${this.url}/object/list/${this.bucketId}`,r,{headers:this.headers},s),error:null}}catch(r){if(Us(r))return{data:null,error:r};throw r}}))}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return"undefined"!=typeof Buffer?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const sr={"X-Client-Info":"storage-js/2.7.1"};var rr=function(e,t,s,r){return new(s||(s=Promise))((function(n,i){function a(e){try{l(r.next(e))}catch(t){i(t)}}function o(e){try{l(r.throw(e))}catch(t){i(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(a,o)}l((r=r.apply(e,t||[])).next())}))};class nr{constructor(e,t={},s){this.url=e,this.headers=Object.assign(Object.assign({},sr),t),this.fetch=qs(s)}listBuckets(){return rr(this,void 0,void 0,(function*(){try{return{data:yield Js(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(Us(e))return{data:null,error:e};throw e}}))}getBucket(e){return rr(this,void 0,void 0,(function*(){try{return{data:yield Js(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(Us(t))return{data:null,error:t};throw t}}))}createBucket(e,t={public:!1}){return rr(this,void 0,void 0,(function*(){try{return{data:yield Ys(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(s){if(Us(s))return{data:null,error:s};throw s}}))}updateBucket(e,t){return rr(this,void 0,void 0,(function*(){try{const s=yield function(e,t,s,r,n){return Hs(this,void 0,void 0,(function*(){return Gs(e,"PUT",t,r,n,s)}))}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers});return{data:s,error:null}}catch(s){if(Us(s))return{data:null,error:s};throw s}}))}emptyBucket(e){return rr(this,void 0,void 0,(function*(){try{return{data:yield Ys(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(Us(t))return{data:null,error:t};throw t}}))}deleteBucket(e){return rr(this,void 0,void 0,(function*(){try{return{data:yield Zs(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(Us(t))return{data:null,error:t};throw t}}))}}class ir extends nr{constructor(e,t={},s){super(e,t,s)}from(e){return new tr(this.url,this.headers,e,this.fetch)}}let ar="";ar="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";const or={headers:{"X-Client-Info":`supabase-js-${ar}/2.49.8`}},lr={schema:"public"},cr={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},dr={};var ur=function(e,t,s,r){return new(s||(s=Promise))((function(n,i){function a(e){try{l(r.next(e))}catch(t){i(t)}}function o(e){try{l(r.throw(e))}catch(t){i(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(a,o)}l((r=r.apply(e,t||[])).next())}))};const hr=e=>{let t;return t=e||("undefined"==typeof fetch?jt:fetch),(...e)=>t(...e)},mr=(e,t,s)=>{const r=hr(s),n="undefined"==typeof Headers?Nt:Headers;return(s,i)=>ur(void 0,void 0,void 0,(function*(){var a;const o=null!==(a=yield t())&&void 0!==a?a:e;let l=new n(null==i?void 0:i.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${o}`),r(s,Object.assign(Object.assign({},i),{headers:l}))}))};var pr=function(e,t,s,r){return new(s||(s=Promise))((function(n,i){function a(e){try{l(r.next(e))}catch(t){i(t)}}function o(e){try{l(r.throw(e))}catch(t){i(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(a,o)}l((r=r.apply(e,t||[])).next())}))};const fr="2.69.1",gr=3e4,xr=9e4,br={"X-Client-Info":`gotrue-js/${fr}`},yr="X-Supabase-Api-Version",vr={timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"},wr=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class jr extends Error{constructor(e,t,s){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=s}}function Nr(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class kr extends jr{constructor(e,t,s){super(e,t,s),this.name="AuthApiError",this.status=t,this.code=s}}class _r extends jr{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class Tr extends jr{constructor(e,t,s,r){super(e,s,r),this.name=t,this.status=s}}class Sr extends Tr{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class Er extends Tr{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class Ar extends Tr{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class Cr extends Tr{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Pr extends Tr{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Or extends Tr{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function Rr(e){return Nr(e)&&"AuthRetryableFetchError"===e.name}class Ir extends Tr{constructor(e,t,s){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=s}}class Lr extends Tr{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const $r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********-_".split(""),Dr=" \t\n\r=".split(""),Mr=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<Dr.length;t+=1)e[Dr[t].charCodeAt(0)]=-2;for(let t=0;t<$r.length;t+=1)e[$r[t].charCodeAt(0)]=t;return e})();function Ur(e,t,s){const r=Mr[e];if(!(r>-1)){if(-2===r)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)s(t.queue>>t.queuedBits-8&255),t.queuedBits-=8}function Br(e){const t=[],s=e=>{t.push(String.fromCodePoint(e))},r={utf8seq:0,codepoint:0},n={queue:0,queuedBits:0},i=e=>{!function(e,t,s){if(0===t.utf8seq){if(e<=127)return void s(e);for(let s=1;s<6;s+=1)if(!(e>>7-s&1)){t.utf8seq=s;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else{if(4!==t.utf8seq)throw new Error("Invalid UTF-8 sequence");t.codepoint=7&e}t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&s(t.codepoint)}}(e,r,s)};for(let a=0;a<e.length;a+=1)Ur(e.charCodeAt(a),n,i);return t.join("")}function zr(e,t){if(!(e<=127)){if(e<=2047)return t(192|e>>6),void t(128|63&e);if(e<=65535)return t(224|e>>12),t(128|e>>6&63),void t(128|63&e);if(e<=1114111)return t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),void t(128|63&e);throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}t(e)}function Fr(e){const t=[],s={queue:0,queuedBits:0},r=e=>{t.push(e)};for(let n=0;n<e.length;n+=1)Ur(e.charCodeAt(n),s,r);return new Uint8Array(t)}function qr(e){const t=[];return function(e,t){for(let s=0;s<e.length;s+=1){let r=e.charCodeAt(s);if(r>55295&&r<=56319){const t=1024*(r-55296)&65535;r=65536+(e.charCodeAt(s+1)-56320&65535|t),s+=1}zr(r,t)}}(e,(e=>t.push(e))),new Uint8Array(t)}const Vr=()=>"undefined"!=typeof window&&"undefined"!=typeof document,Hr={tested:!1,writable:!1},Wr=()=>{if(!Vr())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(t){return!1}if(Hr.tested)return Hr.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),Hr.tested=!0,Hr.writable=!0}catch(t){Hr.tested=!0,Hr.writable=!1}return Hr.writable};const Kr=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>at((async()=>{const{default:e}=await Promise.resolve().then((()=>Tt));return{default:e}}),void 0).then((({default:t})=>t(...e))):fetch),(...e)=>t(...e)},Gr=async(e,t,s)=>{await e.setItem(t,JSON.stringify(s))},Jr=async(e,t)=>{const s=await e.getItem(t);if(!s)return null;try{return JSON.parse(s)}catch(r){return s}},Yr=async(e,t)=>{await e.removeItem(t)};class Zr{constructor(){this.promise=new Zr.promiseConstructor(((e,t)=>{this.resolve=e,this.reject=t}))}}function Xr(e){const t=e.split(".");if(3!==t.length)throw new Lr("Invalid JWT structure");for(let s=0;s<t.length;s++)if(!wr.test(t[s]))throw new Lr("JWT not in base64url format");return{header:JSON.parse(Br(t[0])),payload:JSON.parse(Br(t[1])),signature:Fr(t[2]),raw:{header:t[0],payload:t[1]}}}function Qr(e){return("0"+e.toString(16)).substr(-2)}async function en(e){if(!("undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder))return e;const t=await async function(e){const t=(new TextEncoder).encode(e),s=await crypto.subtle.digest("SHA-256",t),r=new Uint8Array(s);return Array.from(r).map((e=>String.fromCharCode(e))).join("")}(e);return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function tn(e,t,s=!1){const r=function(){const e=new Uint32Array(56);if("undefined"==typeof crypto){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********-._~",t=e.length;let s="";for(let r=0;r<56;r++)s+=e.charAt(Math.floor(Math.random()*t));return s}return crypto.getRandomValues(e),Array.from(e,Qr).join("")}();let n=r;s&&(n+="/PASSWORD_RECOVERY"),await Gr(e,`${t}-code-verifier`,n);const i=await en(r);return[i,r===i?"plain":"s256"]}Zr.promiseConstructor=Promise;const sn=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;var rn=function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(s[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(r=Object.getOwnPropertySymbols(e);n<r.length;n++)t.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(s[r[n]]=e[r[n]])}return s};const nn=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),an=[502,503,504];async function on(e){var t,s;if(!("object"==typeof(s=e)&&null!==s&&"status"in s&&"ok"in s&&"json"in s&&"function"==typeof s.json))throw new Or(nn(e),0);if(an.includes(e.status))throw new Or(nn(e),e.status);let r,n;try{r=await e.json()}catch(a){throw new _r(nn(a),a)}const i=function(e){const t=e.headers.get(yr);if(!t)return null;if(!t.match(sn))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(a){return null}}(e);if(i&&i.getTime()>=vr.timestamp&&"object"==typeof r&&r&&"string"==typeof r.code?n=r.code:"object"==typeof r&&r&&"string"==typeof r.error_code&&(n=r.error_code),n){if("weak_password"===n)throw new Ir(nn(r),e.status,(null===(t=r.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===n)throw new Sr}else if("object"==typeof r&&r&&"object"==typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce(((e,t)=>e&&"string"==typeof t),!0))throw new Ir(nn(r),e.status,r.weak_password.reasons);throw new kr(nn(r),e.status||500,n)}async function ln(e,t,s,r){var n;const i=Object.assign({},null==r?void 0:r.headers);i[yr]||(i[yr]=vr.name),(null==r?void 0:r.jwt)&&(i.Authorization=`Bearer ${r.jwt}`);const a=null!==(n=null==r?void 0:r.query)&&void 0!==n?n:{};(null==r?void 0:r.redirectTo)&&(a.redirect_to=r.redirectTo);const o=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=await async function(e,t,s,r,n,i){const a=((e,t,s,r)=>{const n={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?n:(n.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),n.body=JSON.stringify(r),Object.assign(Object.assign({},n),s))})(t,r,n,i);let o;try{o=await e(s,Object.assign({},a))}catch(l){throw new Or(nn(l),0)}o.ok||await on(o);if(null==r?void 0:r.noResolveJson)return o;try{return await o.json()}catch(l){await on(l)}}(e,t,s+o,{headers:i,noResolveJson:null==r?void 0:r.noResolveJson},{},null==r?void 0:r.body);return(null==r?void 0:r.xform)?null==r?void 0:r.xform(l):{data:Object.assign({},l),error:null}}function cn(e){var t;let s=null;var r;(function(e){return e.access_token&&e.refresh_token&&e.expires_in})(e)&&(s=Object.assign({},e),e.expires_at||(s.expires_at=(r=e.expires_in,Math.round(Date.now()/1e3)+r)));return{data:{session:s,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function dn(e){const t=cn(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce(((e,t)=>e&&"string"==typeof t),!0)&&(t.data.weak_password=e.weak_password),t}function un(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function hn(e){return{data:e,error:null}}function mn(e){const{action_link:t,email_otp:s,hashed_token:r,redirect_to:n,verification_type:i}=e,a=rn(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]);return{data:{properties:{action_link:t,email_otp:s,hashed_token:r,redirect_to:n,verification_type:i},user:Object.assign({},a)},error:null}}function pn(e){return e}var fn=function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(s[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(r=Object.getOwnPropertySymbols(e);n<r.length;n++)t.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(s[r[n]]=e[r[n]])}return s};class gn{constructor({url:e="",headers:t={},fetch:s}){this.url=e,this.headers=t,this.fetch=Kr(s),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t="global"){try{return await ln(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(s){if(Nr(s))return{data:null,error:s};throw s}}async inviteUserByEmail(e,t={}){try{return await ln(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:un})}catch(s){if(Nr(s))return{data:{user:null},error:s};throw s}}async generateLink(e){try{const{options:t}=e,s=fn(e,["options"]),r=Object.assign(Object.assign({},s),t);return"newEmail"in s&&(r.new_email=null==s?void 0:s.newEmail,delete r.newEmail),await ln(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:r,headers:this.headers,xform:mn,redirectTo:null==t?void 0:t.redirectTo})}catch(t){if(Nr(t))return{data:{properties:null,user:null},error:t};throw t}}async createUser(e){try{return await ln(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:un})}catch(t){if(Nr(t))return{data:{user:null},error:t};throw t}}async listUsers(e){var t,s,r,n,i,a,o;try{const l={nextPage:null,lastPage:0,total:0},c=await ln(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(s=null===(t=null==e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==s?s:"",per_page:null!==(n=null===(r=null==e?void 0:e.perPage)||void 0===r?void 0:r.toString())&&void 0!==n?n:""},xform:pn});if(c.error)throw c.error;const d=await c.json(),u=null!==(i=c.headers.get("x-total-count"))&&void 0!==i?i:0,h=null!==(o=null===(a=c.headers.get("link"))||void 0===a?void 0:a.split(","))&&void 0!==o?o:[];return h.length>0&&(h.forEach((e=>{const t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),s=JSON.parse(e.split(";")[1].split("=")[1]);l[`${s}Page`]=t})),l.total=parseInt(u)),{data:Object.assign(Object.assign({},d),l),error:null}}catch(l){if(Nr(l))return{data:{users:[]},error:l};throw l}}async getUserById(e){try{return await ln(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:un})}catch(t){if(Nr(t))return{data:{user:null},error:t};throw t}}async updateUserById(e,t){try{return await ln(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:un})}catch(s){if(Nr(s))return{data:{user:null},error:s};throw s}}async deleteUser(e,t=!1){try{return await ln(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:un})}catch(s){if(Nr(s))return{data:{user:null},error:s};throw s}}async _listFactors(e){try{const{data:t,error:s}=await ln(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:s}}catch(t){if(Nr(t))return{data:null,error:t};throw t}}async _deleteFactor(e){try{return{data:await ln(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(t){if(Nr(t))return{data:null,error:t};throw t}}}const xn={getItem:e=>Wr()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{Wr()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{Wr()&&globalThis.localStorage.removeItem(e)}};function bn(e={}){return{getItem:t=>e[t]||null,setItem:(t,s)=>{e[t]=s},removeItem:t=>{delete e[t]}}}const yn=!!(globalThis&&Wr()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"));class vn extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class wn extends vn{}async function jn(e,t,s){const r=new globalThis.AbortController;return t>0&&setTimeout((()=>{r.abort()}),t),await Promise.resolve().then((()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},(async r=>{if(!r){if(0===t)throw new wn(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(yn)try{await globalThis.navigator.locks.query()}catch(n){}return await s()}try{return await s()}finally{}}))))}!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}();const Nn={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:br,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function kn(e,t,s){return await s()}class _n{constructor(e){var t,s;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=_n.nextInstanceID,_n.nextInstanceID+=1,this.instanceID>0&&Vr();const r=Object.assign(Object.assign({},Nn),e);if(this.logDebugMessages=!!r.debug,"function"==typeof r.debug&&(this.logger=r.debug),this.persistSession=r.persistSession,this.storageKey=r.storageKey,this.autoRefreshToken=r.autoRefreshToken,this.admin=new gn({url:r.url,headers:r.headers,fetch:r.fetch}),this.url=r.url,this.headers=r.headers,this.fetch=Kr(r.fetch),this.lock=r.lock||kn,this.detectSessionInUrl=r.detectSessionInUrl,this.flowType=r.flowType,this.hasCustomAuthorizationHeader=r.hasCustomAuthorizationHeader,r.lock?this.lock=r.lock:Vr()&&(null===(t=null===globalThis||void 0===globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=jn:this.lock=kn,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?r.storage?this.storage=r.storage:Wr()?this.storage=xn:(this.memoryStorage={},this.storage=bn(this.memoryStorage)):(this.memoryStorage={},this.storage=bn(this.memoryStorage)),Vr()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(n){}null===(s=this.broadcastChannel)||void 0===s||s.addEventListener("message",(async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)}))}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${fr}) ${(new Date).toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,(async()=>await this._initialize())))()),await this.initializePromise}async _initialize(){var e;try{const t=function(e){const t={},s=new URL(e);if(s.hash&&"#"===s.hash[0])try{new URLSearchParams(s.hash.substring(1)).forEach(((e,s)=>{t[s]=e}))}catch(r){}return s.searchParams.forEach(((e,s)=>{t[s]=e})),t}(window.location.href);let s="none";if(this._isImplicitGrantCallback(t)?s="implicit":await this._isPKCECallback(t)&&(s="pkce"),Vr()&&this.detectSessionInUrl&&"none"!==s){const{data:r,error:n}=await this._getSessionFromURL(t,s);if(n){if(this._debug("#_initialize()","error detecting session from URL",n),function(e){return Nr(e)&&"AuthImplicitGrantRedirectError"===e.name}(n)){const t=null===(e=n.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:n}}return await this._removeSession(),{error:n}}const{session:i,redirectType:a}=r;return this._debug("#_initialize()","detected session in URL",i,"redirect type",a),await this._saveSession(i),setTimeout((async()=>{"recovery"===a?await this._notifyAllSubscribers("PASSWORD_RECOVERY",i):await this._notifyAllSubscribers("SIGNED_IN",i)}),0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(t){return Nr(t)?{error:t}:{error:new _r("Unexpected error during initialization",t)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,s,r;try{const n=await ln(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(s=null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==s?s:{},gotrue_meta_security:{captcha_token:null===(r=null==e?void 0:e.options)||void 0===r?void 0:r.captchaToken}},xform:cn}),{data:i,error:a}=n;if(a||!i)return{data:{user:null,session:null},error:a};const o=i.session,l=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(n){if(Nr(n))return{data:{user:null,session:null},error:n};throw n}}async signUp(e){var t,s,r;try{let n;if("email"in e){const{email:s,password:r,options:i}=e;let a=null,o=null;"pkce"===this.flowType&&([a,o]=await tn(this.storage,this.storageKey)),n=await ln(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==i?void 0:i.emailRedirectTo,body:{email:s,password:r,data:null!==(t=null==i?void 0:i.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken},code_challenge:a,code_challenge_method:o},xform:cn})}else{if(!("phone"in e))throw new Ar("You must provide either an email or phone number and a password");{const{phone:t,password:i,options:a}=e;n=await ln(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:i,data:null!==(s=null==a?void 0:a.data)&&void 0!==s?s:{},channel:null!==(r=null==a?void 0:a.channel)&&void 0!==r?r:"sms",gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:cn})}}const{data:i,error:a}=n;if(a||!i)return{data:{user:null,session:null},error:a};const o=i.session,l=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(n){if(Nr(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithPassword(e){try{let t;if("email"in e){const{email:s,password:r,options:n}=e;t=await ln(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:s,password:r,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},xform:dn})}else{if(!("phone"in e))throw new Ar("You must provide either an email or phone number and a password");{const{phone:s,password:r,options:n}=e;t=await ln(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:s,password:r,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},xform:dn})}}const{data:s,error:r}=t;return r?{data:{user:null,session:null},error:r}:s&&s.session&&s.user?(s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:Object.assign({user:s.user,session:s.session},s.weak_password?{weakPassword:s.weak_password}:null),error:r}):{data:{user:null,session:null},error:new Er}}catch(t){if(Nr(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOAuth(e){var t,s,r,n;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(s=e.options)||void 0===s?void 0:s.scopes,queryParams:null===(r=e.options)||void 0===r?void 0:r.queryParams,skipBrowserRedirect:null===(n=e.options)||void 0===n?void 0:n.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,(async()=>this._exchangeCodeForSession(e)))}async _exchangeCodeForSession(e){const t=await Jr(this.storage,`${this.storageKey}-code-verifier`),[s,r]=(null!=t?t:"").split("/");try{const{data:t,error:n}=await ln(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:s},xform:cn});if(await Yr(this.storage,`${this.storageKey}-code-verifier`),n)throw n;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=r?r:null}),error:n}):{data:{user:null,session:null,redirectType:null},error:new Er}}catch(n){if(Nr(n))return{data:{user:null,session:null,redirectType:null},error:n};throw n}}async signInWithIdToken(e){try{const{options:t,provider:s,token:r,access_token:n,nonce:i}=e,a=await ln(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:s,id_token:r,access_token:n,nonce:i,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:cn}),{data:o,error:l}=a;return l?{data:{user:null,session:null},error:l}:o&&o.session&&o.user?(o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",o.session)),{data:o,error:l}):{data:{user:null,session:null},error:new Er}}catch(t){if(Nr(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOtp(e){var t,s,r,n,i;try{if("email"in e){const{email:r,options:n}=e;let i=null,a=null;"pkce"===this.flowType&&([i,a]=await tn(this.storage,this.storageKey));const{error:o}=await ln(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:r,data:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:{},create_user:null===(s=null==n?void 0:n.shouldCreateUser)||void 0===s||s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:i,code_challenge_method:a},redirectTo:null==n?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){const{phone:t,options:s}=e,{data:a,error:o}=await ln(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!==(r=null==s?void 0:s.data)&&void 0!==r?r:{},create_user:null===(n=null==s?void 0:s.shouldCreateUser)||void 0===n||n,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken},channel:null!==(i=null==s?void 0:s.channel)&&void 0!==i?i:"sms"}});return{data:{user:null,session:null,messageId:null==a?void 0:a.message_id},error:o}}throw new Ar("You must provide either an email or phone number.")}catch(a){if(Nr(a))return{data:{user:null,session:null},error:a};throw a}}async verifyOtp(e){var t,s;try{let r,n;"options"in e&&(r=null===(t=e.options)||void 0===t?void 0:t.redirectTo,n=null===(s=e.options)||void 0===s?void 0:s.captchaToken);const{data:i,error:a}=await ln(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:n}}),redirectTo:r,xform:cn});if(a)throw a;if(!i)throw new Error("An error occurred on token verification.");const o=i.session,l=i.user;return(null==o?void 0:o.access_token)&&(await this._saveSession(o),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(r){if(Nr(r))return{data:{user:null,session:null},error:r};throw r}}async signInWithSSO(e){var t,s,r;try{let n=null,i=null;return"pkce"===this.flowType&&([n,i]=await tn(this.storage,this.storageKey)),await ln(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(s=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==s?s:void 0}),(null===(r=null==e?void 0:e.options)||void 0===r?void 0:r.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:n,code_challenge_method:i}),headers:this.headers,xform:hn})}catch(n){if(Nr(n))return{data:null,error:n};throw n}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._reauthenticate()))}async _reauthenticate(){try{return await this._useSession((async e=>{const{data:{session:t},error:s}=e;if(s)throw s;if(!t)throw new Sr;const{error:r}=await ln(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:r}}))}catch(e){if(Nr(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{const t=`${this.url}/resend`;if("email"in e){const{email:s,type:r,options:n}=e,{error:i}=await ln(this.fetch,"POST",t,{headers:this.headers,body:{email:s,type:r,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},redirectTo:null==n?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:i}}if("phone"in e){const{phone:s,type:r,options:n}=e,{data:i,error:a}=await ln(this.fetch,"POST",t,{headers:this.headers,body:{phone:s,type:r,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}}});return{data:{user:null,session:null,messageId:null==i?void 0:i.message_id},error:a}}throw new Ar("You must provide either an email or phone number and a type")}catch(t){if(Nr(t))return{data:{user:null,session:null},error:t};throw t}}async getSession(){await this.initializePromise;return await this._acquireLock(-1,(async()=>this._useSession((async e=>e))))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){const e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await s}catch(e){}})()),s}return await this.lock(`lock:${this.storageKey}`,e,(async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(t){}})()),await e;this.pendingInLock.length;){const e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}}))}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{const t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",(new Error).stack);try{let e=null;const t=await Jr(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};const s=!!e.expires_at&&1e3*e.expires_at-Date.now()<xr;if(this._debug("#__loadSession()",`session has${s?"":" not"} expired`,"expires_at",e.expires_at),!s){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,s,r)=>(t||"user"!==s||(t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,s,r))})}return{data:{session:e},error:null}}const{session:r,error:n}=await this._callRefreshToken(e.refresh_token);return n?{data:{session:null},error:n}:{data:{session:r},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){if(e)return await this._getUser(e);await this.initializePromise;return await this._acquireLock(-1,(async()=>await this._getUser()))}async _getUser(e){try{return e?await ln(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:un}):await this._useSession((async e=>{var t,s,r;const{data:n,error:i}=e;if(i)throw i;return(null===(t=n.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await ln(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(r=null===(s=n.session)||void 0===s?void 0:s.access_token)&&void 0!==r?r:void 0,xform:un}):{data:{user:null},error:new Sr}}))}catch(t){if(Nr(t))return function(e){return Nr(e)&&"AuthSessionMissingError"===e.name}(t)&&(await this._removeSession(),await Yr(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:t};throw t}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._updateUser(e,t)))}async _updateUser(e,t={}){try{return await this._useSession((async s=>{const{data:r,error:n}=s;if(n)throw n;if(!r.session)throw new Sr;const i=r.session;let a=null,o=null;"pkce"===this.flowType&&null!=e.email&&([a,o]=await tn(this.storage,this.storageKey));const{data:l,error:c}=await ln(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:a,code_challenge_method:o}),jwt:i.access_token,xform:un});if(c)throw c;return i.user=l.user,await this._saveSession(i),await this._notifyAllSubscribers("USER_UPDATED",i),{data:{user:i.user},error:null}}))}catch(s){if(Nr(s))return{data:{user:null},error:s};throw s}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._setSession(e)))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new Sr;const t=Date.now()/1e3;let s=t,r=!0,n=null;const{payload:i}=Xr(e.access_token);if(i.exp&&(s=i.exp,r=s<=t),r){const{session:t,error:s}=await this._callRefreshToken(e.refresh_token);if(s)return{data:{user:null,session:null},error:s};if(!t)return{data:{user:null,session:null},error:null};n=t}else{const{data:r,error:i}=await this._getUser(e.access_token);if(i)throw i;n={access_token:e.access_token,refresh_token:e.refresh_token,user:r.user,token_type:"bearer",expires_in:s-t,expires_at:s},await this._saveSession(n),await this._notifyAllSubscribers("SIGNED_IN",n)}return{data:{user:n.user,session:n},error:null}}catch(t){if(Nr(t))return{data:{session:null,user:null},error:t};throw t}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._refreshSession(e)))}async _refreshSession(e){try{return await this._useSession((async t=>{var s;if(!e){const{data:r,error:n}=t;if(n)throw n;e=null!==(s=r.session)&&void 0!==s?s:void 0}if(!(null==e?void 0:e.refresh_token))throw new Sr;const{session:r,error:n}=await this._callRefreshToken(e.refresh_token);return n?{data:{user:null,session:null},error:n}:r?{data:{user:r.user,session:r},error:null}:{data:{user:null,session:null},error:null}}))}catch(t){if(Nr(t))return{data:{user:null,session:null},error:t};throw t}}async _getSessionFromURL(e,t){try{if(!Vr())throw new Cr("No browser detected.");if(e.error||e.error_description||e.error_code)throw new Cr(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new Pr("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new Cr("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new Pr("No code detected.");const{data:t,error:s}=await this._exchangeCodeForSession(e.code);if(s)throw s;const r=new URL(window.location.href);return r.searchParams.delete("code"),window.history.replaceState(window.history.state,"",r.toString()),{data:{session:t.session,redirectType:null},error:null}}const{provider_token:s,provider_refresh_token:r,access_token:n,refresh_token:i,expires_in:a,expires_at:o,token_type:l}=e;if(!(n&&a&&i&&l))throw new Cr("No session defined in URL");const c=Math.round(Date.now()/1e3),d=parseInt(a);let u=c+d;o&&(u=parseInt(o));const{data:h,error:m}=await this._getUser(n);if(m)throw m;const p={provider_token:s,provider_refresh_token:r,access_token:n,expires_in:d,expires_at:u,refresh_token:i,token_type:l,user:h.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:p,redirectType:e.type},error:null}}catch(s){if(Nr(s))return{data:{session:null,redirectType:null},error:s};throw s}}_isImplicitGrantCallback(e){return Boolean(e.access_token||e.error_description)}async _isPKCECallback(e){const t=await Jr(this.storage,`${this.storageKey}-code-verifier`);return!(!e.code||!t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._signOut(e)))}async _signOut({scope:e}={scope:"global"}){return await this._useSession((async t=>{var s;const{data:r,error:n}=t;if(n)return{error:n};const i=null===(s=r.session)||void 0===s?void 0:s.access_token;if(i){const{error:t}=await this.admin.signOut(i,e);if(t&&(!function(e){return Nr(e)&&"AuthApiError"===e.name}(t)||404!==t.status&&401!==t.status&&403!==t.status))return{error:t}}return"others"!==e&&(await this._removeSession(),await Yr(this.storage,`${this.storageKey}-code-verifier`)),{error:null}}))}onAuthStateChange(e){const t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})),s={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,s),(async()=>{await this.initializePromise,await this._acquireLock(-1,(async()=>{this._emitInitialSession(t)}))})(),{data:{subscription:s}}}async _emitInitialSession(e){return await this._useSession((async t=>{var s,r;try{const{data:{session:r},error:n}=t;if(n)throw n;await(null===(s=this.stateChangeEmitters.get(e))||void 0===s?void 0:s.callback("INITIAL_SESSION",r)),this._debug("INITIAL_SESSION","callback id",e,"session",r)}catch(n){await(null===(r=this.stateChangeEmitters.get(e))||void 0===r?void 0:r.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",n)}}))}async resetPasswordForEmail(e,t={}){let s=null,r=null;"pkce"===this.flowType&&([s,r]=await tn(this.storage,this.storageKey,!0));try{return await ln(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:s,code_challenge_method:r,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(n){if(Nr(n))return{data:null,error:n};throw n}}async getUserIdentities(){var e;try{const{data:t,error:s}=await this.getUser();if(s)throw s;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(t){if(Nr(t))return{data:null,error:t};throw t}}async linkIdentity(e){var t;try{const{data:s,error:r}=await this._useSession((async t=>{var s,r,n,i,a;const{data:o,error:l}=t;if(l)throw l;const c=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null===(s=e.options)||void 0===s?void 0:s.redirectTo,scopes:null===(r=e.options)||void 0===r?void 0:r.scopes,queryParams:null===(n=e.options)||void 0===n?void 0:n.queryParams,skipBrowserRedirect:!0});return await ln(this.fetch,"GET",c,{headers:this.headers,jwt:null!==(a=null===(i=o.session)||void 0===i?void 0:i.access_token)&&void 0!==a?a:void 0})}));if(r)throw r;return Vr()&&!(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)&&window.location.assign(null==s?void 0:s.url),{data:{provider:e.provider,url:null==s?void 0:s.url},error:null}}catch(s){if(Nr(s))return{data:{provider:e.provider,url:null},error:s};throw s}}async unlinkIdentity(e){try{return await this._useSession((async t=>{var s,r;const{data:n,error:i}=t;if(i)throw i;return await ln(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!==(r=null===(s=n.session)||void 0===s?void 0:s.access_token)&&void 0!==r?r:void 0})}))}catch(t){if(Nr(t))return{data:null,error:t};throw t}}async _refreshAccessToken(e){const t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{const n=Date.now();return await(s=async s=>(s>0&&await async function(e){return await new Promise((t=>{setTimeout((()=>t(null)),e)}))}(200*Math.pow(2,s-1)),this._debug(t,"refreshing attempt",s),await ln(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:cn})),r=(e,t)=>{const s=200*Math.pow(2,e);return t&&Rr(t)&&Date.now()+s-n<gr},new Promise(((e,t)=>{(async()=>{for(let i=0;i<1/0;i++)try{const t=await s(i);if(!r(i,null,t))return void e(t)}catch(n){if(!r(i,n))return void t(n)}})()})))}catch(n){if(this._debug(t,"error",n),Nr(n))return{data:{session:null,user:null},error:n};throw n}finally{this._debug(t,"end")}var s,r}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){const s=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",s),Vr()&&!t.skipBrowserRedirect&&window.location.assign(s),{data:{provider:e,url:s},error:null}}async _recoverAndRefresh(){var e;const t="#_recoverAndRefresh()";this._debug(t,"begin");try{const s=await Jr(this.storage,this.storageKey);if(this._debug(t,"session from storage",s),!this._isValidSession(s))return this._debug(t,"session is not valid"),void(null!==s&&await this._removeSession());const r=1e3*(null!==(e=s.expires_at)&&void 0!==e?e:1/0)-Date.now()<xr;if(this._debug(t,`session has${r?"":" not"} expired with margin of 90000s`),r){if(this.autoRefreshToken&&s.refresh_token){const{error:e}=await this._callRefreshToken(s.refresh_token);e&&(Rr(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",s)}catch(s){return void this._debug(t,"error",s)}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,s;if(!e)throw new Sr;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const r=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(r,"begin");try{this.refreshingDeferred=new Zr;const{data:t,error:s}=await this._refreshAccessToken(e);if(s)throw s;if(!t.session)throw new Sr;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);const r={session:t.session,error:null};return this.refreshingDeferred.resolve(r),r}catch(n){if(this._debug(r,"error",n),Nr(n)){const e={session:null,error:n};return Rr(n)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(e),e}throw null===(s=this.refreshingDeferred)||void 0===s||s.reject(n),n}finally{this.refreshingDeferred=null,this._debug(r,"end")}}async _notifyAllSubscribers(e,t,s=!0){const r=`#_notifyAllSubscribers(${e})`;this._debug(r,"begin",t,`broadcast = ${s}`);try{this.broadcastChannel&&s&&this.broadcastChannel.postMessage({event:e,session:t});const r=[],n=Array.from(this.stateChangeEmitters.values()).map((async s=>{try{await s.callback(e,t)}catch(n){r.push(n)}}));if(await Promise.all(n),r.length>0){for(let e=0;e<r.length;e+=1);throw r[0]}}finally{this._debug(r,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await Gr(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await Yr(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&Vr()&&(null===window||void 0===window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const e=setInterval((()=>this._autoRefreshTokenTick()),gr);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout((async()=>{await this.initializePromise,await this._autoRefreshTokenTick()}),0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,(async()=>{try{const t=Date.now();try{return await this._useSession((async e=>{const{data:{session:s}}=e;if(!s||!s.refresh_token||!s.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");const r=Math.floor((1e3*s.expires_at-t)/gr);this._debug("#_autoRefreshTokenTick()",`access token expires in ${r} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),r<=3&&await this._callRefreshToken(s.refresh_token)}))}catch(e){}}finally{this._debug("#_autoRefreshTokenTick()","end")}}))}catch(e){if(!(e.isAcquireTimeout||e instanceof vn))throw e;this._debug("auto refresh token tick lock not available")}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!Vr()||!(null===window||void 0===window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null===window||void 0===window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){}}async _onVisibilityChanged(e){const t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,(async()=>{"visible"===document.visibilityState?await this._recoverAndRefresh():this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting")})))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,s){const r=[`provider=${encodeURIComponent(t)}`];if((null==s?void 0:s.redirectTo)&&r.push(`redirect_to=${encodeURIComponent(s.redirectTo)}`),(null==s?void 0:s.scopes)&&r.push(`scopes=${encodeURIComponent(s.scopes)}`),"pkce"===this.flowType){const[e,t]=await tn(this.storage,this.storageKey),s=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});r.push(s.toString())}if(null==s?void 0:s.queryParams){const e=new URLSearchParams(s.queryParams);r.push(e.toString())}return(null==s?void 0:s.skipBrowserRedirect)&&r.push(`skip_http_redirect=${s.skipBrowserRedirect}`),`${e}?${r.join("&")}`}async _unenroll(e){try{return await this._useSession((async t=>{var s;const{data:r,error:n}=t;return n?{data:null,error:n}:await ln(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null===(s=null==r?void 0:r.session)||void 0===s?void 0:s.access_token})}))}catch(t){if(Nr(t))return{data:null,error:t};throw t}}async _enroll(e){try{return await this._useSession((async t=>{var s,r;const{data:n,error:i}=t;if(i)return{data:null,error:i};const a=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:o,error:l}=await ln(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:null===(s=null==n?void 0:n.session)||void 0===s?void 0:s.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null===(r=null==o?void 0:o.totp)||void 0===r?void 0:r.qr_code)&&(o.totp.qr_code=`data:image/svg+xml;utf-8,${o.totp.qr_code}`),{data:o,error:null})}))}catch(t){if(Nr(t))return{data:null,error:t};throw t}}async _verify(e){return this._acquireLock(-1,(async()=>{try{return await this._useSession((async t=>{var s;const{data:r,error:n}=t;if(n)return{data:null,error:n};const{data:i,error:a}=await ln(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(s=null==r?void 0:r.session)||void 0===s?void 0:s.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+i.expires_in},i)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",i),{data:i,error:a})}))}catch(t){if(Nr(t))return{data:null,error:t};throw t}}))}async _challenge(e){return this._acquireLock(-1,(async()=>{try{return await this._useSession((async t=>{var s;const{data:r,error:n}=t;return n?{data:null,error:n}:await ln(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null===(s=null==r?void 0:r.session)||void 0===s?void 0:s.access_token})}))}catch(t){if(Nr(t))return{data:null,error:t};throw t}}))}async _challengeAndVerify(e){const{data:t,error:s}=await this._challenge({factorId:e.factorId});return s?{data:null,error:s}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){const{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};const s=(null==e?void 0:e.factors)||[],r=s.filter((e=>"totp"===e.factor_type&&"verified"===e.status)),n=s.filter((e=>"phone"===e.factor_type&&"verified"===e.status));return{data:{all:s,totp:r,phone:n},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,(async()=>await this._useSession((async e=>{var t,s;const{data:{session:r},error:n}=e;if(n)return{data:null,error:n};if(!r)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:i}=Xr(r.access_token);let a=null;i.aal&&(a=i.aal);let o=a;(null!==(s=null===(t=r.user.factors)||void 0===t?void 0:t.filter((e=>"verified"===e.status)))&&void 0!==s?s:[]).length>0&&(o="aal2");return{data:{currentLevel:a,nextLevel:o,currentAuthenticationMethods:i.amr||[]},error:null}}))))}async fetchJwk(e,t={keys:[]}){let s=t.keys.find((t=>t.kid===e));if(s)return s;if(s=this.jwks.keys.find((t=>t.kid===e)),s&&this.jwks_cached_at+6e5>Date.now())return s;const{data:r,error:n}=await ln(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(n)throw n;if(!r.keys||0===r.keys.length)throw new Lr("JWKS is empty");if(this.jwks=r,this.jwks_cached_at=Date.now(),s=r.keys.find((t=>t.kid===e)),!s)throw new Lr("No matching signing key found in JWKS");return s}async getClaims(e,t={keys:[]}){try{let s=e;if(!s){const{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};s=e.session.access_token}const{header:r,payload:n,signature:i,raw:{header:a,payload:o}}=Xr(s);if(function(e){if(!e)throw new Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw new Error("JWT has expired")}(n.exp),!r.kid||"HS256"===r.alg||!("crypto"in globalThis)||!("subtle"in globalThis.crypto)){const{error:e}=await this.getUser(s);if(e)throw e;return{data:{claims:n,header:r,signature:i},error:null}}const l=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}(r.alg),c=await this.fetchJwk(r.kid,t),d=await crypto.subtle.importKey("jwk",c,l,!0,["verify"]);if(!(await crypto.subtle.verify(l,d,i,qr(`${a}.${o}`))))throw new Lr("Invalid JWT signature");return{data:{claims:n,header:r,signature:i},error:null}}catch(s){if(Nr(s))return{data:null,error:s};throw s}}}_n.nextInstanceID=0;const Tn=_n;class Sn extends Tn{constructor(e){super(e)}}var En=function(e,t,s,r){return new(s||(s=Promise))((function(n,i){function a(e){try{l(r.next(e))}catch(t){i(t)}}function o(e){try{l(r.throw(e))}catch(t){i(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(a,o)}l((r=r.apply(e,t||[])).next())}))};class An{constructor(e,t,s){var r,n,i;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const a=(o=e).endsWith("/")?o:o+"/";var o;const l=new URL(a);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const c=`sb-${l.hostname.split(".")[0]}-auth-token`,d=function(e,t){var s,r;const{db:n,auth:i,realtime:a,global:o}=e,{db:l,auth:c,realtime:d,global:u}=t,h={db:Object.assign(Object.assign({},l),n),auth:Object.assign(Object.assign({},c),i),realtime:Object.assign(Object.assign({},d),a),global:Object.assign(Object.assign(Object.assign({},u),o),{headers:Object.assign(Object.assign({},null!==(s=null==u?void 0:u.headers)&&void 0!==s?s:{}),null!==(r=null==o?void 0:o.headers)&&void 0!==r?r:{})}),accessToken:()=>pr(this,void 0,void 0,(function*(){return""}))};return e.accessToken?h.accessToken=e.accessToken:delete h.accessToken,h}(null!=s?s:{},{db:lr,realtime:dr,auth:Object.assign(Object.assign({},cr),{storageKey:c}),global:or});this.storageKey=null!==(r=d.auth.storageKey)&&void 0!==r?r:"",this.headers=null!==(n=d.global.headers)&&void 0!==n?n:{},d.accessToken?(this.accessToken=d.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(i=d.auth)&&void 0!==i?i:{},this.headers,d.global.fetch),this.fetch=mr(t,this._getAccessToken.bind(this),d.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},d.realtime)),this.rest=new ss(new URL("rest/v1",l).href,{headers:this.headers,schema:d.db.schema,fetch:this.fetch}),d.accessToken||this._listenForAuthEvents()}get functions(){return new mt(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new ir(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},s={}){return this.rest.rpc(e,t,s)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return En(this,void 0,void 0,(function*(){if(this.accessToken)return yield this.accessToken();const{data:s}=yield this.auth.getSession();return null!==(t=null===(e=s.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:null}))}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:r,storageKey:n,flowType:i,lock:a,debug:o},l,c){const d={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Sn({url:this.authUrl.href,headers:Object.assign(Object.assign({},d),l),storageKey:n,autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:r,flowType:i,lock:a,debug:o,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new $s(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange(((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)}))}_handleTokenChanged(e,t,s){"TOKEN_REFRESHED"!==e&&"SIGNED_IN"!==e||this.changedAccessToken===s?"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0):this.changedAccessToken=s}}const Cn="https://wikngnwwakatokbgvenw.supabase.co",Pn="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NjY0NTQsImV4cCI6MjA2NDE0MjQ1NH0.CCoK3cPN0Pe-AVUgePrxmWvf9qZho0b-prGDilfKmZ4";const On=function(){try{const{url:e,key:t}=function(){try{new URL(Cn)}catch{throw new Error("Invalid Supabase URL format. Please ensure it's a valid URL (e.g., https://project.supabase.co)")}return{url:Cn,key:Pn}}();return((e,t,s)=>new An(e,t,s))(e,t,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}})}catch(e){throw e}}(),Rn=r.createContext(void 0),In=()=>{const e=r.useContext(Rn);if(void 0===e)throw new Error("useAuth must be used within an AuthProvider");return e},Ln=({children:e})=>{const[s,n]=r.useState(null),[i,a]=r.useState(null),[o,l]=r.useState(!0),[c,d]=r.useState(!1),u=async e=>{if(e?.email)try{const{data:t,error:s}=await On.from("assessment_submissions").update({user_id:e.id}).eq("email",e.email).is("user_id",null).select("id");if(s)return;t&&t.length}catch(t){}};r.useEffect((()=>{const{data:{subscription:e}}=On.auth.onAuthStateChange((async(e,t)=>{a(t),n(t?.user??null),!t?.user||"SIGNED_IN"!==e&&"TOKEN_REFRESHED"!==e||setTimeout((()=>{u(t.user)}),0),t?.user||d(!1),l(!1)}));return On.auth.getSession().then((({data:{session:e}})=>{a(e),n(e?.user??null),e?.user&&u(e.user),l(!1)})),()=>e.unsubscribe()}),[]);const h={user:s,session:i,isLoading:o,isAdmin:c,signUp:async(e,t)=>{try{const{data:s,error:r}=await On.auth.getSession();if(r)return{error:new Error("Cannot connect to authentication service. Please try again.")};if(!e||!t)return{error:new Error("Email and password are required")};if(t.length<6)return{error:new Error("Password must be at least 6 characters long")};const n=`${window.location.origin}/`,{data:i,error:a}=await On.auth.signUp({email:e,password:t,options:{emailRedirectTo:n}});return a?{error:a}:{error:null}}catch(s){return s instanceof TypeError&&s.message.includes("fetch")?{error:new Error("Network connection failed. Please check your internet connection and try again.")}:{error:s instanceof Error?s:new Error("An unexpected error occurred during signup")}}},signIn:async(e,t)=>{try{const{data:s,error:r}=await On.auth.getSession();if(r)return{error:new Error("Cannot connect to authentication service. Please try again.")};const{error:n}=await On.auth.signInWithPassword({email:e,password:t});return{error:n}}catch(s){return s instanceof TypeError&&s.message.includes("fetch")?{error:new Error("Network connection failed. Please check your internet connection and try again.")}:{error:s instanceof Error?s:new Error("An unexpected error occurred during signin")}}},signOut:async()=>{try{await On.auth.signOut(),d(!1);const e=window.location.pathname;["/dashboard","/admin"].some((t=>e.startsWith(t)))&&(window.location.href="/")}catch(e){}},checkAdminRole:async()=>{if(!s)return d(!1),!1;try{const{data:e,error:t}=await On.rpc("has_role",{_user_id:s.id,_role:"admin"});if(t)return d(!1),!1;const r=e||!1;return d(r),r}catch(e){return d(!1),!1}}};return t.jsx(Rn.Provider,{value:h,children:e})},$n=()=>{const{user:e,isAdmin:n,checkAdminRole:i}=In(),[a,o]=r.useState(!1);return r.useEffect((()=>{e&&!a?i().finally((()=>{o(!0)})):e||o(!1)}),[e,i,a]),e&&a&&n?t.jsxs(s,{to:"/admin",className:"flex items-center gap-2 text-white hover:text-green-bright transition-colors duration-200",children:[t.jsx(u,{className:"w-4 h-4"}),t.jsx("span",{children:"Admin"})]}):null},Dn=k("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),Mn=r.forwardRef((({className:e,variant:s,size:r,asChild:n=!1,...i},a)=>{const o=n?de:"button";return t.jsx(o,{className:ae(Dn({variant:s,size:r,className:e})),ref:a,...i})}));Mn.displayName="Button";const Un={default:{primary:{text:"Get Free Assessment",action:"assessment",href:"/phishing-assessment"},secondary:{text:"Schedule Consultation",action:"schedule",href:"https://calendly.com/adam-blackveil/30min",external:!0},emergency:{text:"Cyber Attack Hotline",action:"emergency",href:"https://0508hacked.co.nz",external:!0}},contact:{primary:{text:"Schedule Consultation",action:"schedule",href:"https://calendly.com/adam-blackveil/30min",external:!0},secondary:{text:"Take Assessment",action:"assessment",href:"/phishing-assessment"},emergency:{text:"Cyber Attack Hotline",action:"emergency",href:"https://0508hacked.co.nz",external:!0}},assessment:{primary:{text:"Start Assessment",action:"assessment",href:"/phishing-assessment"},secondary:{text:"Contact Us",action:"contact",href:"/contact"},emergency:{text:"Cyber Attack Hotline",action:"emergency",href:"https://0508hacked.co.nz",external:!0}},results:{primary:{text:"Schedule Consultation",action:"schedule",href:"https://calendly.com/adam-blackveil/30min",external:!0},secondary:{text:"View Training Programs",action:"training",href:"/services/phishing-simulation"},emergency:{text:"Cyber Attack Hotline",action:"emergency",href:"https://0508hacked.co.nz",external:!0}}},Bn="DMARC Compliance Required",zn="https://blackvault.co.nz",Fn=({variant:e="default",layout:r="horizontal",showDmarc:n=!1,showEmergency:i=!0,className:a,onAction:o})=>{const l=Un[e],c=(e,n)=>{const i=(e=>{switch(e){case"schedule":return E;case"assessment":return S;case"emergency":return T;default:return _}})(n.action),a=ae("flex items-center gap-2 font-medium transition-all duration-300",{"cyber-button":"primary"===e,"border border-green-muted/50 text-green-bright hover:bg-green-dark/20 bg-transparent":"secondary"===e,"bg-red-600 hover:bg-red-700 text-white":"emergency"===e,"px-3 lg:px-4 py-1.5 lg:py-2 text-xs lg:text-sm":"navbar"===r,"px-4 py-2 text-sm":"horizontal"===r||"vertical"===r,"w-full py-3 text-base justify-center":"mobile"===r}),l=t.jsxs(t.Fragment,{children:[t.jsx(i,{className:ae("h-4 w-4","mobile"===r&&"h-5 w-5")}),t.jsx("span",{children:n.text})]});return n.external?t.jsx("a",{href:n.href,target:"_blank",rel:"noopener noreferrer",className:a,onClick:o,children:l}):t.jsx(Mn,{asChild:!0,className:a,onClick:o,children:t.jsx(s,{to:n.href||"/",children:l})})},d=()=>n?t.jsxs("a",{href:zn,target:"_blank",rel:"noopener noreferrer",className:ae("flex items-center gap-2 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white font-semibold rounded-md transition-all duration-300",{"px-3 lg:px-4 py-1.5 lg:py-2 text-xs lg:text-sm":"navbar"===r,"px-4 py-2 text-sm":"horizontal"===r,"w-full py-3 text-base justify-center":"mobile"===r||"vertical"===r}),onClick:o,children:[t.jsx(g,{className:ae("h-4 w-4","mobile"===r&&"h-5 w-5")}),t.jsx("span",{children:"navbar"===r?"DMARC Mandate 2026":Bn})]}):null;return"mobile"===r||"vertical"===r?t.jsxs("div",{className:ae("flex flex-col space-y-3",a),children:[n&&d(),c("primary",l.primary),c("secondary",l.secondary),i&&c("emergency",l.emergency)]}):"navbar"===r?t.jsxs("div",{className:ae("hidden md:flex items-center space-x-3 lg:space-x-4",a),children:[n&&d(),i&&c("emergency",l.emergency),c("primary",l.primary)]}):t.jsxs("div",{className:ae("flex items-center space-x-4",a),children:[n&&d(),c("primary",l.primary),c("secondary",l.secondary),i&&c("emergency",l.emergency)]})},qn=({isMobile:e=!1,onClick:s})=>t.jsx(Fn,{variant:"default",layout:e?"mobile":"navbar",showDmarc:!0,showEmergency:!0,className:e?"pt-4 xs:pt-5 sm:pt-6":"",onAction:s}),Vn=({isMobile:e=!1,onAction:r})=>{const{user:n,signOut:i}=In(),a=async()=>{await i(),r&&r()};return n?e?t.jsxs("div",{className:"flex flex-col space-y-3",children:[t.jsxs("div",{className:"flex items-center gap-3 text-white/80 p-3 bg-green-dark/10 rounded-lg border border-green-muted/20",children:[t.jsx(A,{className:"w-5 h-5 text-green-bright"}),t.jsxs("div",{className:"flex-1 min-w-0",children:[t.jsx("p",{className:"text-sm font-medium text-white",children:"Signed in as"}),t.jsx("p",{className:"text-xs text-white/70 truncate",children:n.email})]})]}),t.jsxs("div",{className:"flex flex-col space-y-2",children:[t.jsx(s,{to:"/dashboard",onClick:r,children:t.jsxs(Mn,{variant:"outline",className:"w-full border-green-muted/50 text-green-bright hover:bg-green-dark/20 min-h-[44px] justify-start","aria-label":"Go to dashboard",children:[t.jsx(C,{className:"w-5 h-5 mr-3"}),"Dashboard"]})}),t.jsxs(Mn,{onClick:a,variant:"outline",className:"w-full border-green-muted/50 text-green-bright hover:bg-green-dark/20 min-h-[44px] justify-start","aria-label":"Sign out of your account",children:[t.jsx(P,{className:"w-5 h-5 mr-3"}),"Sign Out"]})]})]}):t.jsxs("div",{className:"flex items-center gap-2 lg:gap-3",children:[t.jsxs("div",{className:"hidden lg:flex items-center gap-2 text-white/80",children:[t.jsx(A,{className:"w-4 h-4"}),t.jsx("span",{className:"text-sm truncate max-w-[120px]",children:n.email})]}),t.jsx(s,{to:"/dashboard",children:t.jsxs(Mn,{variant:"outline",size:"sm",className:"border-green-muted/50 text-green-bright hover:bg-green-dark/20 px-2 lg:px-3 min-h-[44px] md:min-h-[36px]","aria-label":"Dashboard",children:[t.jsx(C,{className:"w-4 h-4 lg:mr-2"}),t.jsx("span",{className:"hidden lg:inline",children:"Dashboard"})]})}),t.jsxs(Mn,{onClick:a,variant:"outline",size:"sm",className:"border-green-muted/50 text-green-bright hover:bg-green-dark/20 px-2 lg:px-3 min-h-[44px] md:min-h-[36px]","aria-label":"Sign out of your account",children:[t.jsx(P,{className:"w-4 h-4 lg:mr-2"}),t.jsx("span",{className:"hidden lg:inline",children:"Sign Out"})]})]}):e?t.jsx(s,{to:"/auth",onClick:r,children:t.jsxs(Mn,{variant:"outline",className:"w-full border-green-muted/50 text-green-bright hover:bg-green-dark/20 min-h-[44px] justify-start","aria-label":"Sign in to your account",children:[t.jsx(A,{className:"w-5 h-5 mr-3"}),"Sign In"]})}):t.jsx(s,{to:"/auth",children:t.jsx(Mn,{variant:"outline",size:"sm",className:"border-green-muted/50 text-green-bright hover:bg-green-dark/20 px-2 lg:px-3 min-h-[44px] md:min-h-[36px]","aria-label":"Sign in to your account",children:"Sign In"})})},Hn=0,Wn=10,Kn=50,Gn=60,Jn=70,Yn=90,Zn=100,Xn={BANNER_HEIGHT:48,NAVBAR_HEIGHT:64,get TOTAL_HEADER_HEIGHT(){return this.BANNER_HEIGHT+this.NAVBAR_HEIGHT},BREAKPOINTS:{SM:640,MD:768,LG:1024,XL:1280,"2XL":1400}},Qn=({isOpen:e,onClose:s})=>e?t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"fixed inset-0 bg-black/80 backdrop-blur-sm md:hidden",style:{zIndex:Jn},onClick:s,"aria-hidden":"true"}),t.jsxs("div",{id:"mobile-menu",className:"fixed inset-x-0 top-0 bg-black border-b border-green-muted/30 md:hidden",style:{zIndex:Jn+1},role:"dialog","aria-modal":"true","aria-label":"Mobile navigation menu",children:[t.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-green-muted/20",children:[t.jsxs("span",{className:"font-bold text-lg",children:[t.jsx("span",{className:"text-white",children:"BLACK"}),t.jsx("span",{className:"text-green-bright",children:"V"}),t.jsx("span",{className:"text-white",children:"EIL"})]}),t.jsx("button",{onClick:s,className:"p-2 text-white hover:text-green-bright transition-colors","aria-label":"Close menu",children:t.jsx(v,{className:"h-6 w-6"})})]}),t.jsxs("div",{className:"px-4 py-4 space-y-4 max-h-[calc(100vh-80px)] overflow-y-auto",children:[t.jsxs("nav",{className:"space-y-3",children:[t.jsx(rt,{onClick:s}),t.jsx($n,{})]}),t.jsx("div",{className:"pt-4 border-t border-green-muted/20",children:t.jsx("div",{className:"mb-4",children:t.jsx(Vn,{isMobile:!0,onAction:s})})}),t.jsx("div",{className:"pt-4 border-t border-green-muted/20",children:t.jsx(qn,{isMobile:!0,onClick:s})})]})]})]}):null,ei=()=>{const[e,s]=r.useState(!1),[n,i]=r.useState(!1),a=x();return r.useEffect((()=>{const e=()=>{i(window.scrollY>20)};return window.addEventListener("scroll",e,{passive:!0}),()=>window.removeEventListener("scroll",e)}),[]),r.useEffect((()=>{s(!1)}),[a]),r.useEffect((()=>(document.body.style.overflow=e?"hidden":"",()=>{document.body.style.overflow=""})),[e]),t.jsxs(t.Fragment,{children:[t.jsx("nav",{className:"fixed left-0 right-0 transition-all duration-300 "+(n?"bg-black/95 backdrop-blur-sm border-b border-green-muted/20":"bg-transparent"),style:{top:`${Xn.BANNER_HEIGHT}px`,zIndex:Gn},children:t.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:t.jsxs("div",{className:"flex items-center justify-between h-16",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx(qe,{})}),t.jsx("div",{className:"hidden md:flex items-center justify-center flex-1 mx-8",children:t.jsx("div",{className:"flex items-center space-x-6 lg:space-x-8",children:t.jsx(rt,{})})}),t.jsxs("div",{className:"hidden md:flex items-center space-x-4 lg:space-x-6 flex-shrink-0",children:[t.jsx($n,{}),t.jsx(Vn,{})]}),t.jsxs("div",{className:"md:hidden flex items-center space-x-3 flex-shrink-0",children:[t.jsx(Vn,{}),t.jsx(nt,{isOpen:e,onClick:()=>s(!e)})]})]})})}),t.jsx(Qn,{isOpen:e,onClose:()=>s(!1)})]})},ti=()=>{const e=(new Date).getFullYear(),r=({to:e,label:r})=>t.jsx("li",{children:t.jsxs(s,{to:e,className:"text-white/70 hover:text-green-bright flex items-center transition-colors text-sm",children:[t.jsx(I,{className:"h-3 w-3 mr-2 flex-shrink-0"}),t.jsx("span",{children:r})]})});return t.jsx("footer",{className:"bg-black-soft border-t border-green-muted/20 pt-10 xs:pt-12 sm:pt-14 md:pt-16 pb-4 xs:pb-5 sm:pb-6 relative z-10",children:t.jsxs("div",{className:"container mx-auto px-4 xs:px-5 sm:px-6 md:px-8",children:[t.jsxs("div",{className:"grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8 xs:gap-10 md:gap-12",children:[t.jsxs("div",{children:[t.jsxs(s,{to:"/",className:"flex items-center space-x-2 mb-4",children:[t.jsx(u,{className:"h-6 w-6 text-green-bright"}),t.jsx("span",{className:"font-mono font-bold text-lg",children:"BlackVeil"})]}),t.jsx("p",{className:"text-white/70 mb-4 text-sm",children:"Enterprise-grade protection without enterprise-level complexity. We help businesses identify and manage cybersecurity risks."}),t.jsx("div",{className:"flex space-x-4",children:t.jsx("a",{href:"https://www.linkedin.com/company/blackveil-limited",target:"_blank",rel:"noopener noreferrer",className:"text-white/50 hover:text-green-bright transition-colors",children:t.jsx(O,{className:"h-5 w-5"})})})]}),t.jsxs("div",{children:[t.jsx("h3",{className:"font-mono text-green-bright text-sm uppercase tracking-wider mb-4",children:"Quick Links"}),t.jsx("ul",{className:"space-y-3",children:[{label:"Home",path:"/"},{label:"Our Services",path:"/services"},{label:"Contact Us",path:"/contact"}].map((e=>t.jsx(r,{to:e.path,label:e.label},e.path)))})]}),t.jsxs("div",{children:[t.jsx("h3",{className:"font-mono text-green-bright text-sm uppercase tracking-wider mb-4",children:"Services"}),t.jsx("ul",{className:"space-y-3",children:[{label:"Security Audits",path:"/services#security-assessment"},{label:"Implementation Plans",path:"/services#secure-growth"},{label:"Ongoing Support",path:"/services#ongoing-protection"},{label:"Security Training",path:"/services#email-security"},{label:"Vulnerability Testing",path:"/services#incident-response"}].map((e=>t.jsx(r,{to:e.path,label:e.label},e.path)))})]}),t.jsxs("div",{children:[t.jsx("h3",{className:"font-mono text-green-bright text-sm uppercase tracking-wider mb-4",children:"Contact Us"}),t.jsx("div",{className:"mb-6",children:t.jsxs(s,{to:"/contact",className:"bg-green-bright/10 border border-green-bright/30 text-green-bright px-4 py-2 rounded text-sm inline-flex items-center hover:bg-green-bright/20 transition-colors",children:[t.jsx(R,{className:"h-4 w-4 mr-2"}),"Contact Us"]})}),t.jsx("p",{className:"text-white/70 text-sm",children:"Connect with us on LinkedIn for updates and cybersecurity insights."})]})]}),t.jsx("div",{className:"cyber-divider mt-8 xs:mt-10 sm:mt-12 mb-4 xs:mb-5 sm:mb-6"}),t.jsxs("div",{className:"flex flex-col xs:flex-row items-center justify-between",children:[t.jsxs("p",{className:"text-white/50 text-xs xs:text-sm",children:["© ",e," BlackVeil. All rights reserved."]}),t.jsxs("div",{className:"flex flex-wrap justify-center xs:justify-end space-x-4 xs:space-x-5 sm:space-x-6 mt-3 xs:mt-0",children:[t.jsx(s,{to:"/privacy-policy",className:"text-white/50 hover:text-green-bright text-xs xs:text-sm transition-colors",children:"Privacy Policy"}),t.jsx(s,{to:"/terms",className:"text-white/50 hover:text-green-bright text-xs xs:text-sm transition-colors",children:"Terms of Service"}),t.jsx(s,{to:"/cookies",className:"text-white/50 hover:text-green-bright text-xs xs:text-sm transition-colors",children:"Cookie Policy"})]})]})]})})};function si(){const{pathname:e}=x();return r.useEffect((()=>{window.scrollTo({top:0,left:0,behavior:"instant"});const e=setTimeout((()=>{window.scrollTo(0,0)}),0);return()=>clearTimeout(e)}),[e]),null}const ri=()=>t.jsx("a",{href:"#main-content",className:"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 \n                focus:px-4 focus:py-2 focus:bg-black focus:border-2 focus:border-green-bright\n                focus:text-green-bright focus:rounded-md focus:outline-none",children:"Skip to main content"});var ni="ToastProvider",[ii,ai,oi]=ue("Toast"),[li,ci]=he("Toast",[oi]),[di,ui]=li(ni),hi=e=>{const{__scopeToast:s,label:n="Notification",duration:i=5e3,swipeDirection:a="right",swipeThreshold:o=50,children:l}=e,[c,d]=r.useState(null),[u,h]=r.useState(0),m=r.useRef(!1),p=r.useRef(!1);return n.trim(),t.jsx(ii.Provider,{scope:s,children:t.jsx(di,{scope:s,label:n,duration:i,swipeDirection:a,swipeThreshold:o,toastCount:u,viewport:c,onViewportChange:d,onToastAdd:r.useCallback((()=>h((e=>e+1))),[]),onToastRemove:r.useCallback((()=>h((e=>e-1))),[]),isFocusedToastEscapeKeyDownRef:m,isClosePausedRef:p,children:l})})};hi.displayName=ni;var mi="ToastViewport",pi=["F8"],fi="toast.viewportPause",gi="toast.viewportResume",xi=r.forwardRef(((e,s)=>{const{__scopeToast:n,hotkey:i=pi,label:a="Notifications ({hotkey})",...o}=e,l=ui(mi,n),c=ai(n),d=r.useRef(null),u=r.useRef(null),h=r.useRef(null),m=r.useRef(null),p=me(s,m,l.onViewportChange),f=i.join("+").replace(/Key/g,"").replace(/Digit/g,""),g=l.toastCount>0;r.useEffect((()=>{const e=e=>{0!==i.length&&i.every((t=>e[t]||e.code===t))&&m.current?.focus()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[i]),r.useEffect((()=>{const e=d.current,t=m.current;if(g&&e&&t){const s=()=>{if(!l.isClosePausedRef.current){const e=new CustomEvent(fi);t.dispatchEvent(e),l.isClosePausedRef.current=!0}},r=()=>{if(l.isClosePausedRef.current){const e=new CustomEvent(gi);t.dispatchEvent(e),l.isClosePausedRef.current=!1}},n=t=>{!e.contains(t.relatedTarget)&&r()},i=()=>{e.contains(document.activeElement)||r()};return e.addEventListener("focusin",s),e.addEventListener("focusout",n),e.addEventListener("pointermove",s),e.addEventListener("pointerleave",i),window.addEventListener("blur",s),window.addEventListener("focus",r),()=>{e.removeEventListener("focusin",s),e.removeEventListener("focusout",n),e.removeEventListener("pointermove",s),e.removeEventListener("pointerleave",i),window.removeEventListener("blur",s),window.removeEventListener("focus",r)}}}),[g,l.isClosePausedRef]);const x=r.useCallback((({tabbingDirection:e})=>{const t=c().map((t=>{const s=t.ref.current,r=[s,...Li(s)];return"forwards"===e?r:r.reverse()}));return("forwards"===e?t.reverse():t).flat()}),[c]);return r.useEffect((()=>{const e=m.current;if(e){const t=t=>{const s=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!s){const s=document.activeElement,r=t.shiftKey;if(t.target===e&&r)return void u.current?.focus();const n=x({tabbingDirection:r?"backwards":"forwards"}),i=n.findIndex((e=>e===s));$i(n.slice(i+1))?t.preventDefault():r?u.current?.focus():h.current?.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}}),[c,x]),t.jsxs(pe,{ref:d,role:"region","aria-label":a.replace("{hotkey}",f),tabIndex:-1,style:{pointerEvents:g?void 0:"none"},children:[g&&t.jsx(yi,{ref:u,onFocusFromOutsideViewport:()=>{$i(x({tabbingDirection:"forwards"}))}}),t.jsx(ii.Slot,{scope:n,children:t.jsx(fe.ol,{tabIndex:-1,...o,ref:p})}),g&&t.jsx(yi,{ref:h,onFocusFromOutsideViewport:()=>{$i(x({tabbingDirection:"backwards"}))}})]})}));xi.displayName=mi;var bi="ToastFocusProxy",yi=r.forwardRef(((e,s)=>{const{__scopeToast:r,onFocusFromOutsideViewport:n,...i}=e,a=ui(bi,r);return t.jsx(ge,{"aria-hidden":!0,tabIndex:0,...i,ref:s,style:{position:"fixed"},onFocus:e=>{const t=e.relatedTarget;!a.viewport?.contains(t)&&n()}})}));yi.displayName=bi;var vi="Toast",wi=r.forwardRef(((e,s)=>{const{forceMount:r,open:n,defaultOpen:i,onOpenChange:a,...o}=e,[l=!0,c]=xe({prop:n,defaultProp:i,onChange:a});return t.jsx(be,{present:r||l,children:t.jsx(ki,{open:l,...o,ref:s,onClose:()=>c(!1),onPause:ye(e.onPause),onResume:ye(e.onResume),onSwipeStart:ve(e.onSwipeStart,(e=>{e.currentTarget.setAttribute("data-swipe","start")})),onSwipeMove:ve(e.onSwipeMove,(e=>{const{x:t,y:s}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${s}px`)})),onSwipeCancel:ve(e.onSwipeCancel,(e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")})),onSwipeEnd:ve(e.onSwipeEnd,(e=>{const{x:t,y:s}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${s}px`),c(!1)}))})})}));wi.displayName=vi;var[ji,Ni]=li(vi,{onClose(){}}),ki=r.forwardRef(((s,n)=>{const{__scopeToast:i,type:a="foreground",duration:o,open:l,onClose:c,onEscapeKeyDown:d,onPause:u,onResume:h,onSwipeStart:m,onSwipeMove:p,onSwipeCancel:f,onSwipeEnd:g,...x}=s,b=ui(vi,i),[y,v]=r.useState(null),w=me(n,(e=>v(e))),j=r.useRef(null),N=r.useRef(null),k=o||b.duration,_=r.useRef(0),T=r.useRef(k),S=r.useRef(0),{onToastAdd:E,onToastRemove:A}=b,C=ye((()=>{const e=y?.contains(document.activeElement);e&&b.viewport?.focus(),c()})),P=r.useCallback((e=>{e&&e!==1/0&&(window.clearTimeout(S.current),_.current=(new Date).getTime(),S.current=window.setTimeout(C,e))}),[C]);r.useEffect((()=>{const e=b.viewport;if(e){const t=()=>{P(T.current),h?.()},s=()=>{const e=(new Date).getTime()-_.current;T.current=T.current-e,window.clearTimeout(S.current),u?.()};return e.addEventListener(fi,s),e.addEventListener(gi,t),()=>{e.removeEventListener(fi,s),e.removeEventListener(gi,t)}}}),[b.viewport,k,u,h,P]),r.useEffect((()=>{l&&!b.isClosePausedRef.current&&P(k)}),[l,k,b.isClosePausedRef,P]),r.useEffect((()=>(E(),()=>A())),[E,A]);const O=r.useMemo((()=>y?Oi(y):null),[y]);return b.viewport?t.jsxs(t.Fragment,{children:[O&&t.jsx(_i,{__scopeToast:i,role:"status","aria-live":"foreground"===a?"assertive":"polite","aria-atomic":!0,children:O}),t.jsx(ji,{scope:i,onClose:C,children:e.createPortal(t.jsx(ii.ItemSlot,{scope:i,children:t.jsx(we,{asChild:!0,onEscapeKeyDown:ve(d,(()=>{b.isFocusedToastEscapeKeyDownRef.current||C(),b.isFocusedToastEscapeKeyDownRef.current=!1})),children:t.jsx(fe.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":l?"open":"closed","data-swipe-direction":b.swipeDirection,...x,ref:w,style:{userSelect:"none",touchAction:"none",...s.style},onKeyDown:ve(s.onKeyDown,(e=>{"Escape"===e.key&&(d?.(e.nativeEvent),e.nativeEvent.defaultPrevented||(b.isFocusedToastEscapeKeyDownRef.current=!0,C()))})),onPointerDown:ve(s.onPointerDown,(e=>{0===e.button&&(j.current={x:e.clientX,y:e.clientY})})),onPointerMove:ve(s.onPointerMove,(e=>{if(!j.current)return;const t=e.clientX-j.current.x,s=e.clientY-j.current.y,r=Boolean(N.current),n=["left","right"].includes(b.swipeDirection),i=["left","up"].includes(b.swipeDirection)?Math.min:Math.max,a=n?i(0,t):0,o=n?0:i(0,s),l="touch"===e.pointerType?10:2,c={x:a,y:o},d={originalEvent:e,delta:c};r?(N.current=c,Ri("toast.swipeMove",p,d,{discrete:!1})):Ii(c,b.swipeDirection,l)?(N.current=c,Ri("toast.swipeStart",m,d,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(s)>l)&&(j.current=null)})),onPointerUp:ve(s.onPointerUp,(e=>{const t=N.current,s=e.target;if(s.hasPointerCapture(e.pointerId)&&s.releasePointerCapture(e.pointerId),N.current=null,j.current=null,t){const s=e.currentTarget,r={originalEvent:e,delta:t};Ii(t,b.swipeDirection,b.swipeThreshold)?Ri("toast.swipeEnd",g,r,{discrete:!0}):Ri("toast.swipeCancel",f,r,{discrete:!0}),s.addEventListener("click",(e=>e.preventDefault()),{once:!0})}}))})})}),b.viewport)})]}):null})),_i=e=>{const{__scopeToast:s,children:n,...i}=e,a=ui(vi,s),[o,l]=r.useState(!1),[c,d]=r.useState(!1);return function(e=()=>{}){const t=ye(e);Ne((()=>{let e=0,s=0;return e=window.requestAnimationFrame((()=>s=window.requestAnimationFrame(t))),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(s)}}),[t])}((()=>l(!0))),r.useEffect((()=>{const e=window.setTimeout((()=>d(!0)),1e3);return()=>window.clearTimeout(e)}),[]),c?null:t.jsx(je,{asChild:!0,children:t.jsx(ge,{...i,children:o&&t.jsxs(t.Fragment,{children:[a.label," ",n]})})})},Ti=r.forwardRef(((e,s)=>{const{__scopeToast:r,...n}=e;return t.jsx(fe.div,{...n,ref:s})}));Ti.displayName="ToastTitle";var Si=r.forwardRef(((e,s)=>{const{__scopeToast:r,...n}=e;return t.jsx(fe.div,{...n,ref:s})}));Si.displayName="ToastDescription";var Ei=r.forwardRef(((e,s)=>{const{altText:r,...n}=e;return r.trim()?t.jsx(Pi,{altText:r,asChild:!0,children:t.jsx(Ci,{...n,ref:s})}):null}));Ei.displayName="ToastAction";var Ai="ToastClose",Ci=r.forwardRef(((e,s)=>{const{__scopeToast:r,...n}=e,i=Ni(Ai,r);return t.jsx(Pi,{asChild:!0,children:t.jsx(fe.button,{type:"button",...n,ref:s,onClick:ve(e.onClick,i.onClose)})})}));Ci.displayName=Ai;var Pi=r.forwardRef(((e,s)=>{const{__scopeToast:r,altText:n,...i}=e;return t.jsx(fe.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...i,ref:s})}));function Oi(e){const t=[];return Array.from(e.childNodes).forEach((e=>{if(e.nodeType===e.TEXT_NODE&&e.textContent&&t.push(e.textContent),function(e){return e.nodeType===e.ELEMENT_NODE}(e)){const s=e.ariaHidden||e.hidden||"none"===e.style.display,r=""===e.dataset.radixToastAnnounceExclude;if(!s)if(r){const s=e.dataset.radixToastAnnounceAlt;s&&t.push(s)}else t.push(...Oi(e))}})),t}function Ri(e,t,s,{discrete:r}){const n=s.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:s});t&&n.addEventListener(e,t,{once:!0}),r?ke(n,i):n.dispatchEvent(i)}var Ii=(e,t,s=0)=>{const r=Math.abs(e.x),n=Math.abs(e.y),i=r>n;return"left"===t||"right"===t?i&&r>s:!i&&n>s};function Li(e){const t=[],s=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)t.push(s.currentNode);return t}function $i(e){const t=document.activeElement;return e.some((e=>e===t||(e.focus(),document.activeElement!==t)))}var Di=xi,Mi=wi,Ui=Ti,Bi=Si,zi=Ei,Fi=Ci;const qi=hi,Vi=r.forwardRef((({className:e,...s},r)=>t.jsx(Di,{ref:r,className:ae("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...s})));Vi.displayName=Di.displayName;const Hi=k("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),Wi=r.forwardRef((({className:e,variant:s,...r},n)=>t.jsx(Mi,{ref:n,className:ae(Hi({variant:s}),e),...r})));Wi.displayName=Mi.displayName;r.forwardRef((({className:e,...s},r)=>t.jsx(zi,{ref:r,className:ae("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...s}))).displayName=zi.displayName;const Ki=r.forwardRef((({className:e,...s},r)=>t.jsx(Fi,{ref:r,className:ae("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...s,children:t.jsx(v,{className:"h-4 w-4"})})));Ki.displayName=Fi.displayName;const Gi=r.forwardRef((({className:e,...s},r)=>t.jsx(Ui,{ref:r,className:ae("text-sm font-semibold",e),...s})));Gi.displayName=Ui.displayName;const Ji=r.forwardRef((({className:e,...s},r)=>t.jsx(Bi,{ref:r,className:ae("text-sm opacity-90",e),...s})));Ji.displayName=Bi.displayName;let Yi=0;const Zi=new Map,Xi=e=>{if(Zi.has(e))return;const t=setTimeout((()=>{Zi.delete(e),sa({type:"REMOVE_TOAST",toastId:e})}),1e6);Zi.set(e,t)},Qi=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map((e=>e.id===t.toast.id?{...e,...t.toast}:e))};case"DISMISS_TOAST":{const{toastId:s}=t;return s?Xi(s):e.toasts.forEach((e=>{Xi(e.id)})),{...e,toasts:e.toasts.map((e=>e.id===s||void 0===s?{...e,open:!1}:e))}}case"REMOVE_TOAST":return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter((e=>e.id!==t.toastId))}}},ea=[];let ta={toasts:[]};function sa(e){ta=Qi(ta,e),ea.forEach((e=>{e(ta)}))}function ra({...e}){const t=(Yi=(Yi+1)%Number.MAX_SAFE_INTEGER,Yi.toString()),s=()=>sa({type:"DISMISS_TOAST",toastId:t});return sa({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||s()}}}),{id:t,dismiss:s,update:e=>sa({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function na(){const[e,t]=r.useState(ta);return r.useEffect((()=>(ea.push(t),()=>{const e=ea.indexOf(t);e>-1&&ea.splice(e,1)})),[e]),{...e,toast:ra,dismiss:e=>sa({type:"DISMISS_TOAST",toastId:e})}}function ia(){const{toasts:e}=na();return t.jsxs(qi,{children:[e.map((function({id:e,title:s,description:r,action:n,...i}){return t.jsxs(Wi,{...i,children:[t.jsxs("div",{className:"grid gap-1",children:[s&&t.jsx(Gi,{children:s}),r&&t.jsx(Ji,{children:r})]}),n,t.jsx(Ki,{})]},e)})),t.jsx(Vi,{style:{zIndex:Zn}})]})}function aa(e){if("undefined"==typeof performance||!performance.mark||!performance.measure)return()=>{};const t=`${e}-start`,s=`${e}-end`;return performance.mark(t),()=>{performance.mark(s),performance.measure(e,t,s);try{const r=performance.getEntriesByName(e);if(r.length>0){r[0].duration;0}performance.clearMarks(t),performance.clearMarks(s),performance.clearMeasures(e)}catch(r){}}}const oa=({threshold:e=.1,rootMargin:t="0px",once:s=!0,delay:n=0}={})=>{const i=r.useRef(null),[a,o]=r.useState(!1);return r.useEffect((()=>{const r=new IntersectionObserver((([e])=>{if(e.isIntersecting){const e=setTimeout((()=>{o(!0),s&&i.current&&r.unobserve(i.current)}),n);return()=>clearTimeout(e)}s||o(!1)}),{threshold:e,rootMargin:t}),a=i.current;return a&&r.observe(a),()=>{a&&r.unobserve(a)}}),[e,t,s,n]),{ref:i,isVisible:a}},la=({variant:e,color:t,density:s,speed:n,isVisible:i})=>{const a=r.useRef(null),o=r.useRef(0),l=r.useRef([]),c=r.useRef(0),[d,u]=r.useState({width:0,height:0}),[h,m]=r.useState(!1);return r.useEffect((()=>{const r=a.current;if(!r||!i)return;const d=r.getContext("2d");if(!d)return;const h=()=>{if("particles"===e||"cyber"===e){const e=Math.max(20,Math.floor(r.width*r.height/15e3)*s/50);l.current=Array.from({length:e},(()=>((e,t,s)=>{const r=3*Math.random()+1;return{x:Math.random()*e,y:Math.random()*t,size:r,originalSize:r,speedX:.5*(Math.random()-.5)*s,speedY:.5*(Math.random()-.5)*s,opacity:.5*Math.random()+.3,hue:30*Math.random()}})(r.width,r.height,n)))}},p=()=>{if(!r)return;const e=r.parentElement;if(!e)return;const t=e.getBoundingClientRect();r.width=t.width,r.height=t.height,(e=>{const t=window.devicePixelRatio||1,s=e.getBoundingClientRect();e.width=s.width*t,e.height=s.height*t,e.style.width=`${s.width}px`,e.style.height=`${s.height}px`;const r=e.getContext("2d");r&&r.scale(t,t),s.width,s.height})(r),u({width:t.width,height:t.height}),h(),m(!0)},f=s=>{d.clearRect(0,0,r.width,r.height),c.current=s,"particles"!==e&&"cyber"!==e||l.current.forEach(((s,n)=>{if(((e,t,s)=>{e.x+=e.speedX,e.y+=e.speedY,e.x<0&&(e.x=t),e.x>t&&(e.x=0),e.y<0&&(e.y=s),e.y>s&&(e.y=0),e.speedX*=.995,e.speedY*=.995,e.speedX+=.005*(Math.random()-.5),e.speedY+=.005*(Math.random()-.5)})(s,r.width,r.height),"cyber"===e){const e=`hsla(140, 100%, ${50+s.hue}%, ${s.opacity})`;d.beginPath(),d.rect(s.x,s.y,s.originalSize,s.originalSize),d.fillStyle=e,d.fill(),l.current.forEach(((e,t)=>{if(n===t)return;const r=s.x-e.x,i=s.y-e.y,a=Math.sqrt(r*r+i*i);a<100&&(d.beginPath(),d.moveTo(s.x+s.size/2,s.y+s.size/2),d.lineTo(e.x+e.size/2,e.y+e.size/2),d.strokeStyle=`rgba(0, 255, 140, ${.15*(1-a/100)})`,d.lineWidth=.5,d.stroke())}))}else d.beginPath(),d.arc(s.x,s.y,s.size,0,2*Math.PI),d.fillStyle=t||`rgba(0, 255, 140, ${s.opacity})`,d.fill()})),"grid"===e?((e,t,s,r,n,i)=>{const a=5e-4*r*i;for(let o=0;o<t;o+=30){const t=.5*Math.sin(a+.01*o)+.5;e.beginPath(),e.moveTo(o,0),e.lineTo(o,s),e.strokeStyle=n||`rgba(0, 255, 140, ${.05+.1*t})`,e.lineWidth=.5,e.stroke()}for(let o=0;o<s;o+=30){const s=.5*Math.sin(a+.01*o)+.5;e.beginPath(),e.moveTo(0,o),e.lineTo(t,o),e.strokeStyle=n||`rgba(0, 255, 140, ${.05+.1*s})`,e.lineWidth=.5,e.stroke()}for(let o=0;o<t;o+=30)for(let r=0;r<s;r+=30){const i=Math.sqrt(Math.pow(o-t/2,2)+Math.pow(r-s/2,2)),l=.5*Math.sin(a-.01*i)+.5;l>.7&&(e.beginPath(),e.arc(o,r,1.5*l,0,2*Math.PI),e.fillStyle=n||`rgba(0, 255, 140, ${.5*l})`,e.fill())}})(d,r.width,r.height,s,t,n):"wave"===e?((e,t,s,r,n,i)=>{const a=.001*r*i;for(let o=1;o<=3;o++){const r=120*o;e.beginPath(),e.strokeStyle=n||`rgba(0, 255, 140, ${.1-.03*(o-1)})`,e.lineWidth=4-o;for(let n=0;n<t;n+=5){const t=.01/o,i=20/o,l=Math.sin(n*t+a+o)*i+s/2+r;0===n?e.moveTo(n,l):e.lineTo(n,l)}e.stroke()}})(d,r.width,r.height,s,t,n):"glow"===e&&((e,t,s,r,n)=>{const i=5e-4*r*n,a=t/2+Math.sin(i)*t*.1,o=s/2+Math.cos(1.3*i)*s*.1,l=e.createRadialGradient(a,o,0,a,o,.6*t);l.addColorStop(0,"rgba(0, 255, 140, 0.3)"),l.addColorStop(.5,"rgba(0, 255, 140, 0.1)"),l.addColorStop(1,"rgba(0, 255, 140, 0)"),e.fillStyle=l,e.fillRect(0,0,t,s);for(let c=0;c<20;c++){const s=Math.random()*Math.PI*2,r=Math.random()*t*.3,n=a+Math.cos(s)*r,i=o+Math.sin(s)*r,l=2*Math.random()+1;e.beginPath(),e.arc(n,i,l,0,2*Math.PI),e.fillStyle="rgba(0, 255, 140, 0.8)",e.fill()}})(d,r.width,r.height,s,n)},g=e=>{f(e),o.current=requestAnimationFrame(g)};return p(),g(0),window.addEventListener("resize",p),()=>{window.removeEventListener("resize",p),cancelAnimationFrame(o.current)}}),[i,e,t,s,n]),{canvasRef:a,isInitialized:h,dimensions:d}},ca=({variant:e="default",color:s="rgba(0, 255, 140, 0.03)",opacity:r=.5,className:n="",density:i=50,speed:a=1})=>{const{ref:o,isVisible:l}=oa({threshold:.1}),{canvasRef:c,isInitialized:d}=la({variant:e,color:s,density:i,speed:a,isVisible:l});return t.jsxs("div",{ref:e=>{o&&"current"in o&&(o.current=e)},className:`absolute inset-0 overflow-hidden ${n}`,style:{opacity:r},children:[t.jsx("canvas",{ref:c,className:"w-full h-full",style:{zIndex:-1}}),!d&&t.jsx("div",{className:"absolute inset-0 bg-cyber-grid opacity-10"})]})},da=({children:e,id:s,as:r="span"})=>{const n=r;return t.jsx(n,{id:s,className:"sr-only","aria-live":"polite",children:e})};da.displayName="ScreenReaderText";const ua=({"aria-label":e,icon:s,className:r,...n})=>t.jsxs("button",{type:"button","aria-label":e,className:L("fixed z-50 bottom-6 right-6","w-12 h-12 min-w-[44px] min-h-[44px] rounded-full","bg-primary-600 text-white shadow-lg","focus:outline-none focus-visible:ring-4 focus-visible:ring-primary-400 focus-visible:ring-offset-2","hover:bg-primary-700 active:bg-primary-800 transition-colors","md:hidden",r),tabIndex:0,...n,children:[s,t.jsx(da,{children:e})]}),ha=Pe,ma=Ce,pa=r.forwardRef((({className:e,...s},r)=>t.jsx(_e,{ref:r,className:ae("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s})));pa.displayName=_e.displayName;const fa=r.forwardRef((({className:e,children:s,...r},n)=>t.jsxs(ma,{children:[t.jsx(pa,{}),t.jsxs(Te,{ref:n,className:ae("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...r,children:[s,t.jsxs(Se,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[t.jsx(v,{className:"h-4 w-4"}),t.jsx("span",{className:"sr-only",children:"Close"})]})]})]})));fa.displayName=Te.displayName;const ga=({className:e,...s})=>t.jsx("div",{className:ae("flex flex-col space-y-1.5 text-center sm:text-left",e),...s});ga.displayName="DialogHeader";const xa=r.forwardRef((({className:e,...s},r)=>t.jsx(Ee,{ref:r,className:ae("text-lg font-semibold leading-none tracking-tight",e),...s})));xa.displayName=Ee.displayName;const ba=r.forwardRef((({className:e,...s},r)=>t.jsx(Ae,{ref:r,className:ae("text-sm text-muted-foreground",e),...s})));ba.displayName=Ae.displayName;const ya=()=>{const[e,t]=r.useState(!1);return r.useEffect((()=>{const e=e=>{"Tab"===e.key&&(t(!0),document.body.classList.add("using-keyboard"))},s=()=>{t(!1),document.body.classList.remove("using-keyboard")};document.addEventListener("keydown",e),document.addEventListener("mousedown",s);const r=document.createElement("style");return r.innerHTML="\n      .using-keyboard *:focus {\n        outline: 2px solid #00FF88 !important;\n        outline-offset: 2px !important;\n      }\n      \n      /* Hide focus outline when not using keyboard */\n      body:not(.using-keyboard) *:focus {\n        outline: none !important;\n      }\n    ",document.head.appendChild(r),()=>{document.removeEventListener("keydown",e),document.removeEventListener("mousedown",s),document.head.removeChild(r)}}),[]),null},va=({commands:e,activeIndex:s,onItemHover:r,onItemSelect:n,listRef:i})=>t.jsx("ul",{id:"command-palette-list",ref:i,role:"listbox","aria-label":"Command results",className:"max-h-64 overflow-y-auto py-2",children:0===e.length?t.jsx("li",{className:"px-4 py-2 text-neutral-500 dark:text-neutral-400",role:"option","aria-disabled":"true",children:"No commands found"}):e.map(((e,i)=>t.jsx("li",{id:`command-palette-item-${e.id}`,role:"option","aria-selected":i===s?"true":"false",tabIndex:-1,className:`px-4 py-2 cursor-pointer select-none rounded ${i===s?"bg-blue-600 text-white":"text-black dark:text-white"} focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500`,style:{outline:i===s?"2px solid #2563eb":void 0,outlineOffset:2},onMouseEnter:()=>r(i),onClick:()=>n(i),children:e.label},e.id)))});const wa=[{id:"open-settings",label:"Open Settings"},{id:"go-home",label:"Go to Home"},{id:"new-project",label:"Create New Project"},{id:"help",label:"Help & Documentation"}],ja=({open:e,onOpenChange:s})=>{const{query:n,setQuery:i,filtered:a,activeIndex:o,setActiveIndex:l,inputRef:c,listRef:d,handleKeyDown:u,announceSelection:h}=function(e,t,s){const[n,i]=r.useState(""),[a,o]=r.useState(e),[l,c]=r.useState(0),d=r.useRef(null),u=r.useRef(null);r.useEffect((()=>{if(t){d.current?.focus();const e=e=>{t&&e.target instanceof Node&&!document.querySelector('[role="dialog"]')?.contains(e.target)&&d.current?.focus()};return document.addEventListener("focusin",e),()=>document.removeEventListener("focusin",e)}}),[t]),r.useEffect((()=>{const t=n.toLowerCase(),s=e.filter((e=>e.label.toLowerCase().includes(t)));o(s),c(0)}),[n,e]),r.useEffect((()=>{const e=document.getElementById("command-palette-live");t?e&&(e.textContent="Command palette opened"):e&&(e.textContent="Command palette closed")}),[t]);const h=e=>{const t=document.getElementById("command-palette-live");t&&(t.textContent=`Selected: ${e}`)},m=()=>{if(u.current){const e=u.current.children[l];e&&e.scrollIntoView({block:"nearest"})}};return{query:n,setQuery:i,filtered:a,activeIndex:l,setActiveIndex:c,inputRef:d,listRef:u,handleKeyDown:e=>{"ArrowDown"===e.key?(e.preventDefault(),c((e=>(e+1)%a.length)),setTimeout(m,0)):"ArrowUp"===e.key?(e.preventDefault(),c((e=>(e-1+a.length)%a.length)),setTimeout(m,0)):"Enter"===e.key?(e.preventDefault(),a[l]&&(h(a[l].label),s(!1))):"Escape"===e.key&&(e.preventDefault(),s(!1))},announceSelection:h}}(wa,e,s),[m,p]=r.useState(!1);r.useEffect((()=>{"CVE-2017-0144"!==n||m||p(!0)}),[n,m]);return t.jsxs(t.Fragment,{children:[t.jsx("div",{id:"command-palette-live","aria-live":"polite",className:"sr-only"}),t.jsx(ha,{open:e,onOpenChange:s,children:t.jsxs(fa,{role:"dialog","aria-modal":"true","aria-label":"Command Palette",className:"max-w-lg w-full p-0 bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg",onOpenAutoFocus:e=>{e.preventDefault(),c.current?.focus()},children:[t.jsxs("form",{className:"border-b border-neutral-200 dark:border-neutral-700 px-4 py-3",onSubmit:e=>e.preventDefault(),children:[t.jsx("label",{htmlFor:"command-palette-input",className:"sr-only",children:"Search commands"}),t.jsx("input",{id:"command-palette-input",ref:c,type:"text",value:n,onChange:e=>i(e.target.value),onKeyDown:u,placeholder:"Type a command…",autoComplete:"off",className:"w-full bg-transparent outline-none text-lg text-black dark:text-white placeholder:text-neutral-400 dark:placeholder:text-neutral-500 py-2","aria-activedescendant":a[o]?`command-palette-item-${a[o].id}`:void 0,"aria-controls":"command-palette-list","aria-autocomplete":"list","aria-haspopup":"listbox","aria-expanded":e?"true":"false",role:"combobox",tabIndex:0}),t.jsx(ya,{})]}),t.jsx(va,{commands:a,activeIndex:o,onItemHover:l,onItemSelect:e=>{a[e]&&(h(a[e].label),s(!1))},listRef:d}),t.jsx(da,{children:"Use up and down arrows to navigate, Enter to select, and Escape to close."})]})}),t.jsx(ha,{open:m,onOpenChange:p,children:t.jsxs(fa,{role:"dialog","aria-modal":"true","aria-label":"CVE-2017-0144 Easter Egg",className:"max-w-lg w-full bg-white dark:bg-neutral-900 border border-red-600 dark:border-red-700 rounded-lg shadow-2xl",children:[t.jsxs(ga,{children:[t.jsx(xa,{className:"text-red-600 dark:text-red-400 flex items-center gap-2",children:"CVE-2017-0144 Detected"}),t.jsxs(ba,{children:[t.jsx("span",{className:"font-semibold text-red-500 dark:text-red-400",children:"Patch Now!"})," This vulnerability is infamous for enabling WannaCry. Stay safe!"]})]}),t.jsxs("div",{className:"my-4",children:[t.jsx("div",{className:"text-xs text-neutral-500 mb-1",children:"Redacted Exploit PoC:"}),t.jsx("pre",{className:"bg-neutral-900 text-green-400 text-xs rounded p-3 overflow-x-auto select-text",children:"// ms17-010 exploit PoC (redacted)\nconst smb = require('smb-protocol');\nconst payload = Buffer.from('[REDACTED]');\nsmb.connect('target-host', 445, () => {\n  // [REDACTED]\n  // Exploit code goes here...\n  // [REDACTED]\n});\n"})]}),t.jsx("a",{href:"/blog/hidden-history-of-zero-days",className:"underline text-blue-600 dark:text-blue-400",target:"_blank",rel:"noopener noreferrer",children:"Read: The Hidden History of Zero-Days (blog post)"})]})})]})},Na=()=>t.jsxs("div",{className:"fixed top-0 left-0 right-0 w-full bg-green-bright text-black text-center py-2 px-4 font-semibold flex items-center justify-center h-12",style:{zIndex:Kn},children:[t.jsx("span",{className:"mr-2",role:"img","aria-label":"rocket",children:"🚀"}),"BlackVault is now live! ",t.jsx("a",{href:"https://blackvault.co.nz",target:"_blank",rel:"noopener noreferrer",className:"underline font-bold hover:text-black-soft transition-colors ml-1",style:{textDecorationThickness:2},children:"Try it now →"})]}),ka=({children:e})=>{const[s,n]=r.useState(!0),[i,a]=r.useState(!1),{ref:o,isIntersecting:l}=function({triggerOnce:e=!0,threshold:t=.1,rootMargin:s="200px"}={}){const[n,i]=r.useState(!1),a=r.useRef(null);return r.useEffect((()=>{const r=a.current;if(!r)return;const n=new IntersectionObserver((([t])=>{t.isIntersecting?(i(!0),e&&n.disconnect()):e||i(!1)}),{threshold:t,rootMargin:s});return n.observe(r),()=>{n.disconnect()}}),[e,t,s]),{ref:a,isIntersecting:n}}({triggerOnce:!0,rootMargin:"200px"});return r.useEffect((()=>{"undefined"!=typeof document&&document.body.classList.add("enhanced-experience");const e=aa("initial-page-load"),t=setTimeout((()=>{n(!1),e()}),800);return()=>{clearTimeout(t),"undefined"!=typeof document&&document.body.classList.remove("enhanced-experience")}}),[]),s?t.jsx("div",{className:"fixed inset-0 flex items-center justify-center bg-black",style:{zIndex:Yn},role:"progressbar","aria-label":"Loading application",children:t.jsxs("div",{className:"text-center",children:[t.jsx(u,{className:"w-12 h-12 sm:w-16 sm:h-16 mx-auto text-green-bright animate-pulse-green mb-4","aria-hidden":"true"}),t.jsx("div",{className:"overflow-hidden h-6",children:t.jsx("p",{className:"text-green-bright font-mono text-xs sm:text-sm relative flex items-center justify-center",children:t.jsx("span",{className:"animate-typewriter overflow-hidden whitespace-nowrap border-r-2 border-green-bright pr-1 animate-blink",children:"LOADING SECURITY INTERFACE"})})})]})}):t.jsxs("div",{className:"flex flex-col min-h-screen relative bg-black",children:[t.jsxs("div",{className:"fixed inset-0 bg-black",style:{zIndex:Hn},children:[t.jsx("div",{className:"absolute inset-0 bg-cyber-grid opacity-10"}),t.jsx(ca,{variant:"cyber",opacity:.25,interactive:!1,className:"fixed inset-0",density:40,speed:.6})]}),t.jsx(ri,{}),t.jsx(si,{}),t.jsx(Na,{}),t.jsxs("div",{className:"relative flex flex-col min-h-screen",style:{zIndex:Wn},children:[t.jsx(ei,{}),t.jsx(ja,{open:i,onOpenChange:a}),t.jsxs("main",{id:"main-content",className:"flex-grow",style:{paddingTop:`${Xn.TOTAL_HEADER_HEIGHT}px`},tabIndex:-1,ref:o,children:[t.jsxs("div",{className:"sr-only","aria-live":"polite",children:["Content loaded - ",document.title||"Page content ready"]}),e]}),t.jsx(ti,{}),t.jsx(ua,{"aria-label":"Create new item",icon:t.jsx($,{className:"w-6 h-6","aria-hidden":"true"})})]}),t.jsx(ia,{})]})},_a=r.createContext({transitionType:"default",setTransitionType:()=>{}}),Ta=e=>{const t={duration:.4,ease:[.22,1,.36,1]},s={duration:.3,ease:[.22,1,.36,1]};switch(e){case"fade-up":default:return{initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:t},exit:{opacity:0,y:-20,transition:s}};case"fade-down":return{initial:{opacity:0,y:-20},animate:{opacity:1,y:0,transition:t},exit:{opacity:0,y:20,transition:s}};case"fade-left":return{initial:{opacity:0,x:20},animate:{opacity:1,x:0,transition:t},exit:{opacity:0,x:-20,transition:s}};case"fade-right":return{initial:{opacity:0,x:-20},animate:{opacity:1,x:0,transition:t},exit:{opacity:0,x:20,transition:s}};case"none":return{initial:{opacity:1},animate:{opacity:1},exit:{opacity:1}}}},Sa={"/":"fade-up","/about":"fade-left","/services":"fade-right","/portfolio":"fade-up","/contact":"fade-right","/blog":"fade-up"},Ea=({children:e})=>{const s=x(),{transitionType:n,setTransitionType:i}=r.useContext(_a),[a,o]=r.useState(!1);r.useEffect((()=>{const e=Sa[s.pathname]||"default";i(e),window.scrollTo({top:0,behavior:a?"auto":"smooth"});const t=document.getElementById("main-content");t&&setTimeout((()=>{t.focus()}),500)}),[s.pathname,i,a]),r.useEffect((()=>{if("undefined"==typeof window)return;const e=window.matchMedia("(prefers-reduced-motion: reduce)");o(e.matches);const t=e=>{o(e.matches)};return e.addEventListener("change",t),()=>{e.removeEventListener("change",t)}}),[]);const l=Ta(a?"none":n);return t.jsx(oe,{mode:"wait",children:t.jsx(ie.div,{initial:l.initial,animate:l.animate,exit:l.exit,className:"page-container",children:e},s.pathname)})},Aa=({children:e})=>{const[s,r]=a.useState(!1);return a.useEffect((()=>{const e=()=>r(!0);return window.addEventListener("error",e),()=>window.removeEventListener("error",e)}),[]),s?t.jsx("div",{className:"min-h-[50vh] flex items-center justify-center",children:t.jsxs("div",{className:"text-center",children:[t.jsx("h2",{className:"text-xl font-bold mb-4",children:"Failed to load page"}),t.jsx("button",{onClick:()=>r(!1),className:"bg-green-bright text-black px-4 py-2 rounded-md",children:"Try Again"})]})}):t.jsx(t.Fragment,{children:e})},Ca=()=>t.jsx("div",{className:"min-h-[50vh] flex items-center justify-center",children:t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"w-16 h-16 border-4 border-t-green-bright border-r-green-muted/30 border-b-green-muted/30 border-l-green-muted/30 rounded-full animate-spin mx-auto mb-4"}),t.jsx("p",{className:"text-white/70",children:"Loading page..."})]})}),Pa=({children:e,requireAdmin:s=!1})=>{const{user:n,isAdmin:i,isLoading:a,checkAdminRole:o}=In(),[l,c]=r.useState(!1);return r.useEffect((()=>{s&&n&&!l?o().finally((()=>{c(!0)})):s||c(!0)}),[s,n,o,l]),a?t.jsx("div",{className:"min-h-screen bg-black flex items-center justify-center",children:t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"w-16 h-16 border-4 border-t-green-bright border-r-green-muted/30 border-b-green-muted/30 border-l-green-muted/30 rounded-full animate-spin mx-auto mb-4"}),t.jsx("p",{className:"text-white/70",children:"Verifying access..."})]})}):n?s&&!l?t.jsx("div",{className:"min-h-screen bg-black flex items-center justify-center",children:t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"w-16 h-16 border-4 border-t-green-bright border-r-green-muted/30 border-b-green-muted/30 border-l-green-muted/30 rounded-full animate-spin mx-auto mb-4"}),t.jsx("p",{className:"text-white/70",children:"Checking permissions..."})]})}):s&&!i?t.jsx("div",{className:"min-h-screen bg-black flex items-center justify-center",children:t.jsxs("div",{className:"text-center max-w-md",children:[t.jsx("h1",{className:"text-2xl font-bold text-white mb-4",children:"Access Denied"}),t.jsx("p",{className:"text-white/60 mb-6",children:"You need administrator privileges to access this area."}),t.jsx("button",{onClick:()=>window.history.back(),className:"bg-green-bright hover:bg-green-muted text-black px-6 py-2 rounded-md font-semibold",children:"Go Back"})]})}):t.jsx(t.Fragment,{children:e}):t.jsx(D,{to:"/auth",replace:!0})};var Oa={exports:{}};function Ra(){}function Ia(){}Ia.resetWarningCache=Ra;Oa.exports=function(){function e(e,t,s,r,n,i){if("SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"!==i){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var s={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:Ia,resetWarningCache:Ra};return s.PropTypes=s,s}();const La=M(Oa.exports);var $a,Da=r,Ma=($a=Da)&&"object"==typeof $a&&"default"in $a?$a.default:$a;function Ua(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}var Ba=!("undefined"==typeof window||!window.document||!window.document.createElement);var za=function(e,t,s){if("function"!=typeof e)throw new Error("Expected reducePropsToState to be a function.");if("function"!=typeof t)throw new Error("Expected handleStateChangeOnClient to be a function.");if(void 0!==s&&"function"!=typeof s)throw new Error("Expected mapStateOnServer to either be undefined or a function.");return function(r){if("function"!=typeof r)throw new Error("Expected WrappedComponent to be a React component.");var n,i=[];function a(){n=e(i.map((function(e){return e.props}))),o.canUseDOM?t(n):s&&(n=s(n))}var o=function(e){var t,s;function o(){return e.apply(this,arguments)||this}s=e,(t=o).prototype=Object.create(s.prototype),t.prototype.constructor=t,t.__proto__=s,o.peek=function(){return n},o.rewind=function(){if(o.canUseDOM)throw new Error("You may only call rewind() on the server. Call peek() to read the current state.");var e=n;return n=void 0,i=[],e};var l=o.prototype;return l.UNSAFE_componentWillMount=function(){i.push(this),a()},l.componentDidUpdate=function(){a()},l.componentWillUnmount=function(){var e=i.indexOf(this);i.splice(e,1),a()},l.render=function(){return Ma.createElement(r,this.props)},o}(Da.PureComponent);return Ua(o,"displayName","SideEffect("+function(e){return e.displayName||e.name||"Component"}(r)+")"),Ua(o,"canUseDOM",Ba),o}};const Fa=M(za);var qa="undefined"!=typeof Element,Va="function"==typeof Map,Ha="function"==typeof Set,Wa="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;function Ka(e,t){if(e===t)return!0;if(e&&t&&"object"==typeof e&&"object"==typeof t){if(e.constructor!==t.constructor)return!1;var s,r,n,i;if(Array.isArray(e)){if((s=e.length)!=t.length)return!1;for(r=s;0!=r--;)if(!Ka(e[r],t[r]))return!1;return!0}if(Va&&e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(i=e.entries();!(r=i.next()).done;)if(!t.has(r.value[0]))return!1;for(i=e.entries();!(r=i.next()).done;)if(!Ka(r.value[1],t.get(r.value[0])))return!1;return!0}if(Ha&&e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(i=e.entries();!(r=i.next()).done;)if(!t.has(r.value[0]))return!1;return!0}if(Wa&&ArrayBuffer.isView(e)&&ArrayBuffer.isView(t)){if((s=e.length)!=t.length)return!1;for(r=s;0!=r--;)if(e[r]!==t[r])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf&&"function"==typeof e.valueOf&&"function"==typeof t.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString&&"function"==typeof e.toString&&"function"==typeof t.toString)return e.toString()===t.toString();if((s=(n=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=s;0!=r--;)if(!Object.prototype.hasOwnProperty.call(t,n[r]))return!1;if(qa&&e instanceof Element)return!1;for(r=s;0!=r--;)if(("_owner"!==n[r]&&"__v"!==n[r]&&"__o"!==n[r]||!e.$$typeof)&&!Ka(e[n[r]],t[n[r]]))return!1;return!0}return e!=e&&t!=t}const Ga=M((function(e,t){try{return Ka(e,t)}catch(s){if((s.message||"").match(/stack|recursion/i))return!1;throw s}}));
/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var Ja=Object.getOwnPropertySymbols,Ya=Object.prototype.hasOwnProperty,Za=Object.prototype.propertyIsEnumerable;const Xa=M(function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},s=0;s<10;s++)t["_"+String.fromCharCode(s)]=s;if("**********"!==Object.getOwnPropertyNames(t).map((function(e){return t[e]})).join(""))return!1;var r={};return"abcdefghijklmnopqrst".split("").forEach((function(e){r[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},r)).join("")}catch(n){return!1}}()?Object.assign:function(e,t){for(var s,r,n=function(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}(e),i=1;i<arguments.length;i++){for(var a in s=Object(arguments[i]))Ya.call(s,a)&&(n[a]=s[a]);if(Ja){r=Ja(s);for(var o=0;o<r.length;o++)Za.call(s,r[o])&&(n[r[o]]=s[r[o]])}}return n});var Qa="bodyAttributes",eo="htmlAttributes",to="titleAttributes",so={BASE:"base",BODY:"body",HEAD:"head",HTML:"html",LINK:"link",META:"meta",NOSCRIPT:"noscript",SCRIPT:"script",STYLE:"style",TITLE:"title"};Object.keys(so).map((function(e){return so[e]}));var ro,no,io,ao,oo="charset",lo="cssText",co="href",uo="http-equiv",ho="innerHTML",mo="itemprop",po="name",fo="property",go="rel",xo="src",bo="target",yo={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"},vo="defaultTitle",wo="defer",jo="encodeSpecialCharacters",No="onChangeClientState",ko="titleTemplate",_o=Object.keys(yo).reduce((function(e,t){return e[yo[t]]=t,e}),{}),To=[so.NOSCRIPT,so.SCRIPT,so.STYLE],So="data-react-helmet",Eo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ao=function(){function e(e,t){for(var s=0;s<t.length;s++){var r=t[s];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,s,r){return s&&e(t.prototype,s),r&&e(t,r),t}}(),Co=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},Po=function(e,t){var s={};for(var r in e)t.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(e,r)&&(s[r]=e[r]);return s},Oo=function(e){return!1===(!(arguments.length>1&&void 0!==arguments[1])||arguments[1])?String(e):String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")},Ro=function(e){var t=Mo(e,so.TITLE),s=Mo(e,ko);if(s&&t)return s.replace(/%s/g,(function(){return Array.isArray(t)?t.join(""):t}));var r=Mo(e,vo);return t||r||void 0},Io=function(e){return Mo(e,No)||function(){}},Lo=function(e,t){return t.filter((function(t){return void 0!==t[e]})).map((function(t){return t[e]})).reduce((function(e,t){return Co({},e,t)}),{})},$o=function(e,t){return t.filter((function(e){return void 0!==e[so.BASE]})).map((function(e){return e[so.BASE]})).reverse().reduce((function(t,s){if(!t.length)for(var r=Object.keys(s),n=0;n<r.length;n++){var i=r[n].toLowerCase();if(-1!==e.indexOf(i)&&s[i])return t.concat(s)}return t}),[])},Do=function(e,t,s){var r={};return s.filter((function(t){return!!Array.isArray(t[e])||(void 0!==t[e]&&qo("Helmet: "+e+' should be of type "Array". Instead found type "'+Eo(t[e])+'"'),!1)})).map((function(t){return t[e]})).reverse().reduce((function(e,s){var n={};s.filter((function(e){for(var s=void 0,i=Object.keys(e),a=0;a<i.length;a++){var o=i[a],l=o.toLowerCase();-1===t.indexOf(l)||s===go&&"canonical"===e[s].toLowerCase()||l===go&&"stylesheet"===e[l].toLowerCase()||(s=l),-1===t.indexOf(o)||o!==ho&&o!==lo&&o!==mo||(s=o)}if(!s||!e[s])return!1;var c=e[s].toLowerCase();return r[s]||(r[s]={}),n[s]||(n[s]={}),!r[s][c]&&(n[s][c]=!0,!0)})).reverse().forEach((function(t){return e.push(t)}));for(var i=Object.keys(n),a=0;a<i.length;a++){var o=i[a],l=Xa({},r[o],n[o]);r[o]=l}return e}),[]).reverse()},Mo=function(e,t){for(var s=e.length-1;s>=0;s--){var r=e[s];if(r.hasOwnProperty(t))return r[t]}return null},Uo=(ro=Date.now(),function(e){var t=Date.now();t-ro>16?(ro=t,e(t)):setTimeout((function(){Uo(e)}),0)}),Bo=function(e){return clearTimeout(e)},zo="undefined"!=typeof window?window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||Uo:global.requestAnimationFrame||Uo,Fo="undefined"!=typeof window?window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||Bo:global.cancelAnimationFrame||Bo,qo=function(e){return console&&"function"==typeof console.warn&&void 0},Vo=null,Ho=function(e,t){var s=e.baseTag,r=e.bodyAttributes,n=e.htmlAttributes,i=e.linkTags,a=e.metaTags,o=e.noscriptTags,l=e.onChangeClientState,c=e.scriptTags,d=e.styleTags,u=e.title,h=e.titleAttributes;Go(so.BODY,r),Go(so.HTML,n),Ko(u,h);var m={baseTag:Jo(so.BASE,s),linkTags:Jo(so.LINK,i),metaTags:Jo(so.META,a),noscriptTags:Jo(so.NOSCRIPT,o),scriptTags:Jo(so.SCRIPT,c),styleTags:Jo(so.STYLE,d)},p={},f={};Object.keys(m).forEach((function(e){var t=m[e],s=t.newTags,r=t.oldTags;s.length&&(p[e]=s),r.length&&(f[e]=m[e].oldTags)})),t&&t(),l(e,p,f)},Wo=function(e){return Array.isArray(e)?e.join(""):e},Ko=function(e,t){void 0!==e&&document.title!==e&&(document.title=Wo(e)),Go(so.TITLE,t)},Go=function(e,t){var s=document.getElementsByTagName(e)[0];if(s){for(var r=s.getAttribute(So),n=r?r.split(","):[],i=[].concat(n),a=Object.keys(t),o=0;o<a.length;o++){var l=a[o],c=t[l]||"";s.getAttribute(l)!==c&&s.setAttribute(l,c),-1===n.indexOf(l)&&n.push(l);var d=i.indexOf(l);-1!==d&&i.splice(d,1)}for(var u=i.length-1;u>=0;u--)s.removeAttribute(i[u]);n.length===i.length?s.removeAttribute(So):s.getAttribute(So)!==a.join(",")&&s.setAttribute(So,a.join(","))}},Jo=function(e,t){var s=document.head||document.querySelector(so.HEAD),r=s.querySelectorAll(e+"["+So+"]"),n=Array.prototype.slice.call(r),i=[],a=void 0;return t&&t.length&&t.forEach((function(t){var s=document.createElement(e);for(var r in t)if(t.hasOwnProperty(r))if(r===ho)s.innerHTML=t.innerHTML;else if(r===lo)s.styleSheet?s.styleSheet.cssText=t.cssText:s.appendChild(document.createTextNode(t.cssText));else{var o=void 0===t[r]?"":t[r];s.setAttribute(r,o)}s.setAttribute(So,"true"),n.some((function(e,t){return a=t,s.isEqualNode(e)}))?n.splice(a,1):i.push(s)})),n.forEach((function(e){return e.parentNode.removeChild(e)})),i.forEach((function(e){return s.appendChild(e)})),{oldTags:n,newTags:i}},Yo=function(e){return Object.keys(e).reduce((function(t,s){var r=void 0!==e[s]?s+'="'+e[s]+'"':""+s;return t?t+" "+r:r}),"")},Zo=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Object.keys(e).reduce((function(t,s){return t[yo[s]||s]=e[s],t}),t)},Xo=function(e,t,s){switch(e){case so.TITLE:return{toComponent:function(){return e=t.title,s=t.titleAttributes,(r={key:e})[So]=!0,n=Zo(s,r),[a.createElement(so.TITLE,n,e)];var e,s,r,n},toString:function(){return function(e,t,s,r){var n=Yo(s),i=Wo(t);return n?"<"+e+" "+So+'="true" '+n+">"+Oo(i,r)+"</"+e+">":"<"+e+" "+So+'="true">'+Oo(i,r)+"</"+e+">"}(e,t.title,t.titleAttributes,s)}};case Qa:case eo:return{toComponent:function(){return Zo(t)},toString:function(){return Yo(t)}};default:return{toComponent:function(){return function(e,t){return t.map((function(t,s){var r,n=((r={key:s})[So]=!0,r);return Object.keys(t).forEach((function(e){var s=yo[e]||e;if(s===ho||s===lo){var r=t.innerHTML||t.cssText;n.dangerouslySetInnerHTML={__html:r}}else n[s]=t[e]})),a.createElement(e,n)}))}(e,t)},toString:function(){return function(e,t,s){return t.reduce((function(t,r){var n=Object.keys(r).filter((function(e){return!(e===ho||e===lo)})).reduce((function(e,t){var n=void 0===r[t]?t:t+'="'+Oo(r[t],s)+'"';return e?e+" "+n:n}),""),i=r.innerHTML||r.cssText||"",a=-1===To.indexOf(e);return t+"<"+e+" "+So+'="true" '+n+(a?"/>":">"+i+"</"+e+">")}),"")}(e,t,s)}}}},Qo=function(e){var t=e.baseTag,s=e.bodyAttributes,r=e.encode,n=e.htmlAttributes,i=e.linkTags,a=e.metaTags,o=e.noscriptTags,l=e.scriptTags,c=e.styleTags,d=e.title,u=void 0===d?"":d,h=e.titleAttributes;return{base:Xo(so.BASE,t,r),bodyAttributes:Xo(Qa,s,r),htmlAttributes:Xo(eo,n,r),link:Xo(so.LINK,i,r),meta:Xo(so.META,a,r),noscript:Xo(so.NOSCRIPT,o,r),script:Xo(so.SCRIPT,l,r),style:Xo(so.STYLE,c,r),title:Xo(so.TITLE,{title:u,titleAttributes:h},r)}},el=Fa((function(e){return{baseTag:$o([co,bo],e),bodyAttributes:Lo(Qa,e),defer:Mo(e,wo),encode:Mo(e,jo),htmlAttributes:Lo(eo,e),linkTags:Do(so.LINK,[go,co],e),metaTags:Do(so.META,[po,oo,uo,fo,mo],e),noscriptTags:Do(so.NOSCRIPT,[ho],e),onChangeClientState:Io(e),scriptTags:Do(so.SCRIPT,[xo,ho],e),styleTags:Do(so.STYLE,[lo],e),title:Ro(e),titleAttributes:Lo(to,e)}}),(function(e){Vo&&Fo(Vo),e.defer?Vo=zo((function(){Ho(e,(function(){Vo=null}))})):(Ho(e),Vo=null)}),Qo)((function(){return null})),tl=(no=el,ao=io=function(e){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,e.apply(this,arguments))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),t.prototype.shouldComponentUpdate=function(e){return!Ga(this.props,e)},t.prototype.mapNestedChildrenToProps=function(e,t){if(!t)return null;switch(e.type){case so.SCRIPT:case so.NOSCRIPT:return{innerHTML:t};case so.STYLE:return{cssText:t}}throw new Error("<"+e.type+" /> elements are self-closing and can not contain children. Refer to our API for more information.")},t.prototype.flattenArrayTypeChildren=function(e){var t,s=e.child,r=e.arrayTypeChildren,n=e.newChildProps,i=e.nestedChildren;return Co({},r,((t={})[s.type]=[].concat(r[s.type]||[],[Co({},n,this.mapNestedChildrenToProps(s,i))]),t))},t.prototype.mapObjectTypeChildren=function(e){var t,s,r=e.child,n=e.newProps,i=e.newChildProps,a=e.nestedChildren;switch(r.type){case so.TITLE:return Co({},n,((t={})[r.type]=a,t.titleAttributes=Co({},i),t));case so.BODY:return Co({},n,{bodyAttributes:Co({},i)});case so.HTML:return Co({},n,{htmlAttributes:Co({},i)})}return Co({},n,((s={})[r.type]=Co({},i),s))},t.prototype.mapArrayTypeChildrenToProps=function(e,t){var s=Co({},t);return Object.keys(e).forEach((function(t){var r;s=Co({},s,((r={})[t]=e[t],r))})),s},t.prototype.warnOnInvalidChildren=function(e,t){return!0},t.prototype.mapChildrenToProps=function(e,t){var s=this,r={};return a.Children.forEach(e,(function(e){if(e&&e.props){var n=e.props,i=n.children,a=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Object.keys(e).reduce((function(t,s){return t[_o[s]||s]=e[s],t}),t)}(Po(n,["children"]));switch(s.warnOnInvalidChildren(e,i),e.type){case so.LINK:case so.META:case so.NOSCRIPT:case so.SCRIPT:case so.STYLE:r=s.flattenArrayTypeChildren({child:e,arrayTypeChildren:r,newChildProps:a,nestedChildren:i});break;default:t=s.mapObjectTypeChildren({child:e,newProps:t,newChildProps:a,nestedChildren:i})}}})),t=this.mapArrayTypeChildrenToProps(r,t)},t.prototype.render=function(){var e=this.props,t=e.children,s=Po(e,["children"]),r=Co({},s);return t&&(r=this.mapChildrenToProps(t,r)),a.createElement(no,r)},Ao(t,null,[{key:"canUseDOM",set:function(e){no.canUseDOM=e}}]),t}(a.Component),io.propTypes={base:La.object,bodyAttributes:La.object,children:La.oneOfType([La.arrayOf(La.node),La.node]),defaultTitle:La.string,defer:La.bool,encodeSpecialCharacters:La.bool,htmlAttributes:La.object,link:La.arrayOf(La.object),meta:La.arrayOf(La.object),noscript:La.arrayOf(La.object),onChangeClientState:La.func,script:La.arrayOf(La.object),style:La.arrayOf(La.object),title:La.string,titleAttributes:La.object,titleTemplate:La.string},io.defaultProps={defer:!0,encodeSpecialCharacters:!0},io.peek=no.peek,io.rewind=function(){var e=no.rewind();return e||(e=Qo({baseTag:[],bodyAttributes:{},encodeSpecialCharacters:!0,htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}})),e},ao);tl.renderStatic=tl.rewind;const sl="https://blackveil.co.nz",rl=({title:e,description:s,canonicalUrl:r=sl,ogImage:n=`${sl}/lovable-uploads/497cce26-2e85-4fb5-8c91-983cebc78bc4.png`,ogType:i="website",twitterCard:a="summary_large_image",keywords:o="cybersecurity, New Zealand, SMB, email security, DMARC, SPF, DKIM, cyber protection, email fraud, Firetail.ai, Modern Cyber Podcast",imageAlt:l="BlackVeil - Email Security for New Zealand Businesses",imageWidth:c="1200",imageHeight:d="630"})=>{const u=e.includes("BlackVeil")?e:`${e} | BlackVeil`,h=r.startsWith("http")?r:`${sl}${r.startsWith("/")?r:`/${r}`}`,m=n.startsWith("http")?n:`${sl}${n.startsWith("/")?n:`/${n}`}`;return t.jsxs(tl,{children:[t.jsx("title",{children:u}),t.jsx("meta",{name:"description",content:s}),t.jsx("meta",{name:"keywords",content:o}),t.jsx("link",{rel:"canonical",href:h}),t.jsx("meta",{property:"og:title",content:u}),t.jsx("meta",{property:"og:description",content:s}),t.jsx("meta",{property:"og:type",content:i}),t.jsx("meta",{property:"og:url",content:h}),t.jsx("meta",{property:"og:image",content:m}),t.jsx("meta",{property:"og:image:alt",content:l}),t.jsx("meta",{property:"og:image:width",content:c}),t.jsx("meta",{property:"og:image:height",content:d}),t.jsx("meta",{property:"og:site_name",content:"BlackVeil"}),t.jsx("meta",{name:"twitter:card",content:a}),t.jsx("meta",{name:"twitter:title",content:u}),t.jsx("meta",{name:"twitter:description",content:s}),t.jsx("meta",{name:"twitter:image",content:m}),t.jsx("meta",{name:"twitter:image:alt",content:l}),t.jsx("meta",{property:"article:publisher",content:"BlackVeil"})]})},nl=({items:e})=>{const s={"@context":"https://schema.org","@type":"BreadcrumbList",itemListElement:e.map(((e,t)=>({"@type":"ListItem",position:t+1,name:e.name,item:e.url})))};return t.jsx(tl,{children:t.jsx("script",{type:"application/ld+json",children:JSON.stringify(s)})})},il=({title:e,description:s,className:r=""})=>t.jsx(le,{className:`pt-16 xs:pt-20 sm:pt-24 pb-10 xs:pb-12 sm:pb-16 ${r}`,children:t.jsxs("div",{className:"max-w-3xl mx-auto text-center",children:[t.jsx("h1",{className:"text-3xl xs:text-4xl md:text-5xl font-bold mb-4 xs:mb-5 sm:mb-6",children:e}),t.jsx("p",{className:"text-white/70 text-base xs:text-lg",children:s})]})}),al=()=>t.jsx(il,{title:"Resources",description:"Access our comprehensive cybersecurity guides, tools, and resources to help protect your business."}),ol=()=>{r.useEffect((()=>{if("undefined"!=typeof window&&window.performance){window.performance.now()}}),[]);const e=[{icon:u,title:"Email Authentication Guide",description:"Learn how to set up SPF, DKIM, and DMARC for your business email",link:"/blog/1"},{icon:m,title:"Cybersecurity Best Practices",description:"Essential security practices every business should implement",link:"/blog"},{icon:U,title:"Incident Response Plan Template",description:"A customizable template to help you prepare for security incidents",link:"/contact"}];return t.jsxs("div",{children:[t.jsx(rl,{title:"Cybersecurity Resources | BlackVeil",description:"Access BlackVeil's comprehensive cybersecurity resources, including our Emergency Response Guide for businesses, email authentication guides, and security best practices.",canonicalUrl:"https://blackveil.co.nz/resources",keywords:"cybersecurity resources, emergency response guide, incident response, email security, DMARC guide"}),t.jsx(nl,{items:[{name:"Home",url:"https://blackveil.co.nz/"},{name:"Resources",url:"https://blackveil.co.nz/resources"}]}),t.jsx(al,{}),t.jsx(le,{background:"soft",className:"py-16",children:t.jsxs("div",{className:"max-w-6xl mx-auto",children:[t.jsx("h2",{className:"text-2xl md:text-3xl font-bold mb-8 text-center",children:"Additional Resources"}),t.jsx("div",{className:"grid md:grid-cols-3 gap-6",children:e.map(((e,r)=>t.jsxs(s,{to:e.link,className:"cyber-card p-6 border border-green-muted/30 hover:border-green-muted/70 transition-all group",children:[t.jsx("div",{className:"mb-4 text-green-bright",children:t.jsx(e.icon,{className:"w-8 h-8"})}),t.jsx("h3",{className:"text-lg font-bold mb-2 group-hover:text-green-bright transition-colors",children:e.title}),t.jsx("p",{className:"text-white/70",children:e.description})]},r)))}),t.jsxs("div",{className:"mt-12 text-center",children:[t.jsx("p",{className:"text-white/70 mb-6",children:"Get Your Free Security Incident Response Template—Take Action Today"}),t.jsx(s,{to:"/contact",className:"cyber-button",children:"Request My Free Template"})]})]})})]})};ol.displayName="ResourcesPage";const ll=({background:e="grid"})=>t.jsxs(le,{background:e,className:"py-24",children:[t.jsxs("div",{className:"text-center mb-16",children:[t.jsx("span",{className:"font-mono uppercase tracking-wider text-xs bg-green-dark/70 text-green-light px-4 py-1.5 rounded-full cyber-glow",children:"Our Process"}),t.jsx("h2",{className:"text-3xl md:text-4xl font-bold mt-5 mb-4 cyber-glow-text",children:"How We Protect Your Business"}),t.jsx("div",{className:"w-24 h-1 bg-gradient-to-r from-green-bright to-transparent mx-auto rounded-full mb-6"}),t.jsx("p",{className:"text-white/80 max-w-2xl mx-auto leading-relaxed",children:"Our streamlined three-step approach makes enterprise-grade protection accessible and manageable for businesses of all sizes."})]}),t.jsxs("div",{className:"grid md:grid-cols-3 gap-10 max-w-6xl mx-auto",children:[t.jsxs("div",{className:"cyber-gradient-card p-8 border border-green-muted/30 rounded-lg transform transition-all duration-500 hover:translate-y-[-8px] hover:shadow-[0_10px_30px_rgba(0,0,0,0.5)] group",children:[t.jsx("div",{className:"mb-6 inline-flex p-4 rounded-lg bg-green-dark/40 group-hover:bg-green-dark/60 transition-all duration-300",children:t.jsx(u,{className:"h-7 w-7 text-green-bright"})}),t.jsx("h3",{className:"text-xl font-bold mb-4 cyber-glow-text",children:"1. Security Check"}),t.jsx("p",{className:"text-white/80 mb-5 leading-relaxed",children:"We identify all security gaps in your email systems through comprehensive scanning and assessment."}),t.jsxs("ul",{className:"cyber-bullet-list text-white/80 mb-6 space-y-2",children:[t.jsx("li",{children:"Email vulnerability scanning"}),t.jsx("li",{children:"System security testing"}),t.jsx("li",{children:"Policy and procedure review"})]}),t.jsxs(s,{to:"/services",className:"inline-flex items-center text-green-bright hover:text-green-light transition-colors font-mono text-sm group-hover:translate-x-1 transform transition-transform duration-300",children:[t.jsx("span",{children:"Learn more"}),t.jsx("svg",{className:"ml-2 w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M14 5l7 7m0 0l-7 7m7-7H3"})})]})]}),t.jsxs("div",{className:"cyber-gradient-card p-8 border border-green-muted/30 rounded-lg transform transition-all duration-500 hover:translate-y-[-8px] hover:shadow-[0_10px_30px_rgba(0,0,0,0.5)] group",children:[t.jsx("div",{className:"mb-6 inline-flex p-4 rounded-lg bg-green-dark/40 group-hover:bg-green-dark/60 transition-all duration-300",children:t.jsx(B,{className:"h-7 w-7 text-green-bright"})}),t.jsx("h3",{className:"text-xl font-bold mb-4 cyber-glow-text",children:"2. Setup"}),t.jsx("p",{className:"text-white/80 mb-5 leading-relaxed",children:"We set up email protection based on our findings, prioritizing critical vulnerabilities first."}),t.jsxs("ul",{className:"cyber-bullet-list text-white/80 mb-6 space-y-2",children:[t.jsx("li",{children:"Prioritized protection steps"}),t.jsx("li",{children:"Clear implementation process"}),t.jsx("li",{children:"Cost-effective solutions"})]}),t.jsxs(s,{to:"/services",className:"inline-flex items-center text-green-bright hover:text-green-light transition-colors font-mono text-sm group-hover:translate-x-1 transform transition-transform duration-300",children:[t.jsx("span",{children:"Learn more"}),t.jsx("svg",{className:"ml-2 w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M14 5l7 7m0 0l-7 7m7-7H3"})})]})]}),t.jsxs("div",{className:"cyber-gradient-card p-8 border border-green-muted/30 rounded-lg transform transition-all duration-500 hover:translate-y-[-8px] hover:shadow-[0_10px_30px_rgba(0,0,0,0.5)] group",children:[t.jsx("div",{className:"mb-6 inline-flex p-4 rounded-lg bg-green-dark/40 group-hover:bg-green-dark/60 transition-all duration-300",children:t.jsx(z,{className:"h-7 w-7 text-green-bright"})}),t.jsx("h3",{className:"text-xl font-bold mb-4 cyber-glow-text",children:"3. Ongoing Protection"}),t.jsx("p",{className:"text-white/80 mb-5 leading-relaxed",children:"We provide continuous monitoring and support to ensure your systems remain secure against evolving threats."}),t.jsxs("ul",{className:"cyber-bullet-list text-white/80 mb-6 space-y-2",children:[t.jsx("li",{children:"24/7 security monitoring"}),t.jsx("li",{children:"Regular security updates"}),t.jsx("li",{children:"Threat intelligence reporting"})]}),t.jsxs(s,{to:"/services",className:"inline-flex items-center text-green-bright hover:text-green-light transition-colors font-mono text-sm group-hover:translate-x-1 transform transition-transform duration-300",children:[t.jsx("span",{children:"Learn more"}),t.jsx("svg",{className:"ml-2 w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M14 5l7 7m0 0l-7 7m7-7H3"})})]})]})]})]}),cl=({name:e,company:s,companyUrl:r,initials:n,text:i,featured:a=!1,className:o=""})=>t.jsxs("div",{className:ae("relative bg-black-soft/90 border border-green-muted/30 rounded-lg p-6 shadow-lg flex flex-col gap-4 animate-fade-in h-full",a&&"border-green-bright/30 shadow-lg shadow-green-bright/5",o),children:[t.jsx("div",{className:ae("absolute -top-4 -left-3 w-8 h-8",a?"text-green-bright/80":"text-green-bright/60"),children:t.jsx(F,{className:"w-full h-full"})}),t.jsx("p",{className:ae("text-white/80 italic",a?"text-lg":"text-base","mb-auto"),children:`"${i}"`}),t.jsxs("div",{className:"flex items-center mt-4",children:[t.jsx("div",{className:ae("w-10 h-10 rounded-full flex items-center justify-center mr-3",a?"bg-green-dark/80":"bg-green-dark/60"),children:t.jsx("span",{className:"text-green-bright font-bold",children:n})}),t.jsxs("div",{children:[t.jsx("h4",{className:"font-bold text-white",children:e}),t.jsx("a",{href:r,target:"_blank",rel:"noopener noreferrer",className:"text-green-bright hover:text-green-light text-xs transition-colors",children:s})]})]})]}),dl=[{name:"Alexander PR",company:"alexanderpr.co.nz",companyUrl:"https://alexanderpr.co.nz",initials:"AP",text:"Adam and his team at BlackVeil are bright and always on top of the latest developments in cyber and email security. What sets them apart is their genuine care and readiness to assist clients immediately when threats like ransomware or phishing attacks are suspected. We recommend BlackVeil for their practical, enterprise-grade cybersecurity solutions tailored for SMBs, combined with their swift crisis response and clear communication that helps businesses stay secure and confident.",featured:!0},{name:"Phreaze Factory",company:"phreazefactory.com",companyUrl:"https://phreazefactory.com",initials:"PF",text:"We had an issue with our emails going to peoples spam or junkmail folders. This was problematic for a lot of reasons as a business but Blackveil was able to find the problem and fix it straight away for us! He was super-quick and it's obvious he really knows his stuff! Highly recommend Blackveil services to everyone we know who has any sort of internet security issue!",featured:!1},{name:"AONE",company:"aone.co.nz",companyUrl:"https://aone.co.nz",initials:"EA",text:"Blackveil has transformed the way we handle email security and IT solutions. Their insights into email security and proactive IT solutions have saved us time and money while keeping our systems safe. The team's professionalism and expertise are unmatched—highly recommended!",featured:!1},{name:"Topstone",company:"topstone.co.nz",companyUrl:"https://topstone.co.nz",initials:"TS",text:"BlackVeil provided us with a seamless and secure email migration. Their expertise ensured zero downtime and improved our overall security posture. The team was responsive, knowledgeable, and a pleasure to work with.",featured:!1}];function ul({className:e,...s}){return t.jsx("div",{className:ae("animate-pulse rounded-md bg-muted",e),...s})}const hl=({count:e=3,className:s})=>t.jsx("div",{className:ae("grid gap-6 md:grid-cols-2 lg:grid-cols-3",s),children:Array.from({length:e}).map(((e,s)=>t.jsxs("div",{className:"bg-black-soft/30 border border-green-muted/20 rounded-lg p-6 space-y-4",role:"status","aria-label":"Loading testimonial",children:[t.jsx("div",{className:"flex space-x-1",children:Array.from({length:5}).map(((e,s)=>t.jsx(ul,{className:"h-4 w-4 rounded-sm"},s)))}),t.jsxs("div",{className:"space-y-2",children:[t.jsx(ul,{className:"h-4 w-full"}),t.jsx(ul,{className:"h-4 w-4/5"}),t.jsx(ul,{className:"h-4 w-3/4"})]}),t.jsxs("div",{className:"flex items-center gap-3 pt-4",children:[t.jsx(ul,{className:"h-10 w-10 rounded-full"}),t.jsxs("div",{className:"space-y-2",children:[t.jsx(ul,{className:"h-4 w-24"}),t.jsx(ul,{className:"h-3 w-32"})]})]})]},s)))}),ml=()=>{const[e,s]=r.useState(!0);return r.useEffect((()=>{const e=setTimeout((()=>{s(!1)}),800);return()=>clearTimeout(e)}),[]),t.jsxs(le,{className:"py-16 md:py-24",children:[t.jsxs("div",{className:"text-center mb-12 md:mb-16",children:[t.jsxs("h2",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold mb-4 md:mb-6",children:["Trusted by ",t.jsx("span",{className:"text-green-bright",children:"NZ Businesses"})]}),t.jsx("p",{className:"text-lg md:text-xl text-white/80 max-w-3xl mx-auto",children:"See what our clients say about their cybersecurity transformation"})]}),e?t.jsx(hl,{count:3}):t.jsx("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:dl.slice(0,6).map(((e,s)=>t.jsx(cl,{name:e.name,company:e.company,companyUrl:e.companyUrl,initials:e.initials,text:e.text,featured:e.featured},s)))})]})},pl=$e,fl=r.forwardRef((({className:e,...s},r)=>t.jsx(Oe,{ref:r,className:ae("border-b",e),...s})));fl.displayName="AccordionItem";const gl=r.forwardRef((({className:e,children:s,...r},i)=>t.jsx(Re,{className:"flex",children:t.jsxs(Ie,{ref:i,className:ae("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",e),...r,children:[s,t.jsx(n,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})})));gl.displayName=Ie.displayName;const xl=r.forwardRef((({className:e,children:s,...r},n)=>t.jsx(Le,{ref:n,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...r,children:t.jsx("div",{className:ae("pb-4 pt-0",e),children:s})})));xl.displayName=Le.displayName;const bl=[{question:"What cyber security solutions do you offer?",answer:"We provide enterprise-grade cyber security services including vulnerability assessments, endpoint protection, email security (DMARC/SPF/DKIM), network monitoring, incident response, and compliance consulting. Our solutions are delivered remotely and are specifically designed for SMEs worldwide, focusing on practical, cost-effective protection without compromising on security."},{question:"How quickly can you respond to security incidents?",answer:"Our Security Operations Center (SOC) provides 24/7 monitoring and incident response. We typically acknowledge critical security alerts within 15 minutes and begin incident response within 1 hour. For severe incidents, we can deploy emergency response measures immediately while keeping you informed throughout the process."},{question:"What is involved in your security assessment process?",answer:"Our security assessment follows a structured approach: 1) Initial system discovery and asset inventory, 2) Vulnerability scanning and penetration testing, 3) Security control review and gap analysis, 4) Email security configuration analysis, 5) Third-party risk assessment, and 6) Detailed reporting with prioritized recommendations. The process typically takes 2-3 weeks, depending on your infrastructure size."},{question:"How do you handle data privacy and compliance requirements?",answer:"We maintain strict compliance with international data protection regulations including GDPR, HIPAA, and NZ Privacy Act 2020. Our services are designed to help you meet specific industry requirements while protecting sensitive data. We use encrypted channels for all communications and maintain detailed audit logs for compliance purposes."},{question:"What makes BlackVeil different from other security providers?",answer:"BlackVeil specializes in delivering enterprise-grade security specifically tailored for SMEs. We combine advanced technology with practical solutions that don't require technical expertise to implement. Our remote-first approach allows us to provide 24/7 protection while keeping costs manageable. We focus on proactive protection rather than just reactive measures."},{question:"What are the immediate steps to improve our email security?",answer:"We begin with implementing essential email authentication protocols (DMARC, SPF, DKIM) to prevent email spoofing and phishing. This includes configuring proper DNS records, monitoring email delivery, and gradually enforcing stricter policies. We also provide anti-phishing training and set up advanced threat detection for your email infrastructure."},{question:"How do you handle remote work security challenges?",answer:"Our remote work security solution includes secure VPN access, endpoint protection, multi-factor authentication, and cloud security controls. We help establish secure remote access policies, implement zero-trust security frameworks, and provide ongoing monitoring of remote endpoints. This ensures your team can work securely from anywhere while maintaining compliance."},{question:"What ongoing support do you provide?",answer:"Our managed security service includes 24/7 monitoring, regular security updates, vulnerability management, incident response, and access to our security experts. We provide monthly security reports, quarterly review meetings, and continuous improvement recommendations. Emergency support is available via phone, email, or our secure portal."},{question:"How do you measure security effectiveness?",answer:"We track key security metrics including incident response times, threat detection rates, vulnerability remediation speed, and system uptime. Monthly reports show your security posture improvements, risk reduction metrics, and compliance status. We use these metrics to continuously refine and enhance your security controls."},{question:"What are your pricing options?",answer:"We offer flexible, subscription-based pricing tailored to your business size and security needs. All services are priced on application (POA) to ensure you receive a customized security package. Our transparent pricing model includes all monitoring, support, and regular assessments with no hidden costs. Contact us for a detailed quote based on your requirements."}],yl=({background:e="soft"})=>t.jsx(le,{background:e,children:t.jsxs("div",{className:"grid md:grid-cols-5 gap-12",children:[t.jsxs("div",{className:"md:col-span-2",children:[t.jsx("span",{className:"font-mono uppercase tracking-wider text-xs bg-green-dark/70 text-green-light px-4 py-1.5 rounded-full cyber-glow mb-6 inline-block",children:"Questions & Answers"}),t.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Frequently Asked Questions"}),t.jsx("p",{className:"text-white/70 mb-8",children:"Find detailed answers about our cyber security services and how we protect businesses worldwide. For specific inquiries, our security experts are ready to help."}),t.jsx(s,{to:"/contact",className:"cyber-button",children:"Contact Our Security Team"})]}),t.jsx("div",{className:"md:col-span-3",children:t.jsx(pl,{type:"single",collapsible:!0,className:"space-y-4",children:bl.map(((e,s)=>t.jsxs(fl,{value:`item-${s}`,className:"cyber-card border-none",children:[t.jsx(gl,{className:"text-lg font-medium py-4 px-4",children:e.question}),t.jsx(xl,{className:"text-white/70 px-4 pb-4",children:e.answer})]},s)))})})]})}),vl=()=>t.jsxs(le,{className:"py-16 md:py-24 bg-gradient-to-r from-black-soft via-green-dark/10 to-black-soft relative overflow-hidden",children:[t.jsx("div",{className:"absolute inset-0 opacity-5",children:t.jsx("div",{className:"absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMwMGZmOGMiIGZpbGwtb3BhY2l0eT0iMC4xIj48Y2lyY2xlIGN4PSIzMCIgY3k9IjMwIiByPSIxIi8+PC9nPjwvZz48L3N2Zz4=')] bg-repeat"})}),t.jsxs("div",{className:"relative z-10 text-center max-w-4xl mx-auto",children:[t.jsxs("div",{className:"inline-flex items-center gap-2 bg-gradient-to-r from-green-900/30 to-blue-900/30 border border-green-500/20 px-4 py-2 rounded-full mb-6",children:[t.jsx(y,{className:"h-5 w-5 text-green-400"}),t.jsx("span",{className:"text-green-200 font-medium",children:"SME Cybersecurity Specialists"})]}),t.jsxs("h2",{className:"text-3xl md:text-4xl lg:text-5xl font-bold mb-6",children:["Ready to Secure Your ",t.jsx("span",{className:"text-green-bright",children:"Business?"})]}),t.jsx("p",{className:"text-xl md:text-2xl text-white/80 mb-8 leading-relaxed",children:"Don't wait for a security incident to realize you need protection. Get enterprise-grade cybersecurity tailored for your SME today."}),t.jsx("div",{className:"flex flex-col sm:flex-row gap-6 justify-center items-center mb-8",children:t.jsx(Fn,{variant:"default",layout:"horizontal",showDmarc:!1,showEmergency:!0})}),t.jsx("div",{className:"text-center",children:t.jsxs(s,{to:"/about",className:"inline-flex items-center text-green-300 hover:text-green-200 font-medium transition-colors",children:["Learn more about our approach to SME cybersecurity",t.jsx(_,{className:"w-4 h-4 ml-1"})]})}),t.jsxs("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 text-center",children:[t.jsxs("div",{className:"p-4 bg-black-soft/50 rounded-lg border border-green-muted/20",children:[t.jsx("div",{className:"text-2xl font-bold text-green-bright mb-2",children:"2-4 Weeks"}),t.jsx("div",{className:"text-white/70",children:"Implementation Time"})]}),t.jsxs("div",{className:"p-4 bg-black-soft/50 rounded-lg border border-green-muted/20",children:[t.jsx("div",{className:"text-2xl font-bold text-green-bright mb-2",children:"24/7"}),t.jsx("div",{className:"text-white/70",children:"Incident Response"})]}),t.jsxs("div",{className:"p-4 bg-black-soft/50 rounded-lg border border-green-muted/20",children:[t.jsx("div",{className:"text-2xl font-bold text-green-bright mb-2",children:"100%"}),t.jsx("div",{className:"text-white/70",children:"SME Focused"})]})]})]})]}),wl=()=>{const[e,n]=r.useState(!1);return r.useEffect((()=>{n(!0)}),[]),t.jsxs(le,{background:"soft",className:"py-24 relative overflow-hidden",children:[t.jsx("div",{className:`absolute top-40 left-1/3 w-80 h-80 rounded-full bg-green-glow blur-[100px] opacity-0 ${e?"animate-glow":""} -z-10`}),t.jsx("div",{className:`absolute bottom-20 right-1/3 w-64 h-64 rounded-full bg-green-glow blur-[80px] opacity-0 ${e?"animate-glow":""} -z-10`,style:{animationDelay:"1s"}}),t.jsxs("div",{className:"max-w-5xl mx-auto",children:[t.jsxs("div",{className:"text-center mb-16",children:[t.jsx("span",{className:"font-mono uppercase tracking-wider text-xs bg-green-dark/70 text-green-light \n            px-4 py-1.5 rounded-full cyber-glow inline-block mb-4 opacity-0 "+(e?"animate-fade-in":""),children:"Our Story"}),t.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-4 cyber-glow-text opacity-0 "+(e?"animate-fade-in":""),style:{animationDelay:"0.2s"},children:"About BlackVeil"}),t.jsx("div",{className:"w-0 h-1 bg-gradient-to-r from-green-bright to-transparent mx-auto rounded-full mb-6 \n            transition-all duration-1000 opacity-0 "+(e?"w-24 opacity-100":"")}),t.jsx("p",{className:"text-white/80 max-w-2xl mx-auto leading-relaxed mb-6 opacity-0 "+(e?"animate-fade-in":""),style:{animationDelay:"0.3s"},children:"Founded on practical research and real-world insights, BlackVeil was created to address the critical email security vulnerabilities we discovered across thousands of domains."}),t.jsx("div",{className:"max-w-3xl mx-auto mb-12 opacity-0 "+(e?"animate-fade-in":""),style:{animationDelay:"0.4s"},children:t.jsx("div",{className:"cyber-gradient-card border border-green-muted/40 rounded-lg p-5 shadow-[0_10px_30px_rgba(0,0,0,0.3)]",children:t.jsxs("div",{className:"flex flex-col md:flex-row items-center gap-5",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx("div",{className:"p-3 bg-black/50 rounded-full border border-green-muted/30",children:t.jsx(q,{className:"h-8 w-8 text-green-bright"})})}),t.jsxs("div",{className:"flex-grow text-center md:text-left",children:[t.jsxs("div",{className:"mb-1 space-x-2",children:[t.jsx("span",{className:"inline-block font-mono uppercase tracking-wider text-xs bg-green-dark/70 text-green-light px-2 py-0.5 rounded-full text-[10px] md:text-xs transform transition-all duration-300 hover:bg-green-dark/90",children:"Featured"}),t.jsx("span",{className:"inline-block font-mono uppercase tracking-wider text-xs bg-red-900/70 text-red-300 px-2 py-0.5 rounded-full text-[10px] md:text-xs transform transition-all duration-300 hover:bg-red-900/90",children:"Podcast"})]}),t.jsx("h4",{className:"font-bold text-lg md:text-xl mb-1 cyber-glow-text",children:"Industry Recognition"}),t.jsxs("p",{className:"text-white/80 text-sm",children:["Our CEO discusses email security innovations and protecting businesses against cyber threats on the ",t.jsx("a",{href:"https://podcasts.apple.com/us/podcast/breach-series-4-adam-burns-of-blackveil/id1741968392?i=1000703039037",target:"_blank",rel:"noopener noreferrer",className:"text-green-bright hover:underline hover:text-green-light transition-colors",children:"Breach Series Podcast"})," by Firetail.ai."]})]}),t.jsx("div",{className:"flex-shrink-0 flex space-x-3",children:t.jsxs("a",{href:"https://www.youtube.com/watch?v=W4aJHpfB5rY",target:"_blank",rel:"noopener noreferrer",className:"cyber-button text-xs flex items-center gap-1.5 py-1.5 px-3 group hover:scale-105 transition-transform",children:[t.jsx(V,{className:"h-3.5 w-3.5 group-hover:scale-110 transition-transform"}),t.jsx("span",{children:"Watch"})]})})]})})})]}),t.jsxs("div",{className:"grid md:grid-cols-2 gap-10 items-center",children:[t.jsxs("div",{className:"cyber-gradient-card border border-green-muted/40 rounded-lg p-8 shadow-[0_10px_30px_rgba(0,0,0,0.3)] opacity-0 "+(e?"animate-fade-in":""),style:{animationDelay:"0.3s"},children:[t.jsx("h3",{className:"text-xl font-bold mb-3 cyber-glow-text",children:"The Origin Story"}),t.jsx("div",{className:"w-16 h-1 bg-gradient-to-r from-green-bright to-transparent rounded-full mb-4"}),t.jsx("p",{className:"text-white/90 mb-4 leading-relaxed",children:"BlackVeil was born after we witnessed the same types of attacks happening over and over again. We knew SPF, DKIM, and DMARC adoption rates were low, and while there were plenty of statistics around it, we wanted proof."}),t.jsx("p",{className:"text-white/90 mb-4 leading-relaxed",children:"Driven by curiosity and a desire to understand the scope of the problem, we built a Python web crawler to scan the .co.nz TLD. We left it running for 6 weeks to collect comprehensive data and generate detailed reports."}),t.jsxs("div",{className:"flex items-center mt-8 text-green-bright",children:[t.jsx(u,{className:"w-6 h-6 mr-3 animate-pulse-slow"}),t.jsx("span",{className:"font-mono text-sm",children:"REVEALING THE VULNERABILITY"})]})]}),t.jsxs("div",{className:"cyber-gradient-card border border-green-muted/40 rounded-lg p-8 shadow-[0_10px_30px_rgba(0,0,0,0.3)] opacity-0 "+(e?"animate-fade-in":""),style:{animationDelay:"0.4s"},children:[t.jsx("h3",{className:"text-xl font-bold mb-3 cyber-glow-text",children:"The Problem & Solution"}),t.jsx("div",{className:"w-16 h-1 bg-gradient-to-r from-green-bright to-transparent rounded-full mb-4"}),t.jsx("p",{className:"text-white/90 mb-4 leading-relaxed",children:"After the data was collated and reported on, it became obvious that a specialized product was required to help fix this systemic issue with email security. The majority of businesses worldwide lacked proper email authentication protocols."}),t.jsx("p",{className:"text-white/90 mb-4 leading-relaxed",children:"This realization led to the birth of BlackVault - our comprehensive email security auditing application, providing a protective layer against email-based cyber threats and offering businesses the tools they need to secure their communications."}),t.jsxs("div",{className:"flex items-center mt-8 text-green-bright",children:[t.jsx(u,{className:"w-6 h-6 mr-3 animate-pulse-slow"}),t.jsx("span",{className:"font-mono text-sm",children:"CREATING THE SOLUTION"})]})]})]}),t.jsx("div",{className:"text-center mt-12",children:t.jsxs(s,{to:"/about",className:"cyber-button flex items-center gap-2 mx-auto w-fit",children:[t.jsx("span",{children:"Read Our Full Story"}),t.jsx(_,{className:"w-4 h-4"})]})})]})]})},jl=({name:e="BlackVeil",url:s="https://blackveil.co.nz",logo:r="https://blackveil.co.nz/lovable-uploads/497cce26-2e85-4fb5-8c91-983cebc78bc4.png",sameAs:n=["https://www.linkedin.com/company/blackveil","https://twitter.com/blackveilsec"],description:i="BlackVeil provides enterprise-grade cybersecurity protection for New Zealand SMBs.",contactEmail:a="<EMAIL>",contactPhone:o,address:l})=>{const c={"@context":"https://schema.org","@type":"Organization",name:e,url:s,logo:r,sameAs:n,description:i,contactPoint:a||o?[{"@type":"ContactPoint",email:a,telephone:o,contactType:"customer support"}]:void 0,address:l?{"@type":"PostalAddress",...l}:void 0},d=JSON.parse(JSON.stringify(c));return t.jsx(tl,{children:t.jsx("script",{type:"application/ld+json",children:JSON.stringify(d)})})},Nl=({faqs:e})=>{const s={"@context":"https://schema.org","@type":"FAQPage",mainEntity:e.map((e=>({"@type":"Question",name:e.question,acceptedAnswer:{"@type":"Answer",text:e.answer}})))};return t.jsx(tl,{children:t.jsx("script",{type:"application/ld+json",children:JSON.stringify(s)})})},kl={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}}},_l=[{title:"Security Assessment",description:"Comprehensive analysis of your current security posture with actionable recommendations.",icon:H},{title:"Managed Protection",description:"24/7 monitoring and management of your security infrastructure.",icon:u},{title:"Incident Response",description:"Rapid response to security threats and breaches with minimal business disruption.",icon:W},{title:"Compliance Support",description:"Meet regulatory requirements with our specialised compliance services.",icon:K}],Tl=()=>t.jsxs(le,{className:"py-16 lg:py-20 bg-gradient-to-b from-black to-black-soft",children:[t.jsxs(ie.div,{initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},variants:kl,className:"text-center mb-16",children:[t.jsx("div",{className:"inline-flex items-center justify-center px-4 py-1.5 bg-green-dark/30 rounded-full mb-4",children:t.jsx("span",{className:"text-green-bright font-medium text-sm",children:"Our Services"})}),t.jsx("h2",{className:"text-3xl md:text-4xl lg:text-5xl font-bold mb-4",children:"Our Core Services"}),t.jsx("p",{className:"text-white/70 max-w-2xl mx-auto text-lg",children:"Comprehensive cyber security solutions tailored for businesses of all sizes across New Zealand"})]}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:_l.map(((e,s)=>t.jsxs(ie.div,{initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},variants:kl,transition:{delay:.1*s},whileHover:{y:-8,transition:{duration:.3}},className:"cyber-card relative backdrop-blur-sm border border-green-muted/30 hover:border-green-muted/70 transition-all duration-300 hover:shadow-[0_0_25px_rgba(0,255,140,0.2)]",children:[t.jsx("div",{className:"absolute inset-0 bg-gradient-to-b from-green-bright/5 to-transparent rounded-lg opacity-50"}),t.jsxs("div",{className:"relative z-10",children:[t.jsx("div",{className:"mb-6 text-green-bright",children:t.jsx(e.icon,{size:36})}),t.jsx("h3",{className:"text-xl font-semibold mb-3",children:e.title}),t.jsx("p",{className:"text-white/70 mb-6",children:e.description})]})]},e.title)))}),t.jsx(ie.div,{initial:"hidden",whileInView:"visible",viewport:{once:!0},variants:kl,className:"text-center mt-12",children:t.jsxs("a",{href:"/services",className:"group inline-flex items-center gap-2 bg-black border border-green-muted/50 hover:border-green-bright text-green-bright px-6 py-3 rounded-md font-medium transition-all duration-300 hover:shadow-[0_0_15px_rgba(0,255,140,0.25)]",children:[t.jsx("span",{children:"View All Services"}),t.jsx(_,{className:"transform transition-transform duration-300 group-hover:translate-x-1"})]})})]}),Sl=r.forwardRef((({className:e,...s},r)=>t.jsx("div",{ref:r,className:ae("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s})));Sl.displayName="Card";const El=r.forwardRef((({className:e,...s},r)=>t.jsx("div",{ref:r,className:ae("flex flex-col space-y-1.5 p-6",e),...s})));El.displayName="CardHeader";const Al=r.forwardRef((({className:e,...s},r)=>t.jsx("h3",{ref:r,className:ae("text-2xl font-semibold leading-none tracking-tight",e),...s})));Al.displayName="CardTitle";const Cl=r.forwardRef((({className:e,...s},r)=>t.jsx("p",{ref:r,className:ae("text-sm text-muted-foreground",e),...s})));Cl.displayName="CardDescription";const Pl=r.forwardRef((({className:e,...s},r)=>t.jsx("div",{ref:r,className:ae("p-6 pt-0",e),...s})));Pl.displayName="CardContent";r.forwardRef((({className:e,...s},r)=>t.jsx("div",{ref:r,className:ae("flex items-center p-6 pt-0",e),...s}))).displayName="CardFooter";const Ol=()=>{const e=13,r="Phishing Attacks on New Zealand Businesses: 2025 Statistics & Prevention Strategies",n="73% of New Zealand businesses faced phishing attempts in 2025. Learn about the latest attack methods, real case studies, and how simulation training can reduce incidents by 82%.",i="BlackVeil Security Team",a="May 30, 2025",o="12 min read",l="Security Awareness";return t.jsx("section",{className:"py-12 sm:py-16 lg:py-20 bg-black-soft",children:t.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[t.jsxs("div",{className:"text-center mb-8 sm:mb-12",children:[t.jsxs("div",{className:"inline-flex items-center gap-2 bg-green-dark/20 border border-green-muted/30 px-3 py-1 rounded-full mb-4",children:[t.jsx(u,{className:"h-4 w-4 text-green-bright"}),t.jsx("span",{className:"text-green-bright text-sm font-medium",children:"Latest Research"})]}),t.jsx("h2",{className:"text-2xl sm:text-3xl lg:text-4xl font-bold mb-4",children:"Featured Article"}),t.jsx("p",{className:"text-white/70 text-lg max-w-2xl mx-auto",children:"Stay informed about the latest cybersecurity threats and protection strategies for New Zealand businesses"})]}),t.jsx(Sl,{className:"cyber-card max-w-4xl mx-auto hover:border-green-bright/60 transition-all duration-300",children:t.jsx(Pl,{className:"p-6 sm:p-8",children:t.jsxs("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-8",children:[t.jsx("div",{className:"lg:w-1/4 flex justify-center lg:justify-start",children:t.jsx("div",{className:"w-24 h-24 sm:w-32 sm:h-32 bg-green-dark/20 rounded-lg flex items-center justify-center border border-green-muted/30",children:t.jsx(u,{className:"h-12 w-12 sm:h-16 sm:w-16 text-green-bright"})})}),t.jsxs("div",{className:"lg:w-3/4",children:[t.jsxs("div",{className:"mb-4",children:[t.jsx("span",{className:"cyber-tag bg-orange-dark/80 text-xs py-1 px-2 mb-3 inline-block",children:l}),t.jsx("h3",{className:"text-xl sm:text-2xl lg:text-3xl font-bold mb-3 text-white",children:r}),t.jsx("p",{className:"text-white/70 text-base sm:text-lg mb-4 leading-relaxed",children:n})]}),t.jsxs("div",{className:"flex flex-wrap items-center gap-4 text-sm text-white/60 mb-6",children:[t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(E,{className:"h-4 w-4"}),t.jsx("span",{children:a})]}),t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(A,{className:"h-4 w-4"}),t.jsx("span",{children:i})]}),t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(z,{className:"h-4 w-4"}),t.jsx("span",{children:o})]})]}),t.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[t.jsx(Mn,{asChild:!0,className:"bg-green-bright hover:bg-green-muted text-black font-semibold",children:t.jsxs(s,{to:`/blog/${e}`,children:["Read Full Article",t.jsx(_,{className:"ml-2 h-4 w-4"})]})}),t.jsx(Mn,{variant:"outline",asChild:!0,className:"border-green-muted/50 text-green-bright hover:bg-green-dark/20",children:t.jsx(s,{to:"/blog",children:"View All Articles"})})]})]})]})})})]})})},Rl={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}}},Il=()=>t.jsxs(le,{className:"py-16 lg:py-20 bg-gradient-to-b from-black-soft to-black relative overflow-hidden",children:[t.jsx("div",{className:"absolute top-10 right-10 w-64 h-64 bg-orange-500/5 rounded-full blur-3xl"}),t.jsx("div",{className:"absolute bottom-10 left-10 w-96 h-96 bg-red-500/5 rounded-full blur-3xl"}),t.jsxs("div",{className:"container relative z-10",children:[t.jsxs(ie.div,{initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},variants:Rl,className:"text-center mb-12",children:[t.jsxs("div",{className:"inline-flex items-center gap-2 bg-orange-900/30 border border-orange-500/30 px-4 py-2 rounded-full mb-6",children:[t.jsx(d,{className:"h-5 w-5 text-orange-400"}),t.jsx("span",{className:"text-orange-200 font-medium",children:"Free Security Assessment"})]}),t.jsxs("h2",{className:"text-3xl md:text-4xl lg:text-5xl font-bold mb-6",children:["Test Your Security in ",t.jsx("span",{className:"text-green-bright",children:"5 Minutes"})]}),t.jsx("p",{className:"text-xl text-white/80 max-w-3xl mx-auto leading-relaxed",children:"Discover your organization's vulnerability to phishing attacks with our comprehensive assessment. Get personalized recommendations to protect your New Zealand business."})]}),t.jsxs("div",{className:"grid md:grid-cols-3 gap-8 mb-12",children:[t.jsxs(ie.div,{initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},variants:Rl,transition:{delay:.1},className:"text-center p-6 bg-gradient-to-b from-green-dark/20 to-transparent rounded-lg border border-green-muted/30",children:[t.jsx(z,{className:"h-12 w-12 text-green-bright mx-auto mb-4"}),t.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Quick & Easy"}),t.jsx("p",{className:"text-white/70",children:"Complete in just 5 minutes with instant results"})]}),t.jsxs(ie.div,{initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},variants:Rl,transition:{delay:.2},className:"text-center p-6 bg-gradient-to-b from-green-dark/20 to-transparent rounded-lg border border-green-muted/30",children:[t.jsx(u,{className:"h-12 w-12 text-green-bright mx-auto mb-4"}),t.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Expert Analysis"}),t.jsx("p",{className:"text-white/70",children:"Professional cybersecurity insights tailored for NZ businesses"})]}),t.jsxs(ie.div,{initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},variants:Rl,transition:{delay:.3},className:"text-center p-6 bg-gradient-to-b from-green-dark/20 to-transparent rounded-lg border border-green-muted/30",children:[t.jsx(G,{className:"h-12 w-12 text-green-bright mx-auto mb-4"}),t.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Actionable Results"}),t.jsx("p",{className:"text-white/70",children:"Clear next steps to improve your security posture"})]})]}),t.jsxs(ie.div,{initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},variants:Rl,className:"bg-gradient-to-r from-orange-900/20 to-red-900/20 border border-orange-500/30 p-6 rounded-lg mb-8",children:[t.jsxs("div",{className:"grid md:grid-cols-3 gap-6 text-center",children:[t.jsxs("div",{children:[t.jsx("div",{className:"text-2xl md:text-3xl font-bold text-orange-200",children:"60%+"}),t.jsx("div",{className:"text-white/70",children:"of NZ businesses report annual phishing attempts"})]}),t.jsxs("div",{children:[t.jsx("div",{className:"text-2xl md:text-3xl font-bold text-orange-200",children:"$1.8M"}),t.jsx("div",{className:"text-white/70",children:"average cost of a successful cyber attack*"})]}),t.jsxs("div",{children:[t.jsx("div",{className:"text-2xl md:text-3xl font-bold text-orange-200",children:"Up to 75%"}),t.jsx("div",{className:"text-white/70",children:"reduction in email fraud with proper DMARC"})]})]}),t.jsx("p",{className:"text-white/50 text-xs text-center mt-4",children:"*Based on industry research and cybersecurity reports"})]}),t.jsxs(ie.div,{initial:"hidden",whileInView:"visible",viewport:{once:!0},variants:Rl,className:"text-center",children:[t.jsxs(s,{to:"/phishing-assessment",className:"group inline-flex items-center gap-3 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-400 hover:to-red-400 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl",children:[t.jsx(u,{className:"h-6 w-6"}),t.jsx("span",{children:"Start Your Free Security Assessment"}),t.jsx(_,{className:"h-6 w-6 transform transition-transform duration-300 group-hover:translate-x-1"})]}),t.jsx("p",{className:"text-white/60 mt-4 text-sm",children:"No email required • Instant results • Completely free"})]})]})]}),Ll=()=>{const[e,s]=r.useState(!1);r.useRef(!1),r.useEffect((()=>{const e=e=>{e.target instanceof HTMLElement&&document.body.contains(e.target)&&(e.preventDefault(),s(!0))};return window.addEventListener("contextmenu",e),()=>{window.removeEventListener("contextmenu",e)}}),[]);const[n,i]=r.useState(!1),a=r.useRef(null);return r.useEffect((()=>{const e=["ArrowUp","ArrowUp","ArrowDown","ArrowDown","ArrowLeft","ArrowRight","ArrowLeft","ArrowRight","b","a"],t=[],s=s=>{t.push(s.key),t.length>e.length&&t.shift(),t.join(",").toLowerCase()!==e.join(",").toLowerCase()||n||(i(!0),a.current&&clearTimeout(a.current),a.current=setTimeout((()=>{i(!1)}),1e4))};return window.addEventListener("keydown",s),()=>{window.removeEventListener("keydown",s),a.current&&clearTimeout(a.current)}}),[n]),t.jsxs("div",{className:"relative z-10",children:[t.jsx(Na,{}),t.jsx(rl,{title:"BlackVeil - Enterprise-grade Cybersecurity for SMBs Worldwide",description:"BlackVeil provides enterprise-grade cybersecurity protection for small and medium businesses worldwide without enterprise-level complexity. Featured on Firetail.ai Modern Cyber Podcast discussing email security innovations.",canonicalUrl:"https://blackveil.co.nz/",keywords:"cybersecurity, SMB, email security, DMARC, SPF, DKIM, cyber protection, email fraud, Firetail.ai, Modern Cyber Podcast, global security"}),t.jsx(jl,{}),t.jsx(nl,{items:[{name:"Home",url:"https://blackveil.co.nz/"}]}),t.jsx(Nl,{faqs:bl}),t.jsx(ce,{stuxnetCascadeActive:n}),t.jsx(Ol,{}),t.jsx(Tl,{}),t.jsx(Il,{}),t.jsx(wl,{}),t.jsx(ll,{}),t.jsx(ml,{}),t.jsx(yl,{}),t.jsx(vl,{}),t.jsx(ha,{open:e,onOpenChange:s,children:t.jsxs(fa,{role:"dialog","aria-modal":"true","aria-label":"API Key Easter Egg",className:"max-w-lg w-full bg-neutral-900 border border-green-700 rounded-lg shadow-2xl",children:[t.jsx(ga,{children:t.jsx(xa,{className:"text-green-400 flex items-center gap-2",children:"🗝️ API Key Unlocked!"})}),t.jsxs("div",{className:"mt-2 mb-4 text-center",children:[t.jsx("div",{className:"font-mono text-lg bg-neutral-800 text-green-400 px-4 py-2 rounded select-all border border-green-700 inline-block",children:"sk-live-V1B3-C0D3R-APIKEY-2025"}),t.jsxs("div",{className:"mt-4 text-green-300 font-semibold",children:["This key only works if you're coding at 2am",t.jsx("br",{}),"with lo-fi beats and good vibes."]})]})]})})]})},$l=k("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function Dl({className:e,variant:s,...r}){return t.jsx("div",{className:ae($l({variant:s}),e),...r})}const Ml=()=>{const[e,n]=r.useState([]),[i,a]=r.useState(!0),{user:o}=In();r.useEffect((()=>{o&&l()}),[o]);const l=async()=>{try{const{data:e,error:t}=await On.from("assessment_submissions").select("\n          id,\n          company_name,\n          industry,\n          employee_count,\n          status,\n          created_at,\n          completed_at,\n          lead_scores (\n            risk_level,\n            risk_percentage,\n            lead_priority\n          )\n        ").eq("user_id",o?.id).order("created_at",{ascending:!1});if(t)throw t;n(e||[])}catch(e){}finally{a(!1)}},c=e=>{switch(e){case"LOW":return"bg-green-500/20 text-green-400 border-green-500/30";case"MEDIUM":return"bg-yellow-500/20 text-yellow-400 border-yellow-500/30";case"HIGH":return"bg-red-500/20 text-red-400 border-red-500/30";default:return"bg-gray-500/20 text-gray-400 border-gray-500/30"}};return i?t.jsx(Sl,{className:"cyber-gradient-card border border-green-muted/30",children:t.jsx(Pl,{className:"p-8 text-center",children:t.jsx("div",{className:"text-white",children:"Loading your assessment history..."})})}):0===e.length?t.jsx(Sl,{className:"cyber-gradient-card border border-green-muted/30",children:t.jsxs(Pl,{className:"p-8 text-center",children:[t.jsx(u,{className:"h-12 w-12 text-green-bright mx-auto mb-4 opacity-60"}),t.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"No Assessments Yet"}),t.jsx("p",{className:"text-white/70 mb-6",children:"You haven't completed any security assessments yet. Take your first assessment to understand your security posture."}),t.jsx(s,{to:"/phishing-assessment",children:t.jsx(Mn,{className:"bg-green-bright hover:bg-green-muted text-black font-semibold",children:"Take Assessment"})})]})}):t.jsxs(Sl,{className:"cyber-gradient-card border border-green-muted/30",children:[t.jsx(El,{children:t.jsxs(Al,{className:"text-white flex items-center gap-2",children:[t.jsx(E,{className:"h-5 w-5"}),"Your Assessment History (",e.length,")"]})}),t.jsx(Pl,{className:"space-y-4",children:e.map((e=>{const s=e.lead_scores?(e=>{switch(e){case"LOW":default:return u;case"MEDIUM":return g;case"HIGH":return J}})(e.lead_scores.risk_level):u;return t.jsxs("div",{className:"p-4 bg-black-soft/50 rounded-lg border border-green-muted/20 hover:border-green-muted/40 transition-colors",children:[t.jsxs("div",{className:"flex items-start justify-between mb-3",children:[t.jsxs("div",{children:[t.jsx("h4",{className:"text-white font-semibold",children:e.company_name}),t.jsxs("p",{className:"text-white/60 text-sm",children:[e.industry," • ",e.employee_count," employees"]})]}),e.lead_scores&&t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(s,{className:"h-4 w-4 "+("LOW"===e.lead_scores.risk_level?"text-green-400":"MEDIUM"===e.lead_scores.risk_level?"text-yellow-400":"text-red-400")}),t.jsxs(Dl,{className:c(e.lead_scores.risk_level),children:[e.lead_scores.risk_level," Risk"]})]})]}),t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex items-center gap-4 text-sm text-white/60",children:[t.jsxs("span",{children:["Created: ",new Date(e.created_at).toLocaleDateString()]}),e.completed_at&&t.jsxs("span",{children:["Completed: ",new Date(e.completed_at).toLocaleDateString()]}),e.lead_scores&&t.jsxs("span",{children:["Risk Score: ",e.lead_scores.risk_percentage,"%"]})]}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(Dl,{variant:"outline",className:"completed"===e.status?"border-green-500/50 text-green-400":"border-yellow-500/50 text-yellow-400",children:e.status}),"completed"===e.status&&t.jsxs(Mn,{size:"sm",variant:"outline",className:"border-green-muted/50 text-green-bright hover:bg-green-dark/20",children:[t.jsx(H,{className:"h-3 w-3 mr-1"}),"View Report"]})]})]})]},e.id)}))})]})},Ul=()=>{const{assessmentTypes:e,loading:n,error:i}=(()=>{const[e,t]=r.useState([]),[s,n]=r.useState(!0),[i,a]=r.useState(null);return r.useEffect((()=>{(async()=>{try{n(!0),a(null);const{data:e,error:s}=await On.from("assessment_types").select("*").eq("is_active",!0).order("order_index");if(s)throw s;t(e||[])}catch(e){const t=e instanceof Error?e.message:"An unknown error occurred";a(`Failed to load assessments: ${t}`)}finally{n(!1)}})()}),[]),{assessmentTypes:e,loading:s,error:i}})();if(n)return t.jsx("div",{className:"space-y-6",children:[1,2,3].map((e=>t.jsx(Sl,{className:"cyber-gradient-card border border-green-muted/30",children:t.jsx(Pl,{className:"p-6",children:t.jsxs("div",{className:"animate-pulse",children:[t.jsx("div",{className:"h-6 bg-green-muted/20 rounded w-3/4 mb-4"}),t.jsx("div",{className:"h-4 bg-green-muted/20 rounded w-full mb-2"}),t.jsx("div",{className:"h-4 bg-green-muted/20 rounded w-2/3"})]})})},e)))});if(i)return t.jsx(Sl,{className:"cyber-gradient-card border border-red-500/30",children:t.jsxs(Pl,{className:"p-6 text-center",children:[t.jsx(u,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),t.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"Assessment Library Unavailable"}),t.jsx("p",{className:"text-red-400",children:i})]})});const a=e=>{switch(e.toLowerCase()){case"email security":return"📧";case"overall security":return"🛡️";case"threat protection":return"🔒";case"infrastructure security":return"🏗️";case"compliance":return"📋";default:return"🔐"}},o=(e,t)=>({"phishing-risk":"Email Security Check","dmarc-compliance":"Email Protection Setup","cybersecurity-maturity":"Security Readiness Review","ai-security":"AI Security Assessment","ransomware-readiness":"Ransomware Protection Check","supply-chain-security":"Vendor Security Review","cloud-security":"Cloud Security Assessment","zero-trust":"Zero Trust Security Review","remote-work-security":"Remote Work Security Check","compliance-readiness":"Compliance Readiness Review"}[t]||e),l=(e,t)=>({"phishing-risk":"Protect your business from email attacks and scams that target your employees and customers.","dmarc-compliance":"Verify your email authentication setup to prevent scammers from impersonating your business.","cybersecurity-maturity":"Get a complete overview of your business security posture and readiness against cyber threats.","ai-security":"Assess how artificial intelligence tools and systems impact your business security.","ransomware-readiness":"Evaluate your protection against ransomware attacks that can shut down your business.","supply-chain-security":"Review the security risks from your vendors, suppliers, and business partners.","cloud-security":"Check the security of your cloud services and online business tools.","zero-trust":"Assess your network security approach for modern business environments.","remote-work-security":"Evaluate security for employees working from home or remote locations.","compliance-readiness":"Check your readiness for industry regulations and compliance requirements."}[t]||e),c=e=>{switch(e.toLowerCase()){case"email security":return"bg-blue-500/20 text-blue-300 border-blue-500/30";case"overall security":return"bg-green-500/20 text-green-300 border-green-500/30";case"threat protection":return"bg-red-500/20 text-red-300 border-red-500/30";case"infrastructure security":return"bg-purple-500/20 text-purple-300 border-purple-500/30";case"compliance":return"bg-orange-500/20 text-orange-300 border-orange-500/30";default:return"bg-gray-500/20 text-gray-300 border-gray-500/30"}},d=["phishing-risk","ai-security","ransomware-readiness","supply-chain-security","cloud-security","zero-trust"],h=["cybersecurity-maturity","dmarc-compliance","remote-work-security","compliance-readiness"],m=()=>e.filter((e=>d.includes(e.slug)&&!["phishing-risk","cybersecurity-maturity","dmarc-compliance"].includes(e.slug))),p=()=>e.filter((e=>h.includes(e.slug)&&!["phishing-risk","cybersecurity-maturity","dmarc-compliance"].includes(e.slug)));return t.jsxs("div",{className:"space-y-8",children:[t.jsxs("div",{children:[t.jsxs("div",{className:"flex items-center gap-2 mb-6",children:[t.jsx(Y,{className:"h-5 w-5 text-yellow-400"}),t.jsx("h2",{className:"text-2xl font-bold text-white",children:"Most Popular"})]}),t.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.filter((e=>["phishing-risk","cybersecurity-maturity","dmarc-compliance"].includes(e.slug))).map((e=>{return t.jsx(Sl,{className:"cyber-gradient-card border border-green-muted/30 hover:border-green-bright/50 transition-all duration-300 group",children:t.jsxs(Pl,{className:"p-6",children:[t.jsxs("div",{className:"flex items-start justify-between mb-4",children:[t.jsx("div",{className:"text-3xl",children:a(e.category)}),t.jsx(Dl,{className:`${c(e.category)} text-xs`,children:e.category})]}),t.jsx("h3",{className:"text-xl font-bold text-white mb-3 group-hover:text-green-bright transition-colors",children:o(e.title,e.slug)}),t.jsx("p",{className:"text-white/70 text-sm mb-4 line-clamp-3",children:l(e.description,e.slug)}),t.jsxs("div",{className:"flex items-center gap-4 text-white/60 text-sm mb-6",children:[t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(z,{className:"h-4 w-4"}),t.jsxs("span",{children:[e.estimated_time_minutes," min"]})]}),t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(y,{className:"h-4 w-4"}),t.jsx("span",{children:"Free"})]})]}),(r=e.slug,d.includes(r)?t.jsx(Mn,{asChild:!0,className:"w-full bg-green-bright hover:bg-green-muted text-black font-semibold group-hover:shadow-lg group-hover:shadow-green-bright/20 transition-all",children:t.jsxs(s,{to:`/assessment/${e.slug}`,children:["Start Assessment",t.jsx(_,{className:"w-4 h-4 ml-2"})]})}):t.jsxs(Mn,{disabled:!0,className:"w-full bg-gray-600 text-gray-300 cursor-not-allowed",children:["Coming Soon",t.jsx(z,{className:"w-4 h-4 ml-2"})]}))]})},e.id);var r}))})]}),m().length>0&&t.jsxs("div",{children:[t.jsx("h2",{className:"text-2xl font-bold text-white mb-6",children:"Available Now"}),t.jsx("div",{className:"grid md:grid-cols-2 gap-6",children:m().map((e=>t.jsx(Sl,{className:"cyber-gradient-card border border-green-muted/30 hover:border-green-bright/50 transition-all duration-300 group",children:t.jsx(Pl,{className:"p-6",children:t.jsxs("div",{className:"flex items-start gap-4",children:[t.jsx("div",{className:"text-2xl flex-shrink-0",children:a(e.category)}),t.jsxs("div",{className:"flex-1",children:[t.jsxs("div",{className:"flex items-start justify-between mb-2",children:[t.jsx("h3",{className:"text-lg font-bold text-white group-hover:text-green-bright transition-colors",children:o(e.title,e.slug)}),t.jsx(Dl,{className:`${c(e.category)} text-xs ml-2`,children:e.category})]}),t.jsx("p",{className:"text-white/70 text-sm mb-4",children:l(e.description,e.slug)}),t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx("div",{className:"flex items-center gap-4 text-white/60 text-sm",children:t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(z,{className:"h-4 w-4"}),t.jsxs("span",{children:[e.estimated_time_minutes," min"]})]})}),t.jsx(Mn,{asChild:!0,size:"sm",className:"bg-green-bright hover:bg-green-muted text-black font-semibold",children:t.jsxs(s,{to:`/assessment/${e.slug}`,children:["Start",t.jsx(_,{className:"w-4 h-4 ml-1"})]})})]})]})]})})},e.id)))})]}),p().length>0&&t.jsxs("div",{children:[t.jsx("h2",{className:"text-2xl font-bold text-white mb-6",children:"Coming Soon"}),t.jsx("div",{className:"grid md:grid-cols-2 gap-6",children:p().map((e=>t.jsx(Sl,{className:"cyber-gradient-card border border-gray-500/30 opacity-75",children:t.jsx(Pl,{className:"p-6",children:t.jsxs("div",{className:"flex items-start gap-4",children:[t.jsx("div",{className:"text-2xl flex-shrink-0 opacity-60",children:a(e.category)}),t.jsxs("div",{className:"flex-1",children:[t.jsxs("div",{className:"flex items-start justify-between mb-2",children:[t.jsx("h3",{className:"text-lg font-bold text-white/70",children:o(e.title,e.slug)}),t.jsx(Dl,{className:"bg-gray-500/20 text-gray-300 border-gray-500/30 text-xs ml-2",children:e.category})]}),t.jsx("p",{className:"text-white/50 text-sm mb-4",children:l(e.description,e.slug)}),t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx("div",{className:"flex items-center gap-4 text-white/40 text-sm",children:t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(z,{className:"h-4 w-4"}),t.jsxs("span",{children:[e.estimated_time_minutes," min"]})]})}),t.jsxs(Mn,{disabled:!0,size:"sm",className:"bg-gray-600 text-gray-400 cursor-not-allowed",children:["Coming Soon",t.jsx(z,{className:"w-4 h-4 ml-1"})]})]})]})]})})},e.id)))})]})]})},Bl=Be,zl=r.forwardRef((({className:e,...s},r)=>t.jsx(De,{ref:r,className:ae("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s})));zl.displayName=De.displayName;const Fl=r.forwardRef((({className:e,...s},r)=>t.jsx(Me,{ref:r,className:ae("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s})));Fl.displayName=Me.displayName;const ql=r.forwardRef((({className:e,...s},r)=>t.jsx(Ue,{ref:r,className:ae("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s})));ql.displayName=Ue.displayName;const Vl=({onDismiss:e})=>t.jsx(Sl,{className:"mb-6 border-blue-500/30 bg-blue-500/10",children:t.jsxs(Pl,{className:"p-6",children:[t.jsxs("div",{className:"flex items-start justify-between mb-4",children:[t.jsx("h3",{className:"text-xl font-bold text-white",children:"Welcome to Your Security Dashboard"}),t.jsx(Mn,{variant:"ghost",size:"sm",onClick:e,className:"text-white/70 hover:text-white",children:"×"})]}),t.jsx("p",{className:"text-white/80 mb-6",children:"Get started with these simple steps to understand and improve your business security:"}),t.jsxs("div",{className:"grid md:grid-cols-3 gap-4",children:[t.jsxs("div",{className:"text-center p-4 bg-white/5 rounded-lg",children:[t.jsx(u,{className:"h-8 w-8 text-blue-400 mx-auto mb-2"}),t.jsx("h4",{className:"font-semibold text-white mb-1",children:"1. Check Your Security"}),t.jsx("p",{className:"text-sm text-white/70",children:"Take a quick 5-minute health check"})]}),t.jsxs("div",{className:"text-center p-4 bg-white/5 rounded-lg",children:[t.jsx(p,{className:"h-8 w-8 text-blue-400 mx-auto mb-2"}),t.jsx("h4",{className:"font-semibold text-white mb-1",children:"2. Review Your Results"}),t.jsx("p",{className:"text-sm text-white/70",children:"Understand your security risks"})]}),t.jsxs("div",{className:"text-center p-4 bg-white/5 rounded-lg",children:[t.jsx(X,{className:"h-8 w-8 text-blue-400 mx-auto mb-2"}),t.jsx("h4",{className:"font-semibold text-white mb-1",children:"3. Take Action"}),t.jsx("p",{className:"text-sm text-white/70",children:"Follow our recommendations"})]})]}),t.jsx("div",{className:"mt-6 text-center",children:t.jsx(s,{to:"/assessment/phishing-risk",children:t.jsxs(Mn,{className:"bg-blue-500 hover:bg-blue-600 text-white",children:["Start Your First Security Check",t.jsx(_,{className:"w-4 h-4 ml-2"})]})})})]})}),Hl=()=>{const{user:e}=In(),[n,i]=r.useState(!1);r.useEffect((()=>{e&&(localStorage.getItem("hasSeenWelcome")||i(!0))}),[e]);return t.jsxs("div",{className:"min-h-screen bg-black",children:[t.jsx(rl,{title:"Security Dashboard | Your Assessment History | BlackVeil",description:"View your security assessment history and track your cybersecurity progress with BlackVeil.",canonicalUrl:"https://blackveil.co.nz/dashboard"}),t.jsx(le,{className:"pt-16 pb-8",children:t.jsxs("div",{className:"max-w-6xl mx-auto",children:[n&&t.jsx(Vl,{onDismiss:()=>{i(!1),localStorage.setItem("hasSeenWelcome","true")}}),t.jsxs("div",{className:"mb-8",children:[t.jsxs("h1",{className:"text-3xl font-bold mb-4 text-white flex items-center gap-3",children:[t.jsx(u,{className:"h-8 w-8 text-green-bright"}),"Security Dashboard"]}),t.jsxs("p",{className:"text-white/70",children:["Welcome back, ",e?.email?.split("@")[0],". Here's your business security overview."]})]}),t.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6 lg:gap-8",children:[t.jsxs("div",{className:"lg:col-span-1 space-y-4 lg:space-y-6",children:[t.jsxs(Sl,{className:"cyber-gradient-card border border-green-muted/30",children:[t.jsxs(El,{children:[t.jsxs(Al,{className:"text-white text-lg flex items-center gap-2",children:[t.jsx(d,{className:"h-5 w-5 text-green-bright"}),"Quick Security Checks"]}),t.jsx("p",{className:"text-white/70 text-sm",children:"Fast assessments to check your business security"})]}),t.jsxs(Pl,{className:"space-y-3",children:[t.jsx(s,{to:"/assessment/phishing-risk",children:t.jsxs(Mn,{className:"w-full bg-green-bright hover:bg-green-muted text-black font-semibold h-auto p-4 justify-between",children:[t.jsxs("div",{className:"text-left",children:[t.jsx("div",{className:"font-semibold",children:"Email Security Check"}),t.jsx("div",{className:"text-sm opacity-80",children:"Protect against email attacks"})]}),t.jsxs("div",{className:"text-right",children:[t.jsx("div",{className:"text-xs opacity-80",children:"5 min"}),t.jsx(_,{className:"h-4 w-4 mt-1"})]})]})}),t.jsx(s,{to:"/assessment/cybersecurity-maturity",children:t.jsxs(Mn,{variant:"outline",className:"w-full border-green-muted/50 text-green-bright hover:bg-green-dark/20 h-auto p-4 justify-between",children:[t.jsxs("div",{className:"text-left",children:[t.jsx("div",{className:"font-semibold",children:"Security Readiness Review"}),t.jsx("div",{className:"text-sm opacity-80",children:"Complete security assessment"})]}),t.jsxs("div",{className:"text-right",children:[t.jsx("div",{className:"text-xs opacity-80",children:"10 min"}),t.jsx(_,{className:"h-4 w-4 mt-1"})]})]})}),t.jsx(s,{to:"/assessment/dmarc-compliance",children:t.jsxs(Mn,{variant:"outline",className:"w-full border-green-muted/50 text-green-bright hover:bg-green-dark/20 h-auto p-4 justify-between",children:[t.jsxs("div",{className:"text-left",children:[t.jsx("div",{className:"font-semibold",children:"Email Protection Setup"}),t.jsx("div",{className:"text-sm opacity-80",children:"Verify email authentication"})]}),t.jsxs("div",{className:"text-right",children:[t.jsx("div",{className:"text-xs opacity-80",children:"7 min"}),t.jsx(_,{className:"h-4 w-4 mt-1"})]})]})})]})]}),t.jsxs(Sl,{className:"cyber-gradient-card border border-green-muted/30",children:[t.jsx(El,{children:t.jsx(Al,{className:"text-white text-lg",children:"Other Actions"})}),t.jsxs(Pl,{className:"space-y-4",children:[t.jsx(s,{to:"/services",children:t.jsxs(Mn,{variant:"outline",className:"w-full border-green-muted/50 text-green-bright hover:bg-green-dark/20",children:[t.jsx(Z,{className:"h-4 w-4 mr-2"}),"View Services"]})}),t.jsx("a",{href:"https://calendly.com/adam-blackveil/30min",target:"_blank",rel:"noopener noreferrer",children:t.jsxs(Mn,{variant:"outline",className:"w-full border-blue-400/50 text-blue-400 hover:bg-blue-500/10",children:[t.jsx(z,{className:"h-4 w-4 mr-2"}),"Schedule Consultation"]})})]})]}),t.jsx(Sl,{className:"cyber-gradient-card border border-green-muted/30",children:t.jsxs(Pl,{className:"p-6",children:[t.jsx("h3",{className:"text-white font-semibold mb-2",children:"Security Tip"}),t.jsx("p",{className:"text-white/70 text-sm",children:"Regular security assessments help identify vulnerabilities before attackers do. Consider taking an assessment every quarter to stay protected."})]})})]}),t.jsx("div",{className:"lg:col-span-3",children:t.jsxs(Bl,{defaultValue:"health-checks",className:"w-full",children:[t.jsxs(zl,{className:"grid w-full grid-cols-3 bg-gray-800/50 border border-green-muted/30 h-auto",children:[t.jsxs(Fl,{value:"health-checks",className:"text-white data-[state=active]:bg-green-bright data-[state=active]:text-black p-2 lg:p-3 text-xs lg:text-sm",children:[t.jsx(u,{className:"h-4 w-4 mr-1 lg:mr-2"}),t.jsx("span",{className:"hidden sm:inline",children:"Security Health Checks"}),t.jsx("span",{className:"sm:hidden",children:"Health Checks"})]}),t.jsxs(Fl,{value:"reports",className:"text-white data-[state=active]:bg-green-bright data-[state=active]:text-black p-2 lg:p-3 text-xs lg:text-sm",children:[t.jsx(p,{className:"h-4 w-4 mr-1 lg:mr-2"}),t.jsx("span",{className:"hidden sm:inline",children:"My Security Reports"}),t.jsx("span",{className:"sm:hidden",children:"Reports"})]}),t.jsxs(Fl,{value:"action-items",className:"text-white data-[state=active]:bg-green-bright data-[state=active]:text-black p-2 lg:p-3 text-xs lg:text-sm",children:[t.jsx(X,{className:"h-4 w-4 mr-1 lg:mr-2"}),t.jsx("span",{className:"hidden sm:inline",children:"Recommended Actions"}),t.jsx("span",{className:"sm:hidden",children:"Actions"})]})]}),t.jsx(ql,{value:"health-checks",className:"mt-6",children:t.jsxs(Sl,{className:"cyber-gradient-card border border-green-muted/30",children:[t.jsxs(El,{children:[t.jsxs(Al,{className:"text-white flex items-center gap-2",children:[t.jsx(l,{className:"h-5 w-5"}),"Available Security Checks"]}),t.jsx("p",{className:"text-white/70",children:"Choose from our library of security assessments designed for businesses like yours."})]}),t.jsx(Pl,{children:t.jsx(Ul,{})})]})}),t.jsx(ql,{value:"reports",className:"mt-6",children:t.jsxs(Sl,{className:"cyber-gradient-card border border-green-muted/30",children:[t.jsxs(El,{children:[t.jsxs(Al,{className:"text-white flex items-center gap-2",children:[t.jsx(p,{className:"h-5 w-5"}),"Your Security Reports"]}),t.jsx("p",{className:"text-white/70",children:"View and download your completed security assessments and recommendations."})]}),t.jsx(Pl,{children:t.jsx(Ml,{})})]})}),t.jsx(ql,{value:"action-items",className:"mt-6",children:t.jsxs(Sl,{className:"cyber-gradient-card border border-green-muted/30",children:[t.jsxs(El,{children:[t.jsxs(Al,{className:"text-white flex items-center gap-2",children:[t.jsx(X,{className:"h-5 w-5"}),"Recommended Actions"]}),t.jsx("p",{className:"text-white/70",children:"Priority security improvements based on your assessments."})]}),t.jsx(Pl,{children:t.jsxs("div",{className:"text-center py-12",children:[t.jsx(X,{className:"h-12 w-12 text-green-bright mx-auto mb-4 opacity-60"}),t.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"No Action Items Yet"}),t.jsx("p",{className:"text-white/70 mb-6",children:"Complete a security assessment to receive personalized recommendations for improving your business security."}),t.jsx(s,{to:"/assessment/phishing-risk",children:t.jsxs(Mn,{className:"bg-green-bright hover:bg-green-muted text-black font-semibold",children:[t.jsx(d,{className:"h-4 w-4 mr-2"}),"Start Your First Assessment"]})})]})})]})})]})})]})]})})]})},Wl=r.lazy((()=>at((()=>import("./AboutPage-mPJmZo0Y.js")),__vite__mapDeps([2,1,3,4])))),Kl=r.lazy((()=>at((()=>import("./ServicesPage-C-yqMHYG.js")),__vite__mapDeps([5,1,3,6,4,7,8])))),Gl=r.lazy((()=>at((()=>import("./ContactPage-DgJZBVvg.js")),__vite__mapDeps([9,1,3,4,10,11,12,13,14])))),Jl=r.lazy((()=>at((()=>import("./PortfolioPage-LVmrSn5C.js")),__vite__mapDeps([15,1,3,11,6,4,16])))),Yl=r.lazy((()=>at((()=>import("./ProductPage-DOGKd3WR.js")),__vite__mapDeps([17,1,3,16,7,4])))),Zl=r.lazy((()=>at((()=>import("./BlogPage-BJI_7Ip3.js")),__vite__mapDeps([18,1,3,19,20,12,11,4])))),Xl=r.lazy((()=>at((()=>import("./BlogPostPage-bgzhMLHW.js")),__vite__mapDeps([21,1,3,19,20,4,22])))),Ql=r.lazy((()=>at((()=>import("./PrivacyPolicyPage-CG9L2_ze.js")),__vite__mapDeps([23,1,24,3,4])))),ec=r.lazy((()=>at((()=>import("./TermsOfServicePage-B8AHJvR8.js")),__vite__mapDeps([25,1,24,3,4])))),tc=r.lazy((()=>at((()=>import("./CookiePolicyPage-7fTgCI27.js")),__vite__mapDeps([26,1,24,3,4])))),sc=r.lazy((()=>at((()=>import("./PhishingSimulationPage-D79IpelA.js")),__vite__mapDeps([27,1,3,8,4])))),rc=r.lazy((()=>at((()=>import("./NotFound-2MhZjmJl.js")),__vite__mapDeps([28,1,20,3,4]))));r.lazy((()=>at((()=>import("./PhishingAssessmentPage-CBwJ6FZe.js")),__vite__mapDeps([29,1,3,30,4,12,11,10]))));const nc=r.lazy((()=>at((()=>import("./AuthPage-DpESnke1.js")),__vite__mapDeps([31,1,3,12,11,10,4])))),ic=r.lazy((()=>at((()=>import("./AdminDashboardPage-BokzDjxX.js")),__vite__mapDeps([32,1,3,12,11,4,10,13])))),ac=r.lazy((()=>at((()=>import("./AssessmentLibraryPage-COEOtXJh.js")),__vite__mapDeps([33,1,3,4])))),oc=r.lazy((()=>at((()=>import("./DynamicAssessmentPage-Cf3KY6Ef.js")),__vite__mapDeps([34,1,3,12,11,10,4,30])))),lc=()=>t.jsxs(Q,{children:[t.jsx(ee,{path:"/",element:t.jsx(Ll,{})}),t.jsx(ee,{path:"/auth",element:t.jsx(r.Suspense,{fallback:t.jsx(Ca,{}),children:t.jsx(nc,{})})}),t.jsx(ee,{path:"/admin",element:t.jsx(r.Suspense,{fallback:t.jsx(Ca,{}),children:t.jsx(Pa,{requireAdmin:!0,children:t.jsx(ic,{})})})}),t.jsx(ee,{path:"/about",element:t.jsx(r.Suspense,{fallback:t.jsx(Ca,{}),children:t.jsx(Wl,{})})}),t.jsx(ee,{path:"/blog",element:t.jsx(r.Suspense,{fallback:t.jsx(Ca,{}),children:t.jsx(Zl,{})})}),t.jsx(ee,{path:"/blog/:id",element:t.jsx(r.Suspense,{fallback:t.jsx(Ca,{}),children:t.jsx(Xl,{})})}),t.jsx(ee,{path:"/resources",element:t.jsx(ol,{})}),t.jsx(ee,{path:"/products/blackvault-lite",element:t.jsx(r.Suspense,{fallback:t.jsx(Ca,{}),children:t.jsx(Yl,{})})}),t.jsx(ee,{path:"/services",element:t.jsx(r.Suspense,{fallback:t.jsx(Ca,{}),children:t.jsx(Kl,{})})}),t.jsx(ee,{path:"/services/phishing-simulation",element:t.jsx(r.Suspense,{fallback:t.jsx(Ca,{}),children:t.jsx(sc,{})})}),t.jsx(ee,{path:"/home",element:t.jsx(D,{to:"/",replace:!0})}),t.jsx(ee,{path:"/portfolio",element:t.jsx(r.Suspense,{fallback:t.jsx(Ca,{}),children:t.jsx(Jl,{})})}),t.jsx(ee,{path:"/contact",element:t.jsx(r.Suspense,{fallback:t.jsx(Ca,{}),children:t.jsx(Gl,{})})}),t.jsx(ee,{path:"/privacy-policy",element:t.jsx(r.Suspense,{fallback:t.jsx(Ca,{}),children:t.jsx(Ql,{})})}),t.jsx(ee,{path:"/terms",element:t.jsx(r.Suspense,{fallback:t.jsx(Ca,{}),children:t.jsx(ec,{})})}),t.jsx(ee,{path:"/cookies",element:t.jsx(r.Suspense,{fallback:t.jsx(Ca,{}),children:t.jsx(tc,{})})}),t.jsx(ee,{path:"/assessments",element:t.jsx(r.Suspense,{fallback:t.jsx(Ca,{}),children:t.jsx(ac,{})})}),t.jsx(ee,{path:"/assessment/:slug",element:t.jsx(r.Suspense,{fallback:t.jsx(Ca,{}),children:t.jsx(oc,{})})}),t.jsx(ee,{path:"/assessment",element:t.jsx(D,{to:"/assessments",replace:!0})}),t.jsx(ee,{path:"/phishing-assessment",element:t.jsx(D,{to:"/assessment/phishing-risk",replace:!0})}),t.jsx(ee,{path:"/dashboard",element:t.jsx(Pa,{children:t.jsx(Hl,{})})}),t.jsx(ee,{path:"*",element:t.jsx(r.Suspense,{fallback:t.jsx(Ca,{}),children:t.jsx(rc,{})})})]}),cc=()=>{const e=x();r.useEffect((()=>{!function(){if("undefined"!=typeof performance&&performance.getEntriesByType&&"function"==typeof PerformanceObserver)try{performance.getEntriesByType("paint").find((e=>"first-contentful-paint"===e.name)),"undefined"!=typeof PerformanceObserver&&new PerformanceObserver((e=>{const t=e.getEntries();t[t.length-1]})).observe({type:"largest-contentful-paint",buffered:!0})}catch(e){}}();const e=document.getElementById("main-content");e&&e.focus(),(()=>{const e=document.createElement("link");e.rel="prefetch",e.as="script",e.href="/src/pages/ResourcesPage.tsx",document.head.appendChild(e)})();const t=document.title,s=document.getElementById("page-announcer");s&&(s.textContent=`Navigated to ${t}`)}),[e.pathname])},dc=()=>(cc(),t.jsx(Aa,{children:t.jsxs(Ea,{children:[t.jsx("div",{id:"page-announcer",className:"sr-only","aria-live":"polite","aria-atomic":"true"}),t.jsx(lc,{})]})}));function uc(){return t.jsx(ka,{children:t.jsx(dc,{})})}var hc=(e=>(e.LCP="LCP",e.FID="FID",e.CLS="CLS",e.FCP="FCP",e.TTFB="TTFB",e.Navigation="Navigation",e.Resource="Resource",e.Custom="Custom",e))(hc||{});let mc=[];const pc=e=>{const t={...e,timestamp:Date.now()};if(mc.push(t),mc.length>100&&(mc=mc.slice(-100)),"undefined"!=typeof window)try{fc()}catch(s){}},fc=()=>{if(0!==mc.length)try{localStorage.setItem("performanceMetrics",JSON.stringify(mc))}catch(e){}},gc=()=>{try{const e=localStorage.getItem("performanceMetrics");e&&(mc=JSON.parse(e))}catch(e){}},xc=()=>{const e=(()=>{if("undefined"!=typeof window&&0===mc.length)try{gc()}catch(e){}return mc})();0!==e.length&&e.forEach((e=>{"good"===e.rating||e.rating}))},bc=()=>{if("undefined"!=typeof window&&"PerformanceObserver"in window)try{(()=>{try{new PerformanceObserver((e=>{const t=e.getEntries(),s=t[t.length-1].startTime;pc({type:hc.LCP,value:s,name:"Largest Contentful Paint"})})).observe({type:"largest-contentful-paint",buffered:!0})}catch(e){}})(),(()=>{try{new PerformanceObserver((e=>{e.getEntries().forEach((e=>{const t=e,s=t.processingStart-t.startTime;pc({type:hc.FID,value:s,name:"First Input Delay"})}))})).observe({type:"first-input",buffered:!0})}catch(e){}})(),(()=>{try{let e=0,t=[];new PerformanceObserver((s=>{s.getEntries().forEach((s=>{const r=s;r.hadRecentInput||(e+=r.value,t.push(s))})),pc({type:hc.CLS,value:e,name:"Cumulative Layout Shift"})})).observe({type:"layout-shift",buffered:!0})}catch(e){}})(),(()=>{try{new PerformanceObserver((e=>{e.getEntries().forEach((e=>{pc({type:hc.FCP,value:e.startTime,name:"First Contentful Paint"})}))})).observe({type:"paint",buffered:!0})}catch(e){}})(),(()=>{try{window.addEventListener("load",(()=>{setTimeout((()=>{const e=performance.getEntriesByType("navigation")[0];if(e){const t=e.domContentLoadedEventEnd-e.startTime;pc({type:hc.Custom,value:t,name:"DOM Content Loaded"});const s=e.loadEventEnd-e.startTime;pc({type:hc.Custom,value:s,name:"Page Load Time"})}}),0)}))}catch(e){}})(),(e=>{try{window.addEventListener("beforeunload",(()=>{e()}))}catch(t){}})(xc),yc()}catch(e){}},yc=()=>{if("undefined"!=typeof window)try{window.addEventListener("load",(()=>{setTimeout((()=>{const e=document.querySelectorAll("img");let t=0,s=0;if(e.forEach((e=>{const r=performance.getEntriesByName(e.src);if(r.length>0){const e=r[0];t+=e.responseEnd-e.startTime,s++}})),s>0){const e=t/s;pc({type:hc.Custom,value:e,name:"Average Image Load Time"})}}),1e3)}))}catch(e){}},vc=()=>{if("undefined"==typeof window)return!1;try{const e=window.plausible;return window.plausible=function(...t){if(e)return e.apply(this,t)},window.originalPlausible=e,!0}catch(e){return!1}},wc=()=>{"undefined"!=typeof window&&(vc(),(()=>{if("undefined"==typeof window)return!1;try{return window.dataLayer=window.dataLayer||[],!0}catch(e){return!1}})(),jc())},jc=e=>{const t="undefined"!=typeof window?window.location.pathname:"";(e=>{if("undefined"!=typeof window&&window.plausible)try{window.plausible("pageview",{props:{path:e}})}catch(t){}})(t),(e=>{if("undefined"!=typeof window&&window.dataLayer)try{window.dataLayer.push({event:"page_view",page_path:e})}catch(t){}})(t)},Nc=k("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),kc=r.forwardRef((({className:e,variant:s,...r},n)=>t.jsx("div",{ref:n,role:"alert",className:ae(Nc({variant:s}),e),...r})));kc.displayName="Alert";const _c=r.forwardRef((({className:e,...s},r)=>t.jsx("h5",{ref:r,className:ae("mb-1 font-medium leading-none tracking-tight",e),...s})));_c.displayName="AlertTitle";const Tc=r.forwardRef((({className:e,...s},r)=>t.jsx("div",{ref:r,className:ae("text-sm [&_p]:leading-relaxed",e),...s})));function Sc({title:e,message:s,instructions:r,technicalDetails:n}){const[o,l]=a.useState(null),c=async(e,t)=>{try{await navigator.clipboard.writeText(e),l(t),setTimeout((()=>l(null)),2e3)}catch(s){}},d="VITE_SUPABASE_URL=https://wikngnwwakatokbgvenw.supabase.co\nVITE_SUPABASE_ANON_KEY=your-supabase-anon-key-here",u="name: Deploy to GitHub Pages\non:\n  push:\n    branches: [ main ]\njobs:\n  build-and-deploy:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v3\n      - name: Setup Node.js\n        uses: actions/setup-node@v3\n        with:\n          node-version: '18'\n      - name: Install dependencies\n        run: npm ci\n      - name: Build\n        env:\n          VITE_SUPABASE_URL: ${{ secrets.VITE_SUPABASE_URL }}\n          VITE_SUPABASE_ANON_KEY: ${{ secrets.VITE_SUPABASE_ANON_KEY }}\n        run: npm run build\n      - name: Deploy\n        uses: peaceiris/actions-gh-pages@v3\n        with:\n          github_token: ${{ secrets.GITHUB_TOKEN }}\n          publish_dir: ./dist";return t.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4",children:t.jsxs("div",{className:"max-w-4xl w-full space-y-6",children:[t.jsxs("div",{className:"text-center space-y-4",children:[t.jsx("div",{className:"flex justify-center",children:t.jsx("div",{className:"p-4 bg-red-500/20 rounded-full",children:t.jsx(g,{className:"h-12 w-12 text-red-400"})})}),t.jsx("h1",{className:"text-3xl font-bold text-white",children:e}),t.jsx("p",{className:"text-lg text-slate-300 max-w-2xl mx-auto",children:s})]}),t.jsxs("div",{className:"grid md:grid-cols-2 gap-4",children:[t.jsxs(Sl,{className:"bg-slate-800/50 border-slate-700",children:[t.jsx(El,{children:t.jsxs(Al,{className:"text-white flex items-center gap-2",children:[t.jsx(te,{className:"h-5 w-5"}),"Environment Status"]})}),t.jsxs(Pl,{className:"space-y-3",children:[t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("span",{className:"text-slate-300",children:"Environment:"}),t.jsx(Dl,{variant:"outline",className:"text-white border-slate-600",children:n.environment})]}),t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("span",{className:"text-slate-300",children:"Deployment:"}),t.jsx(Dl,{variant:"outline",className:"text-white border-slate-600",children:n.deployment})]}),t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("span",{className:"text-slate-300",children:"Supabase URL:"}),t.jsx(Dl,{variant:n.hasSupabaseUrl?"default":"destructive",children:n.hasSupabaseUrl?"✓ Set":"✗ Missing"})]}),t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("span",{className:"text-slate-300",children:"Supabase Key:"}),t.jsx(Dl,{variant:n.hasSupabaseKey?"default":"destructive",children:n.hasSupabaseKey?"✓ Set":"✗ Missing"})]})]})]}),t.jsxs(Sl,{className:"bg-slate-800/50 border-slate-700",children:[t.jsx(El,{children:t.jsx(Al,{className:"text-white",children:"Quick Actions"})}),t.jsxs(Pl,{className:"space-y-3",children:[t.jsxs(Mn,{variant:"outline",className:"w-full justify-start text-left",onClick:()=>window.open("https://docs.lovable.dev/faq","_blank"),children:[t.jsx(i,{className:"h-4 w-4 mr-2"}),"Lovable.dev Documentation"]}),t.jsxs(Mn,{variant:"outline",className:"w-full justify-start text-left",onClick:()=>window.open("https://supabase.com/dashboard","_blank"),children:[t.jsx(i,{className:"h-4 w-4 mr-2"}),"Supabase Dashboard"]}),t.jsx(Mn,{variant:"outline",className:"w-full justify-start text-left",onClick:()=>window.location.reload(),children:"🔄 Reload Application"})]})]})]}),t.jsxs(Sl,{className:"bg-slate-800/50 border-slate-700",children:[t.jsxs(El,{children:[t.jsx(Al,{className:"text-white",children:"Configuration Instructions"}),t.jsx(Cl,{className:"text-slate-400",children:"Follow these steps to fix the deployment configuration"})]}),t.jsxs(Pl,{className:"space-y-4",children:[t.jsx(kc,{className:"bg-blue-500/10 border-blue-500/20",children:t.jsx(Tc,{className:"text-slate-300 whitespace-pre-line",children:r})}),t.jsxs("div",{className:"space-y-2",children:[t.jsx("h4",{className:"text-white font-medium",children:"Required Environment Variables:"}),t.jsxs("div",{className:"relative",children:[t.jsx("pre",{className:"bg-slate-900 p-4 rounded-lg text-sm text-slate-300 overflow-x-auto",children:d}),t.jsx(Mn,{size:"sm",variant:"outline",className:"absolute top-2 right-2",onClick:()=>c(d,"env"),children:"env"===o?t.jsx(se,{className:"h-4 w-4"}):t.jsx(re,{className:"h-4 w-4"})})]})]}),"GitHub Pages"===n.deployment&&t.jsxs("div",{className:"space-y-2",children:[t.jsx("h4",{className:"text-white font-medium",children:"GitHub Actions Workflow Example:"}),t.jsxs("div",{className:"relative",children:[t.jsx("pre",{className:"bg-slate-900 p-4 rounded-lg text-sm text-slate-300 overflow-x-auto max-h-64",children:u}),t.jsx(Mn,{size:"sm",variant:"outline",className:"absolute top-2 right-2",onClick:()=>c(u,"github"),children:"github"===o?t.jsx(se,{className:"h-4 w-4"}):t.jsx(re,{className:"h-4 w-4"})})]})]})]})]}),t.jsxs("div",{className:"text-center text-slate-400 text-sm",children:[t.jsx("p",{children:"BlackVeil Security Platform - Environment Configuration Required"}),t.jsxs("p",{className:"mt-1",children:["For support, contact"," ",t.jsx("a",{href:"mailto:<EMAIL>",className:"text-blue-400 hover:text-blue-300 underline",children:"<EMAIL>"})]})]})]})})}Tc.displayName="AlertDescription";const Ec="production",Ac={url:"https://wikngnwwakatokbgvenw.supabase.co",anonKey:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NjY0NTQsImV4cCI6MjA2NDE0MjQ1NH0.CCoK3cPN0Pe-AVUgePrxmWvf9qZho0b-prGDilfKmZ4"},Cc="undefined"!=typeof window&&window.location.hostname.includes("github.io"),Pc="undefined"!=typeof window&&("localhost"===window.location.hostname||"127.0.0.1"===window.location.hostname);(()=>{if("undefined"!=typeof document){const e=((e={})=>{const t={"X-Content-Type-Options":"nosniff","X-XSS-Protection":"1; mode=block"};return!1!==e.strictTransportSecurity&&(t["Strict-Transport-Security"]="max-age=31536000; includeSubDomains; preload"),!1!==e.xFrameOptions&&(t["X-Frame-Options"]="SAMEORIGIN"),!1!==e.referrerPolicy&&(t["Referrer-Policy"]="strict-origin-when-cross-origin"),!1!==e.contentSecurityPolicy&&(t["Content-Security-Policy"]=["default-src 'self'","font-src 'self' https://fonts.gstatic.com https://lovable.dev","img-src 'self' data: https: blob:","script-src 'self' https://cdn.gpteng.co https://plausible.io https://www.googletagmanager.com https://static.cloudflareinsights.com https://cloudflareinsights.com https://*.cloudflareinsights.com 'unsafe-inline' 'unsafe-eval'","style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://lovable.dev","connect-src 'self' https://api.blackveil.co.nz https://api.web3forms.com https://plausible.io https://www.google-analytics.com https://static.cloudflareinsights.com https://cloudflareinsights.com https://*.cloudflareinsights.com https://wikngnwwakatokbgvenw.supabase.co wss://*.lovableproject.com","frame-src https://www.googletagmanager.com","form-action 'self'","base-uri 'self'","upgrade-insecure-requests"].join("; ")),t["Cache-Control"]="no-cache, no-store, must-revalidate",t["Permissions-Policy"]="camera=(), microphone=(), geolocation=(), interest-cohort=()",t})();e["Content-Security-Policy"]&&(e["Content-Security-Policy"]=e["Content-Security-Policy"].split("; ").filter((e=>!e.startsWith("frame-ancestors"))).join("; "));const t=[{httpEquiv:"Content-Security-Policy",content:e["Content-Security-Policy"]},{httpEquiv:"X-Content-Type-Options",content:e["X-Content-Type-Options"]},{httpEquiv:"Referrer-Policy",content:e["Referrer-Policy"]}];return document.querySelectorAll("meta[http-equiv]").forEach((e=>{t.forEach((t=>{e.getAttribute("http-equiv")===t.httpEquiv&&e.remove()}))})),t.forEach((e=>{const t=document.createElement("meta");t.httpEquiv=e.httpEquiv,t.content=e.content,document.head.appendChild(t)})),!0}})(),(async()=>{wc();await(async()=>"undefined"!=typeof window&&new Promise((e=>{const t=()=>{const t=!(!window.__cfBeacon&&!window.Beacon),s=!!document.querySelector('script[src*="cloudflareinsights"]');t||s?e(!0):(window.location.hostname.includes("blackveil.co.nz"),e(!1))};t(),setTimeout(t,5e3)})))()})();class Oc extends a.Component{constructor(e){super(e),this.state={hasError:!1,error:null}}static getDerivedStateFromError(e){return e.message.includes("Missing Supabase environment variables")||e.message.includes("VITE_SUPABASE_URL")||e.message.includes("VITE_SUPABASE_ANON_KEY")?{hasError:!0,error:e}:null}componentDidCatch(e,t){}render(){if(this.state.hasError){const e={title:"Configuration Required",message:"The application needs to be configured with Supabase environment variables.",instructions:Cc?"\n🔧 GITHUB PAGES DEPLOYMENT CONFIGURATION\n\nTo fix this deployment, add environment variables as GitHub repository secrets:\n\n1. Go to your GitHub repository\n2. Navigate to Settings > Secrets and variables > Actions\n3. Add the following repository secrets:\n   - VITE_SUPABASE_URL: https://wikngnwwakatokbgvenw.supabase.co\n   - VITE_SUPABASE_ANON_KEY: [Your Supabase Anonymous Key]\n4. Update your GitHub Actions workflow to use these secrets during build\n5. Redeploy your application\n\nExample GitHub Actions workflow configuration:\n```yaml\nenv:\n  VITE_SUPABASE_URL: ${{ secrets.VITE_SUPABASE_URL }}\n  VITE_SUPABASE_ANON_KEY: ${{ secrets.VITE_SUPABASE_ANON_KEY }}\n```\n":"\n⚙️ DEPLOYMENT CONFIGURATION REQUIRED\n\nThis application requires Supabase environment variables to be configured in your deployment platform.\n\nRequired variables:\n- VITE_SUPABASE_URL: Your Supabase project URL\n- VITE_SUPABASE_ANON_KEY: Your Supabase anonymous key\n\nConfigure these variables in your deployment platform's environment settings.\n",technicalDetails:{environment:Ec,deployment:Cc?"GitHub Pages":Pc?"Local Development":"Unknown",hasSupabaseUrl:!!Ac.url,hasSupabaseKey:!!Ac.anonKey}};return t.jsx(Sc,{...e})}return this.props.children}}!function(){const e=document.getElementById("root");if(!e)return;const s=ze(e),r=t.jsx(Oc,{children:t.jsx(a.StrictMode,{children:t.jsx(ne,{children:t.jsx(Ln,{children:t.jsx(uc,{})})})})});s.render(r),setTimeout((()=>{bc()}),0)}();export{kc as A,nl as B,Sl as C,il as H,rl as P,ql as T,Fn as U,Pl as a,Mn as b,El as c,Al as d,Bl as e,zl as f,Fl as g,_c as h,Tc as i,ca as j,ml as k,oa as l,tl as m,In as n,Dl as o,Dn as p,La as q,Cl as r,On as s,aa as t,na as u,Ul as v};
