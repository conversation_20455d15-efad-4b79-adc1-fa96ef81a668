import{j as e,al as t,aj as n,r as a,m as i,am as r,C as o,s as l,O as s,L as c,an as m}from"./vendor-TvU2VNGP.js";import{S as d}from"./core-BZ2etCil.js";import{m as u,P as p,B as f,b as h}from"./index-gHFbPIvn.js";import{b as g,J as b}from"./index-RABeG8Z6.js";import"./ui-qGggI9tr.js";import"./post-8-C3aRwc2Q.js";const x=({headline:t,description:n,datePublished:a,dateModified:i,authorName:r,publisherName:o,publisherLogo:l,url:s,imageUrl:c})=>{const m={"@context":"https://schema.org","@type":"Article",headline:t,description:n,datePublished:a,dateModified:i||a,author:{"@type":"Person",name:r},publisher:{"@type":"Organization",name:o,logo:{"@type":"ImageObject",url:l}},mainEntityOfPage:{"@type":"WebPage","@id":s},...c&&{image:{"@type":"ImageObject",url:c,width:1200,height:630}}};return e.jsx(u,{children:e.jsx("script",{type:"application/ld+json",children:JSON.stringify(m)})})},y=({imageUrl:t,alt:n,caption:a,articleTitle:i,articleUrl:r,author:o="BlackVeil Security Team",datePublished:l,width:s=1200,height:c=630})=>{const m={"@context":"https://schema.org","@type":"ImageObject",url:t,name:n,description:a||`Featured image for ${i}`,contentUrl:t,width:s,height:c,author:{"@type":"Person",name:o},isPartOf:{"@type":"BlogPosting",name:i,url:r},copyrightNotice:"© BlackVeil Security Ltd. All rights reserved.",creditText:"BlackVeil Security",...l&&{datePublished:l}};return e.jsx("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(m)}})},{entries:T,setPrototypeOf:N,isFrozen:E,getPrototypeOf:A,getOwnPropertyDescriptor:S}=Object;let{freeze:_,seal:w,create:v}=Object,{apply:k,construct:R}="undefined"!=typeof Reflect&&Reflect;_||(_=function(e){return e}),w||(w=function(e){return e}),k||(k=function(e,t,n){return e.apply(t,n)}),R||(R=function(e,t){return new e(...t)});const O=Y(Array.prototype.forEach),L=Y(Array.prototype.lastIndexOf),I=Y(Array.prototype.pop),C=Y(Array.prototype.push),D=Y(Array.prototype.splice),M=Y(String.prototype.toLowerCase),j=Y(String.prototype.toString),P=Y(String.prototype.match),z=Y(String.prototype.replace),U=Y(String.prototype.indexOf),H=Y(String.prototype.trim),F=Y(Object.prototype.hasOwnProperty),B=Y(RegExp.prototype.test),W=(G=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return R(G,t)});var G;function Y(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,a=new Array(n>1?n-1:0),i=1;i<n;i++)a[i-1]=arguments[i];return k(e,t,a)}}function $(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:M;N&&N(e,null);let a=t.length;for(;a--;){let i=t[a];if("string"==typeof i){const e=n(i);e!==i&&(E(t)||(t[a]=e),i=e)}e[i]=!0}return e}function V(e){for(let t=0;t<e.length;t++){F(e,t)||(e[t]=null)}return e}function X(e){const t=v(null);for(const[n,a]of T(e)){F(e,n)&&(Array.isArray(a)?t[n]=V(a):a&&"object"==typeof a&&a.constructor===Object?t[n]=X(a):t[n]=a)}return t}function q(e,t){for(;null!==e;){const n=S(e,t);if(n){if(n.get)return Y(n.get);if("function"==typeof n.value)return Y(n.value)}e=A(e)}return function(){return null}}const K=_(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),J=_(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Z=_(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Q=_(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),ee=_(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),te=_(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),ne=_(["#text"]),ae=_(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),ie=_(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),re=_(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),oe=_(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),le=w(/\{\{[\w\W]*|[\w\W]*\}\}/gm),se=w(/<%[\w\W]*|[\w\W]*%>/gm),ce=w(/\$\{[\w\W]*/gm),me=w(/^data-[\-\w.\u00B7-\uFFFF]+$/),de=w(/^aria-[\-\w]+$/),ue=w(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),pe=w(/^(?:\w+script|data):/i),fe=w(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),he=w(/^html$/i),ge=w(/^[a-z][.\w]*(-[.\w]+)+$/i);var be=Object.freeze({__proto__:null,ARIA_ATTR:de,ATTR_WHITESPACE:fe,CUSTOM_ELEMENT:ge,DATA_ATTR:me,DOCTYPE_NAME:he,ERB_EXPR:se,IS_ALLOWED_URI:ue,IS_SCRIPT_OR_DATA:pe,MUSTACHE_EXPR:le,TMPLIT_EXPR:ce});const xe=1,ye=3,Te=7,Ne=8,Ee=9,Ae=function(){return"undefined"==typeof window?null:window};var Se=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Ae();const n=t=>e(t);if(n.version="3.2.5",n.removed=[],!t||!t.document||t.document.nodeType!==Ee||!t.Element)return n.isSupported=!1,n;let{document:a}=t;const i=a,r=i.currentScript,{DocumentFragment:o,HTMLTemplateElement:l,Node:s,Element:c,NodeFilter:m,NamedNodeMap:d=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:u,DOMParser:p,trustedTypes:f}=t,h=c.prototype,g=q(h,"cloneNode"),b=q(h,"remove"),x=q(h,"nextSibling"),y=q(h,"childNodes"),N=q(h,"parentNode");if("function"==typeof l){const e=a.createElement("template");e.content&&e.content.ownerDocument&&(a=e.content.ownerDocument)}let E,A="";const{implementation:S,createNodeIterator:w,createDocumentFragment:k,getElementsByTagName:R}=a,{importNode:G}=i;let Y={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};n.isSupported="function"==typeof T&&"function"==typeof N&&S&&void 0!==S.createHTMLDocument;const{MUSTACHE_EXPR:V,ERB_EXPR:le,TMPLIT_EXPR:se,DATA_ATTR:ce,ARIA_ATTR:me,IS_SCRIPT_OR_DATA:de,ATTR_WHITESPACE:pe,CUSTOM_ELEMENT:fe}=be;let{IS_ALLOWED_URI:ge}=be,Se=null;const _e=$({},[...K,...J,...Z,...ee,...ne]);let we=null;const ve=$({},[...ae,...ie,...re,...oe]);let ke=Object.seal(v(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Re=null,Oe=null,Le=!0,Ie=!0,Ce=!1,De=!0,Me=!1,je=!0,Pe=!1,ze=!1,Ue=!1,He=!1,Fe=!1,Be=!1,We=!0,Ge=!1,Ye=!0,$e=!1,Ve={},Xe=null;const qe=$({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Ke=null;const Je=$({},["audio","video","img","source","image","track"]);let Ze=null;const Qe=$({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),et="http://www.w3.org/1998/Math/MathML",tt="http://www.w3.org/2000/svg",nt="http://www.w3.org/1999/xhtml";let at=nt,it=!1,rt=null;const ot=$({},[et,tt,nt],j);let lt=$({},["mi","mo","mn","ms","mtext"]),st=$({},["annotation-xml"]);const ct=$({},["title","style","font","a","script"]);let mt=null;const dt=["application/xhtml+xml","text/html"];let ut=null,pt=null;const ft=a.createElement("form"),ht=function(e){return e instanceof RegExp||e instanceof Function},gt=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!pt||pt!==e){if(e&&"object"==typeof e||(e={}),e=X(e),mt=-1===dt.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,ut="application/xhtml+xml"===mt?j:M,Se=F(e,"ALLOWED_TAGS")?$({},e.ALLOWED_TAGS,ut):_e,we=F(e,"ALLOWED_ATTR")?$({},e.ALLOWED_ATTR,ut):ve,rt=F(e,"ALLOWED_NAMESPACES")?$({},e.ALLOWED_NAMESPACES,j):ot,Ze=F(e,"ADD_URI_SAFE_ATTR")?$(X(Qe),e.ADD_URI_SAFE_ATTR,ut):Qe,Ke=F(e,"ADD_DATA_URI_TAGS")?$(X(Je),e.ADD_DATA_URI_TAGS,ut):Je,Xe=F(e,"FORBID_CONTENTS")?$({},e.FORBID_CONTENTS,ut):qe,Re=F(e,"FORBID_TAGS")?$({},e.FORBID_TAGS,ut):{},Oe=F(e,"FORBID_ATTR")?$({},e.FORBID_ATTR,ut):{},Ve=!!F(e,"USE_PROFILES")&&e.USE_PROFILES,Le=!1!==e.ALLOW_ARIA_ATTR,Ie=!1!==e.ALLOW_DATA_ATTR,Ce=e.ALLOW_UNKNOWN_PROTOCOLS||!1,De=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,Me=e.SAFE_FOR_TEMPLATES||!1,je=!1!==e.SAFE_FOR_XML,Pe=e.WHOLE_DOCUMENT||!1,He=e.RETURN_DOM||!1,Fe=e.RETURN_DOM_FRAGMENT||!1,Be=e.RETURN_TRUSTED_TYPE||!1,Ue=e.FORCE_BODY||!1,We=!1!==e.SANITIZE_DOM,Ge=e.SANITIZE_NAMED_PROPS||!1,Ye=!1!==e.KEEP_CONTENT,$e=e.IN_PLACE||!1,ge=e.ALLOWED_URI_REGEXP||ue,at=e.NAMESPACE||nt,lt=e.MATHML_TEXT_INTEGRATION_POINTS||lt,st=e.HTML_INTEGRATION_POINTS||st,ke=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ht(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(ke.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ht(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(ke.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(ke.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Me&&(Ie=!1),Fe&&(He=!0),Ve&&(Se=$({},ne),we=[],!0===Ve.html&&($(Se,K),$(we,ae)),!0===Ve.svg&&($(Se,J),$(we,ie),$(we,oe)),!0===Ve.svgFilters&&($(Se,Z),$(we,ie),$(we,oe)),!0===Ve.mathMl&&($(Se,ee),$(we,re),$(we,oe))),e.ADD_TAGS&&(Se===_e&&(Se=X(Se)),$(Se,e.ADD_TAGS,ut)),e.ADD_ATTR&&(we===ve&&(we=X(we)),$(we,e.ADD_ATTR,ut)),e.ADD_URI_SAFE_ATTR&&$(Ze,e.ADD_URI_SAFE_ATTR,ut),e.FORBID_CONTENTS&&(Xe===qe&&(Xe=X(Xe)),$(Xe,e.FORBID_CONTENTS,ut)),Ye&&(Se["#text"]=!0),Pe&&$(Se,["html","head","body"]),Se.table&&($(Se,["tbody"]),delete Re.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw W('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw W('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');E=e.TRUSTED_TYPES_POLICY,A=E.createHTML("")}else void 0===E&&(E=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const a="data-tt-policy-suffix";t&&t.hasAttribute(a)&&(n=t.getAttribute(a));const i="dompurify"+(n?"#"+n:"");try{return e.createPolicy(i,{createHTML:e=>e,createScriptURL:e=>e})}catch(r){return null}}(f,r)),null!==E&&"string"==typeof A&&(A=E.createHTML(""));_&&_(e),pt=e}},bt=$({},[...J,...Z,...Q]),xt=$({},[...ee,...te]),yt=function(e){C(n.removed,{element:e});try{N(e).removeChild(e)}catch(t){b(e)}},Tt=function(e,t){try{C(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(a){C(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(He||Fe)try{yt(t)}catch(a){}else try{t.setAttribute(e,"")}catch(a){}},Nt=function(e){let t=null,n=null;if(Ue)e="<remove></remove>"+e;else{const t=P(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===mt&&at===nt&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const i=E?E.createHTML(e):e;if(at===nt)try{t=(new p).parseFromString(i,mt)}catch(o){}if(!t||!t.documentElement){t=S.createDocument(at,"template",null);try{t.documentElement.innerHTML=it?A:i}catch(o){}}const r=t.body||t.documentElement;return e&&n&&r.insertBefore(a.createTextNode(n),r.childNodes[0]||null),at===nt?R.call(t,Pe?"html":"body")[0]:Pe?t.documentElement:r},Et=function(e){return w.call(e.ownerDocument||e,e,m.SHOW_ELEMENT|m.SHOW_COMMENT|m.SHOW_TEXT|m.SHOW_PROCESSING_INSTRUCTION|m.SHOW_CDATA_SECTION,null)},At=function(e){return e instanceof u&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof d)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},St=function(e){return"function"==typeof s&&e instanceof s};function _t(e,t,a){O(e,(e=>{e.call(n,t,a,pt)}))}const wt=function(e){let t=null;if(_t(Y.beforeSanitizeElements,e,null),At(e))return yt(e),!0;const a=ut(e.nodeName);if(_t(Y.uponSanitizeElement,e,{tagName:a,allowedTags:Se}),e.hasChildNodes()&&!St(e.firstElementChild)&&B(/<[/\w!]/g,e.innerHTML)&&B(/<[/\w!]/g,e.textContent))return yt(e),!0;if(e.nodeType===Te)return yt(e),!0;if(je&&e.nodeType===Ne&&B(/<[/\w]/g,e.data))return yt(e),!0;if(!Se[a]||Re[a]){if(!Re[a]&&kt(a)){if(ke.tagNameCheck instanceof RegExp&&B(ke.tagNameCheck,a))return!1;if(ke.tagNameCheck instanceof Function&&ke.tagNameCheck(a))return!1}if(Ye&&!Xe[a]){const t=N(e)||e.parentNode,n=y(e)||e.childNodes;if(n&&t){for(let a=n.length-1;a>=0;--a){const i=g(n[a],!0);i.__removalCount=(e.__removalCount||0)+1,t.insertBefore(i,x(e))}}}return yt(e),!0}return e instanceof c&&!function(e){let t=N(e);t&&t.tagName||(t={namespaceURI:at,tagName:"template"});const n=M(e.tagName),a=M(t.tagName);return!!rt[e.namespaceURI]&&(e.namespaceURI===tt?t.namespaceURI===nt?"svg"===n:t.namespaceURI===et?"svg"===n&&("annotation-xml"===a||lt[a]):Boolean(bt[n]):e.namespaceURI===et?t.namespaceURI===nt?"math"===n:t.namespaceURI===tt?"math"===n&&st[a]:Boolean(xt[n]):e.namespaceURI===nt?!(t.namespaceURI===tt&&!st[a])&&!(t.namespaceURI===et&&!lt[a])&&!xt[n]&&(ct[n]||!bt[n]):!("application/xhtml+xml"!==mt||!rt[e.namespaceURI]))}(e)?(yt(e),!0):"noscript"!==a&&"noembed"!==a&&"noframes"!==a||!B(/<\/no(script|embed|frames)/i,e.innerHTML)?(Me&&e.nodeType===ye&&(t=e.textContent,O([V,le,se],(e=>{t=z(t,e," ")})),e.textContent!==t&&(C(n.removed,{element:e.cloneNode()}),e.textContent=t)),_t(Y.afterSanitizeElements,e,null),!1):(yt(e),!0)},vt=function(e,t,n){if(We&&("id"===t||"name"===t)&&(n in a||n in ft))return!1;if(Ie&&!Oe[t]&&B(ce,t));else if(Le&&B(me,t));else if(!we[t]||Oe[t]){if(!(kt(e)&&(ke.tagNameCheck instanceof RegExp&&B(ke.tagNameCheck,e)||ke.tagNameCheck instanceof Function&&ke.tagNameCheck(e))&&(ke.attributeNameCheck instanceof RegExp&&B(ke.attributeNameCheck,t)||ke.attributeNameCheck instanceof Function&&ke.attributeNameCheck(t))||"is"===t&&ke.allowCustomizedBuiltInElements&&(ke.tagNameCheck instanceof RegExp&&B(ke.tagNameCheck,n)||ke.tagNameCheck instanceof Function&&ke.tagNameCheck(n))))return!1}else if(Ze[t]);else if(B(ge,z(n,pe,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==U(n,"data:")||!Ke[e]){if(Ce&&!B(de,z(n,pe,"")));else if(n)return!1}else;return!0},kt=function(e){return"annotation-xml"!==e&&P(e,fe)},Rt=function(e){_t(Y.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||At(e))return;const a={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:we,forceKeepAttr:void 0};let i=t.length;for(;i--;){const o=t[i],{name:l,namespaceURI:s,value:c}=o,m=ut(l);let d="value"===l?c:H(c);if(a.attrName=m,a.attrValue=d,a.keepAttr=!0,a.forceKeepAttr=void 0,_t(Y.uponSanitizeAttribute,e,a),d=a.attrValue,!Ge||"id"!==m&&"name"!==m||(Tt(l,e),d="user-content-"+d),je&&B(/((--!?|])>)|<\/(style|title)/i,d)){Tt(l,e);continue}if(a.forceKeepAttr)continue;if(Tt(l,e),!a.keepAttr)continue;if(!De&&B(/\/>/i,d)){Tt(l,e);continue}Me&&O([V,le,se],(e=>{d=z(d,e," ")}));const u=ut(e.nodeName);if(vt(u,m,d)){if(E&&"object"==typeof f&&"function"==typeof f.getAttributeType)if(s);else switch(f.getAttributeType(u,m)){case"TrustedHTML":d=E.createHTML(d);break;case"TrustedScriptURL":d=E.createScriptURL(d)}try{s?e.setAttributeNS(s,l,d):e.setAttribute(l,d),At(e)?yt(e):I(n.removed)}catch(r){}}}_t(Y.afterSanitizeAttributes,e,null)},Ot=function e(t){let n=null;const a=Et(t);for(_t(Y.beforeSanitizeShadowDOM,t,null);n=a.nextNode();)_t(Y.uponSanitizeShadowNode,n,null),wt(n),Rt(n),n.content instanceof o&&e(n.content);_t(Y.afterSanitizeShadowDOM,t,null)};return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=null,r=null,l=null,c=null;if(it=!e,it&&(e="\x3c!--\x3e"),"string"!=typeof e&&!St(e)){if("function"!=typeof e.toString)throw W("toString is not a function");if("string"!=typeof(e=e.toString()))throw W("dirty is not a string, aborting")}if(!n.isSupported)return e;if(ze||gt(t),n.removed=[],"string"==typeof e&&($e=!1),$e){if(e.nodeName){const t=ut(e.nodeName);if(!Se[t]||Re[t])throw W("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof s)a=Nt("\x3c!----\x3e"),r=a.ownerDocument.importNode(e,!0),r.nodeType===xe&&"BODY"===r.nodeName||"HTML"===r.nodeName?a=r:a.appendChild(r);else{if(!He&&!Me&&!Pe&&-1===e.indexOf("<"))return E&&Be?E.createHTML(e):e;if(a=Nt(e),!a)return He?null:Be?A:""}a&&Ue&&yt(a.firstChild);const m=Et($e?e:a);for(;l=m.nextNode();)wt(l),Rt(l),l.content instanceof o&&Ot(l.content);if($e)return e;if(He){if(Fe)for(c=k.call(a.ownerDocument);a.firstChild;)c.appendChild(a.firstChild);else c=a;return(we.shadowroot||we.shadowrootmode)&&(c=G.call(i,c,!0)),c}let d=Pe?a.outerHTML:a.innerHTML;return Pe&&Se["!doctype"]&&a.ownerDocument&&a.ownerDocument.doctype&&a.ownerDocument.doctype.name&&B(he,a.ownerDocument.doctype.name)&&(d="<!DOCTYPE "+a.ownerDocument.doctype.name+">\n"+d),Me&&O([V,le,se],(e=>{d=z(d,e," ")})),E&&Be?E.createHTML(d):d},n.setConfig=function(){gt(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),ze=!0},n.clearConfig=function(){pt=null,ze=!1},n.isValidAttribute=function(e,t,n){pt||gt({});const a=ut(e),i=ut(t);return vt(a,i,n)},n.addHook=function(e,t){"function"==typeof t&&C(Y[e],t)},n.removeHook=function(e,t){if(void 0!==t){const n=L(Y[e],t);return-1===n?void 0:D(Y[e],n,1)[0]}return I(Y[e])},n.removeHooks=function(e){Y[e]=[]},n.removeAllHooks=function(){Y={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},n}();const _e=()=>{const{id:u}=t(),T=n(),N=parseInt(u||"0",10),E=g.find((e=>e.id===N)),A=E?.content?Se.sanitize(E.content):"";if(a.useEffect((()=>{if(isNaN(N))return b.error("Invalid blog post ID"),void T("/not-found",{replace:!0});E||(b.error(`Blog post #${N} not found`),T("/blog",{replace:!0}))}),[N,E,T]),!E)return e.jsx(d,{className:"min-h-[50vh] flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-16 h-16 border-4 border-t-green-bright border-r-green-muted/30 border-b-green-muted/30 border-l-green-muted/30 rounded-full animate-spin mx-auto mb-4"}),e.jsx("p",{className:"text-white/70",children:"Loading..."})]})});const S=[{name:"Home",url:"https://blackveil.co.nz/"},{name:"Blog",url:"https://blackveil.co.nz/blog"},{name:E.title,url:`https://blackveil.co.nz/blog/${E.id}`}],_=E.icon||i;return e.jsxs("article",{className:"min-h-screen transition-all duration-300",children:[e.jsx(p,{title:`${E.title} | BlackVeil Blog`,description:E.excerpt,canonicalUrl:`https://blackveil.co.nz/blog/${E.id}`,keywords:`${E.category}, cybersecurity, email security, BlackVeil, blog`,ogImage:E.image,imageAlt:E.imageAlt,imageWidth:E.imageWidth,imageHeight:E.imageHeight,ogType:"article"}),e.jsx(f,{items:S}),e.jsx(x,{headline:E.title,description:E.excerpt,datePublished:E.datePublished||E.date,dateModified:E.datePublished||E.date,authorName:E.author,publisherName:"BlackVeil",publisherLogo:"https://blackveil.co.nz/lovable-uploads/f5571c80-738e-4366-b61c-69a9d2ade2de.png",url:`https://blackveil.co.nz/blog/${E.id}`,imageUrl:E.image}),E.image&&e.jsx(y,{imageUrl:E.image,alt:E.imageAlt,articleTitle:E.title,articleUrl:`https://blackveil.co.nz/blog/${E.id}`,author:E.author,datePublished:E.datePublished||E.date,width:parseInt(E.imageWidth||"1200"),height:parseInt(E.imageHeight||"630")}),e.jsx(d,{className:"pt-14 xs:pt-16 sm:pt-20 md:pt-24 pb-4 xs:pb-6 sm:pb-8 transition-all duration-300",children:e.jsxs("div",{className:"max-w-4xl mx-auto px-3 xs:px-4 sm:px-6 lg:px-8 transition-all duration-300",children:[e.jsxs("div",{className:"mb-4 xs:mb-6 sm:mb-8 transition-all duration-300",children:[e.jsxs(h,{variant:"ghost",size:"sm",className:"text-white/70 hover:text-white mb-3 xs:mb-4 transition-all duration-300 h-10 px-3",onClick:()=>T("/blog"),"aria-label":"Back to blog",children:[e.jsx(r,{className:"h-4 w-4 mr-2","aria-hidden":"true"}),e.jsx("span",{className:"hidden xs:inline",children:"Back to Blog"}),e.jsx("span",{className:"xs:hidden",children:"Back"})]}),e.jsxs("header",{className:"mb-3 xs:mb-4 sm:mb-6 transition-all duration-300",children:[e.jsx("div",{className:"cyber-tag bg-green-dark/80 text-xs sm:text-sm mb-3 sm:mb-4 inline-block transition-all duration-300 py-1 px-2",children:E.category}),e.jsx("h1",{className:"text-xl xs:text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-3 sm:mb-4 leading-tight transition-all duration-300",children:E.title}),e.jsxs("div",{className:"flex flex-wrap items-center text-xs sm:text-sm text-white/70 gap-2 sm:gap-4 md:gap-6 transition-all duration-300",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(o,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 text-green-bright","aria-hidden":"true"}),e.jsx("time",{dateTime:E.datePublished||E.date,children:E.date})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(l,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 text-green-bright","aria-hidden":"true"}),e.jsx("span",{children:E.author})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(s,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 text-green-bright","aria-hidden":"true"}),e.jsx("span",{children:E.readTime})]})]})]})]}),e.jsx("figure",{className:"h-36 xs:h-44 sm:h-52 md:h-60 lg:h-72 relative overflow-hidden bg-black-muted mb-4 sm:mb-6 md:mb-8 rounded-lg transition-all duration-300",children:E.image?e.jsxs(e.Fragment,{children:[e.jsx("img",{src:E.image,alt:E.imageAlt,className:"absolute inset-0 w-full h-full object-cover",loading:"eager"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black via-black/60 to-transparent opacity-80"}),e.jsx(_,{className:"absolute bottom-4 right-4 text-green-bright h-8 w-8 xs:h-10 xs:w-10 sm:h-12 sm:w-12 opacity-90 z-10","aria-hidden":"true"})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black via-black/80 to-transparent opacity-70"}),e.jsx(_,{className:"text-green-bright h-16 w-16 xs:h-20 xs:w-20 sm:h-24 sm:w-24 md:h-28 md:w-28 lg:h-32 lg:w-32 opacity-80 animate-pulse-slow transition-all duration-300 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2","aria-hidden":"true"})]})}),e.jsx("div",{className:"cyber-card p-4 xs:p-5 sm:p-6 md:p-8 lg:p-10 bg-black-soft/80 mb-6 sm:mb-8",children:E.content?e.jsx("div",{dangerouslySetInnerHTML:{__html:A},className:"blog-content text-sm xs:text-base"}):e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-white/70",children:"No content available for this post."})})}),e.jsxs("footer",{className:"mt-4 xs:mt-6 sm:mt-8 md:mt-10 pt-3 xs:pt-4 sm:pt-6 border-t border-white/10 transition-all duration-300",children:[e.jsx("h3",{className:"text-base sm:text-lg md:text-xl font-bold mb-2 sm:mb-3 md:mb-4 transition-all duration-300",children:"Related Topics"}),e.jsx("nav",{className:"flex flex-wrap gap-1.5 sm:gap-2 transition-all duration-300","aria-label":"Related topics",children:E.tags&&E.tags.map(((t,n)=>e.jsxs(c,{to:`/blog?category=${encodeURIComponent(t)}`,className:"cyber-tag bg-black-soft hover:bg-green-dark/30 transition-colors text-xs py-1.5 px-2.5 transition-all duration-300",children:[e.jsx(m,{className:"h-3 w-3 mr-1","aria-hidden":"true"}),t]},n)))})]})]})})]})};export{_e as default};
