import{j as r}from"./vendor-TvU2VNGP.js";import{c as e}from"./core-BZ2etCil.js";const a=({variant:a="default",className:o,children:d,...t})=>r.jsx("div",{className:e("relative p-6 rounded-lg border border-green-muted/30 transition-all duration-300","hover:border-green-muted/70 hover:shadow-[0_0_15px_rgba(0,255,140,0.2)]",{"bg-black-soft/90 backdrop-blur-sm":"default"===a,"bg-gradient-to-br from-black-soft to-green-dark/10":"gradient"===a},o),...t,children:d});export{a as C};
