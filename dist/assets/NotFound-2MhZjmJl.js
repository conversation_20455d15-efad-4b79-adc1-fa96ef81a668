import{u as e,aj as s,r as t,j as a,m as l,am as r,L as n}from"./vendor-TvU2VNGP.js";import{b as i}from"./index-gHFbPIvn.js";import{g as o,f as c,e as m,d as x,c as d,b as h,a as f,p as b}from"./post-8-C3aRwc2Q.js";import"./core-BZ2etCil.js";import"./ui-qGggI9tr.js";const p=[o,c,m,x,d,h,f,b],g=()=>{const o=e(),c=s(),[m,x]=t.useState(null),[d,h]=t.useState(!1),f=(()=>{const e=o.pathname.replace(/\/+/g,"/").toLowerCase();return"/../../blackvault"===e||"/..%2f..%2fblackvault"===e||"/..%2F..%2Fblackvault"===e||"/..//..//blackvault"===e})();return t.useEffect((()=>{if(!f&&o.pathname.startsWith("/blog/")){const e=o.pathname.split("/blog/")[1];if(isNaN(parseInt(e))||!e)x({type:"blog list",path:"/blog"});else{const s=parseInt(e);p.some((e=>e.id===s))||x({type:"blog list",path:"/blog"})}}}),[o.pathname,f]),f?a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-b from-black via-zinc-900 to-black px-4 py-8",children:a.jsxs("div",{className:"cyber-card max-w-lg w-full text-center p-6 sm:p-10 relative overflow-hidden",children:[a.jsx("h1",{className:"text-2xl sm:text-3xl font-bold mb-2 text-green-bright animate-pulse",children:"You found the rabbit hole!"}),a.jsx("p",{className:"text-white/80 mb-6",children:"Welcome to the Blackvault. Explore the file tree below..."}),a.jsx(j,{showFlag:d,setShowFlag:h}),a.jsx("style",{children:"\n            .filetree-branch {\n              stroke: #4ade80;\n              stroke-width: 2;\n              stroke-dasharray: 6 4;\n              animation: dashmove 2s linear infinite;\n            }\n            @keyframes dashmove {\n              to { stroke-dashoffset: 20; }\n            }\n            .filetree-folder, .filetree-file {\n              transition: color 0.2s;\n              cursor: pointer;\n              user-select: none;\n            }\n            .filetree-file:hover {\n              color: #facc15;\n              text-shadow: 0 0 8px #facc15;\n            }\n            .flag-reveal {\n              background: #18181b;\n              border: 1px solid #4ade80;\n              color: #facc15;\n              font-family: monospace;\n              padding: 1rem 1.5rem;\n              border-radius: 0.5rem;\n              margin-top: 1.5rem;\n              word-break: break-all;\n              animation: fadein 0.7s;\n            }\n            @keyframes fadein {\n              from { opacity: 0; transform: translateY(20px);}\n              to { opacity: 1; transform: translateY(0);}\n            }\n          "})]})}):a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-black-soft px-4 py-8",children:a.jsxs("div",{className:"cyber-card max-w-md w-full text-center p-4 xs:p-6 sm:p-8",children:[a.jsx("div",{className:"flex justify-center mb-4 sm:mb-6",children:a.jsx(l,{className:"h-12 w-12 sm:h-16 sm:w-16 text-green-bright"})}),a.jsx("h1",{className:"text-3xl sm:text-4xl font-bold mb-2 sm:mb-4",children:"404"}),a.jsx("p",{className:"text-lg sm:text-xl text-white/70 mb-4 sm:mb-6",children:"Oops! Page not found"}),m&&a.jsxs("div",{className:"mb-6 text-sm sm:text-base text-white/70 p-3 border border-white/10 rounded bg-black-muted",children:[a.jsxs("p",{children:["It seems you're looking for a ",m.type," that doesn't exist."]}),a.jsxs(i,{variant:"link",className:"text-green-bright mt-1",onClick:()=>c(m.path),children:[a.jsx(r,{className:"h-4 w-4 mr-1"}),"View all available ","blog list"===m.type?"blog posts":"content"]})]}),a.jsx("p",{className:"text-sm sm:text-base text-white/60 mb-6 sm:mb-8",children:"The page you are looking for might have been removed, had its name changed, or is temporarily unavailable."}),a.jsxs("div",{className:"flex flex-col xs:flex-row gap-3 justify-center",children:[a.jsxs(i,{variant:"outline",onClick:()=>c(-1),className:"order-2 xs:order-1",children:[a.jsx(r,{className:"h-4 w-4 mr-2"}),"Go Back"]}),a.jsx(n,{to:"/",className:"cyber-button inline-block py-2 px-4 sm:py-3 sm:px-6 text-sm sm:text-base order-1 xs:order-2",children:"Return Home"})]})]})})},j=({showFlag:e,setShowFlag:s})=>a.jsxs("div",{className:"flex flex-col items-center mt-2",children:[a.jsxs("svg",{width:"220",height:"180",viewBox:"0 0 220 180",fill:"none",className:"mb-2",children:[a.jsx("path",{className:"filetree-branch",d:"M30 30 V60 H60 V90 H100"}),a.jsx("path",{className:"filetree-branch",d:"M60 60 V120 H140"}),a.jsx("path",{className:"filetree-branch",d:"M100 90 V150 H180"}),a.jsxs("g",{children:[a.jsx("text",{x:"20",y:"28",className:"filetree-folder",fill:"#4ade80",fontSize:"16",children:"/blackvault"}),a.jsx("text",{x:"60",y:"58",className:"filetree-folder",fill:"#4ade80",fontSize:"15",children:"secrets/"}),a.jsx("text",{x:"100",y:"88",className:"filetree-folder",fill:"#4ade80",fontSize:"15",children:"hidden/"}),a.jsx("text",{x:"140",y:"118",className:"filetree-folder",fill:"#4ade80",fontSize:"15",children:"deep/"})]}),a.jsx("g",{children:a.jsx("text",{x:"180",y:"148",className:"filetree-file",fill:"#facc15",fontSize:"15",style:{cursor:"pointer",fontWeight:600,textDecoration:"underline"},onClick:()=>s(!0),children:"flag.txt"})})]}),a.jsxs("div",{className:"text-xs text-white/60 mb-2",children:["Click ",a.jsx("span",{className:"text-yellow-400 font-mono",children:"flag.txt"})," to reveal the secret"]}),e&&a.jsx("div",{className:"flag-reveal",children:"VGhlIHJlYWwgdHJlYXN1cmUgaXMgY3VyaW9zaXR5LiB7cmFiYml0X2hvbGVfYmxhY2t2YXVsdH0="})]});export{g as default};
