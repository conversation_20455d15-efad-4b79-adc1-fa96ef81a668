import{r as e,j as a,x as t,f as s,ad as i,aj as r}from"./vendor-TvU2VNGP.js";import{S as n}from"./core-BZ2etCil.js";import{n as l,u as c,C as o,c as d,d as u,a as m,b as h,P as g}from"./index-gHFbPIvn.js";import{I as x}from"./input-wyDUQPna.js";import{L as v}from"./label-qGISAqow.js";import"./ui-qGggI9tr.js";import"./security-D6XyL6Yo.js";const p=({mode:r,onModeChange:n})=>{const[g,p]=e.useState(""),[j,b]=e.useState(""),[y,w]=e.useState(!1),{signUp:f,signIn:N}=l(),{toast:k}=c();return a.jsxs(o,{className:"cyber-gradient-card border border-green-muted/30 w-full max-w-md",children:[a.jsxs(d,{className:"text-center",children:[a.jsx(u,{className:"text-white text-2xl",children:"login"===r?"Sign In":"Create Account"}),a.jsx("p",{className:"text-white/60",children:"login"===r?"Welcome back to BlackVeil Security":"Join BlackVeil Security Platform"})]}),a.jsxs(m,{children:[a.jsxs("form",{onSubmit:async e=>{e.preventDefault(),w(!0);try{if(!g.trim())return k({title:"Validation Error",description:"Please enter your email address.",variant:"destructive"}),void w(!1);if(!j.trim())return k({title:"Validation Error",description:"Please enter your password.",variant:"destructive"}),void w(!1);if(j.length<6)return k({title:"Validation Error",description:"Password must be at least 6 characters long.",variant:"destructive"}),void w(!1);if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(g))return k({title:"Validation Error",description:"Please enter a valid email address.",variant:"destructive"}),void w(!1);const{error:e}="login"===r?await N(g,j):await f(g,j);if(e){let a=e.message||"An unknown error occurred";a.includes("Invalid login credentials")?a="Invalid email or password. Please check your credentials and try again.":a.includes("User already registered")?a="An account with this email already exists. Try signing in instead.":a.includes("Password should be at least")?a="Password must be at least 6 characters long.":a.includes("Unable to validate email")?a="Please enter a valid email address.":a.includes("fetch")||a.includes("Network")?a="Connection failed. Please check your internet connection and try again.":a.includes("authentication service")&&(a="Authentication service is currently unavailable. Please try again in a moment."),k({title:"Authentication Error",description:a,variant:"destructive"})}else k("signup"===r?{title:"Account created successfully!",description:"Please check your email to verify your account before signing in."}:{title:"Welcome back!",description:"You have successfully signed in."})}catch(a){k({title:"Unexpected Error",description:"Something went wrong. Please try again.",variant:"destructive"})}finally{w(!1)}},className:"space-y-4",children:[a.jsxs("div",{className:"space-y-2",children:[a.jsx(v,{htmlFor:"email",className:"text-white",children:"Email"}),a.jsxs("div",{className:"relative",children:[a.jsx(t,{className:"absolute left-3 top-3 h-4 w-4 text-white/60"}),a.jsx(x,{id:"email",type:"email",placeholder:"<EMAIL>",value:g,onChange:e=>p(e.target.value),required:!0,className:"pl-10 bg-black-soft border-green-muted/30 text-white",disabled:y})]})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(v,{htmlFor:"password",className:"text-white",children:"Password"}),a.jsxs("div",{className:"relative",children:[a.jsx(s,{className:"absolute left-3 top-3 h-4 w-4 text-white/60"}),a.jsx(x,{id:"password",type:"password",placeholder:"••••••••",value:j,onChange:e=>b(e.target.value),required:!0,minLength:6,className:"pl-10 bg-black-soft border-green-muted/30 text-white",disabled:y})]})]}),a.jsx(h,{type:"submit",disabled:y,className:"w-full bg-green-bright hover:bg-green-muted text-black font-semibold",children:y?a.jsxs(a.Fragment,{children:[a.jsx(i,{className:"mr-2 h-4 w-4 animate-spin"}),"login"===r?"Signing In...":"Creating Account..."]}):"login"===r?"Sign In":"Create Account"})]}),a.jsxs("div",{className:"mt-6 text-center",children:[a.jsx("p",{className:"text-white/60",children:"login"===r?"Don't have an account?":"Already have an account?"}),a.jsx(h,{variant:"link",onClick:()=>n("login"===r?"signup":"login"),className:"text-green-bright hover:text-green-muted p-0 h-auto",disabled:y,children:"login"===r?"Create one here":"Sign in instead"})]})]})]})},j=()=>{const[t,s]=e.useState("login"),{user:i,isLoading:c}=l(),o=r();return e.useEffect((()=>{!c&&i&&o("/")}),[i,c,o]),c?a.jsx("div",{className:"min-h-screen bg-black flex items-center justify-center",children:a.jsx("div",{className:"text-white",children:"Loading..."})}):a.jsxs("div",{className:"min-h-screen bg-black",children:[a.jsx(g,{title:("login"===t?"Sign In":"Create Account")+" | BlackVeil Security",description:"Access your BlackVeil Security account or create a new one to manage phishing assessments and security tools.",canonicalUrl:"https://blackveil.co.nz/auth"}),a.jsx(n,{className:"py-20",children:a.jsx("div",{className:"max-w-md mx-auto",children:a.jsx(p,{mode:t,onModeChange:s})})})]})};export{j as default};
