import{j as e,C as t,x as s,w as r,R as a,r as n,ac as i,ad as o,ae as d,E as u,F as l}from"./vendor-TvU2VNGP.js";import{c,S as m}from"./core-BZ2etCil.js";import{H as h,U as f,s as p,u as y,A as v,h as g,i as _,b as x,P as b,B as k}from"./index-gHFbPIvn.js";import{S as w}from"./ui-qGggI9tr.js";import{L as j}from"./label-qGISAqow.js";import{i as S,s as N}from"./security-D6XyL6Yo.js";import{I as C}from"./input-wyDUQPna.js";import{T as O}from"./textarea-LL_Wfanq.js";const T=()=>e.jsx(h,{title:"Contact Us",description:"Get in touch with our team to discuss your cybersecurity needs, arrange a consultation, or learn more about our services."}),V=()=>e.jsxs("div",{className:"cyber-card",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Get in Touch"}),e.jsx("p",{className:"text-white/70 mb-8",children:"Have questions about our cybersecurity services? Want to discuss how we can help protect your business? Our team is ready to provide the security expertise your organization needs."}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx(t,{className:"h-6 w-6 mr-4 text-green-muted"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-bold mb-1",children:"Schedule a Consultation"}),e.jsx("p",{className:"text-white/70 mb-3",children:"Book a 30-minute call with our security experts"}),e.jsx(f,{variant:"contact",layout:"horizontal",showDmarc:!1,showEmergency:!1})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(s,{className:"h-6 w-6 mr-4 text-green-muted"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-bold mb-1",children:"Connect With Us"}),e.jsxs("p",{className:"text-white/70",children:["Email us at ",e.jsx("a",{href:"mailto:<EMAIL>",className:"text-green-bright hover:text-green-bright/80 transition-colors",children:"<EMAIL>"})]})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx(r,{className:"h-6 w-6 mr-4 text-green-muted"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-bold mb-1",children:"Follow Us"}),e.jsx("a",{href:"https://www.linkedin.com/company/blackveil-limited",target:"_blank",rel:"noopener noreferrer",className:"text-green-bright hover:text-green-bright/80 transition-colors",children:"Follow BlackVeil on LinkedIn"})]})]}),e.jsxs("div",{className:"cyber-card-highlight mt-8 p-4",children:[e.jsx("h3",{className:"font-bold mb-2",children:"Remote Security Solutions"}),e.jsx("p",{className:"text-white/70",children:"BlackVeil provides enterprise-grade cybersecurity services remotely to businesses worldwide."})]})]})]});var A=e=>"checkbox"===e.type,E=e=>e instanceof Date,F=e=>null==e;const Z=e=>"object"==typeof e;var D=e=>!F(e)&&!Array.isArray(e)&&Z(e)&&!E(e),I=e=>D(e)&&e.target?A(e.target)?e.target.checked:e.target.value:e,P=(e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)),R="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function L(e){let t;const s=Array.isArray(e);if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else{if(R&&(e instanceof Blob||e instanceof FileList)||!s&&!D(e))return e;if(t=s?[]:{},s||(e=>{const t=e.constructor&&e.constructor.prototype;return D(t)&&t.hasOwnProperty("isPrototypeOf")})(e))for(const s in e)e.hasOwnProperty(s)&&(t[s]=L(e[s]));else t=e}return t}var $=e=>Array.isArray(e)?e.filter(Boolean):[],M=e=>void 0===e,U=(e,t,s)=>{if(!t||!D(e))return s;const r=$(t.split(/[,[\].]+?/)).reduce(((e,t)=>F(e)?e:e[t]),e);return M(r)||r===e?M(e[t])?s:e[t]:r},B=e=>"boolean"==typeof e,z=e=>/^\w*$/.test(e),W=e=>$(e.replace(/["|']|\]/g,"").split(/\.|\[/)),K=(e,t,s)=>{let r=-1;const a=z(t)?[t]:W(t),n=a.length,i=n-1;for(;++r<n;){const t=a[r];let n=s;if(r!==i){const s=e[t];n=D(s)||Array.isArray(s)?s:isNaN(+a[r+1])?{}:[]}if("__proto__"===t)return;e[t]=n,e=e[t]}return e};const q="blur",H="focusout",Y="change",G="onBlur",J="onChange",X="onSubmit",Q="onTouched",ee="all",te="max",se="min",re="maxLength",ae="minLength",ne="pattern",ie="required",oe="validate",de=a.createContext(null),ue=()=>a.useContext(de);var le=(e,t,s,r=!0)=>{const a={defaultValues:t._defaultValues};for(const n in e)Object.defineProperty(a,n,{get:()=>{const a=n;return t._proxyFormState[a]!==ee&&(t._proxyFormState[a]=!r||ee),s&&(s[a]=!0),e[a]}});return a},ce=e=>D(e)&&!Object.keys(e).length,me=(e,t,s,r)=>{s(e);const{name:a,...n}=e;return ce(n)||Object.keys(n).length>=Object.keys(t).length||Object.keys(n).find((e=>t[e]===(!r||ee)))},he=e=>Array.isArray(e)?e:[e],fe=(e,t,s)=>!e||!t||e===t||he(e).some((e=>e&&(s?e===t:e.startsWith(t)||t.startsWith(e))));function pe(e){const t=a.useRef(e);t.current=e,a.useEffect((()=>{const s=!e.disabled&&t.current.subject&&t.current.subject.subscribe({next:t.current.next});return()=>{s&&s.unsubscribe()}}),[e.disabled])}var ye=e=>"string"==typeof e,ve=(e,t,s,r,a)=>ye(e)?(r&&t.watch.add(e),U(s,e,a)):Array.isArray(e)?e.map((e=>(r&&t.watch.add(e),U(s,e)))):(r&&(t.watchAll=!0),s);function ge(e){const t=ue(),{name:s,disabled:r,control:n=t.control,shouldUnregister:i}=e,o=P(n._names.array,s),d=function(e){const t=ue(),{control:s=t.control,name:r,defaultValue:n,disabled:i,exact:o}=e||{},d=a.useRef(r);d.current=r,pe({disabled:i,subject:s._subjects.values,next:e=>{fe(d.current,e.name,o)&&l(L(ve(d.current,s._names,e.values||s._formValues,!1,n)))}});const[u,l]=a.useState(s._getWatch(r,n));return a.useEffect((()=>s._removeUnmounted())),u}({control:n,name:s,defaultValue:U(n._formValues,s,U(n._defaultValues,s,e.defaultValue)),exact:!0}),u=function(e){const t=ue(),{control:s=t.control,disabled:r,name:n,exact:i}=e||{},[o,d]=a.useState(s._formState),u=a.useRef(!0),l=a.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1}),c=a.useRef(n);return c.current=n,pe({disabled:r,next:e=>u.current&&fe(c.current,e.name,i)&&me(e,l.current,s._updateFormState)&&d({...s._formState,...e}),subject:s._subjects.state}),a.useEffect((()=>(u.current=!0,l.current.isValid&&s._updateValid(!0),()=>{u.current=!1})),[s]),le(o,s,l.current,!1)}({control:n,name:s,exact:!0}),l=a.useRef(n.register(s,{...e.rules,value:d,...B(e.disabled)?{disabled:e.disabled}:{}}));return a.useEffect((()=>{const e=n._options.shouldUnregister||i,t=(e,t)=>{const s=U(n._fields,e);s&&s._f&&(s._f.mount=t)};if(t(s,!0),e){const e=L(U(n._options.defaultValues,s));K(n._defaultValues,s,e),M(U(n._formValues,s))&&K(n._formValues,s,e)}return()=>{(o?e&&!n._state.action:e)?n.unregister(s):t(s,!1)}}),[s,n,o,i]),a.useEffect((()=>{U(n._fields,s)&&n._updateDisabledField({disabled:r,fields:n._fields,name:s,value:U(n._fields,s)._f.value})}),[r,s,n]),{field:{name:s,value:d,...B(r)||u.disabled?{disabled:u.disabled||r}:{},onChange:a.useCallback((e=>l.current.onChange({target:{value:I(e),name:s},type:Y})),[s]),onBlur:a.useCallback((()=>l.current.onBlur({target:{value:U(n._formValues,s),name:s},type:q})),[s,n]),ref:a.useCallback((e=>{const t=U(n._fields,s);t&&e&&(t._f.ref={focus:()=>e.focus(),select:()=>e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})}),[n._fields,s])},formState:u,fieldState:Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!U(u.errors,s)},isDirty:{enumerable:!0,get:()=>!!U(u.dirtyFields,s)},isTouched:{enumerable:!0,get:()=>!!U(u.touchedFields,s)},isValidating:{enumerable:!0,get:()=>!!U(u.validatingFields,s)},error:{enumerable:!0,get:()=>U(u.errors,s)}})}}const _e=e=>e.render(ge(e));var xe=(e,t,s,r,a)=>t?{...s[e],types:{...s[e]&&s[e].types?s[e].types:{},[r]:a||!0}}:{},be=e=>({isOnSubmit:!e||e===X,isOnBlur:e===G,isOnChange:e===J,isOnAll:e===ee,isOnTouch:e===Q}),ke=(e,t,s)=>!s&&(t.watchAll||t.watch.has(e)||[...t.watch].some((t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length)))));const we=(e,t,s,r)=>{for(const a of s||Object.keys(e)){const s=U(e,a);if(s){const{_f:e,...n}=s;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!r)return!0;if(e.ref&&t(e.ref,e.name)&&!r)return!0;if(we(n,t))break}else if(D(n)&&we(n,t))break}}};var je=(e,t,s)=>{const r=he(U(e,s));return K(r,"root",t[s]),K(e,s,r),e},Se=e=>"file"===e.type,Ne=e=>"function"==typeof e,Ce=e=>{if(!R)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},Oe=e=>ye(e),Te=e=>"radio"===e.type,Ve=e=>e instanceof RegExp;const Ae={value:!1,isValid:!1},Ee={value:!0,isValid:!0};var Fe=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter((e=>e&&e.checked&&!e.disabled)).map((e=>e.value));return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!M(e[0].attributes.value)?M(e[0].value)||""===e[0].value?Ee:{value:e[0].value,isValid:!0}:Ee:Ae}return Ae};const Ze={isValid:!1,value:null};var De=e=>Array.isArray(e)?e.reduce(((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e),Ze):Ze;function Ie(e,t,s="validate"){if(Oe(e)||Array.isArray(e)&&e.every(Oe)||B(e)&&!e)return{type:s,message:Oe(e)?e:"",ref:t}}var Pe=e=>D(e)&&!Ve(e)?e:{value:e,message:""},Re=async(e,t,s,r,a)=>{const{ref:n,refs:i,required:o,maxLength:d,minLength:u,min:l,max:c,pattern:m,validate:h,name:f,valueAsNumber:p,mount:y,disabled:v}=e._f,g=U(t,f);if(!y||v)return{};const _=i?i[0]:n,x=e=>{r&&_.reportValidity&&(_.setCustomValidity(B(e)?"":e||""),_.reportValidity())},b={},k=Te(n),w=A(n),j=k||w,S=(p||Se(n))&&M(n.value)&&M(g)||Ce(n)&&""===n.value||""===g||Array.isArray(g)&&!g.length,N=xe.bind(null,f,s,b),C=(e,t,s,r=re,a=ae)=>{const i=e?t:s;b[f]={type:e?r:a,message:i,ref:n,...N(e?r:a,i)}};if(a?!Array.isArray(g)||!g.length:o&&(!j&&(S||F(g))||B(g)&&!g||w&&!Fe(i).isValid||k&&!De(i).isValid)){const{value:e,message:t}=Oe(o)?{value:!!o,message:o}:Pe(o);if(e&&(b[f]={type:ie,message:t,ref:_,...N(ie,t)},!s))return x(t),b}if(!(S||F(l)&&F(c))){let e,t;const r=Pe(c),a=Pe(l);if(F(g)||isNaN(g)){const s=n.valueAsDate||new Date(g),i=e=>new Date((new Date).toDateString()+" "+e),o="time"==n.type,d="week"==n.type;ye(r.value)&&g&&(e=o?i(g)>i(r.value):d?g>r.value:s>new Date(r.value)),ye(a.value)&&g&&(t=o?i(g)<i(a.value):d?g<a.value:s<new Date(a.value))}else{const s=n.valueAsNumber||(g?+g:g);F(r.value)||(e=s>r.value),F(a.value)||(t=s<a.value)}if((e||t)&&(C(!!e,r.message,a.message,te,se),!s))return x(b[f].message),b}if((d||u)&&!S&&(ye(g)||a&&Array.isArray(g))){const e=Pe(d),t=Pe(u),r=!F(e.value)&&g.length>+e.value,a=!F(t.value)&&g.length<+t.value;if((r||a)&&(C(r,e.message,t.message),!s))return x(b[f].message),b}if(m&&!S&&ye(g)){const{value:e,message:t}=Pe(m);if(Ve(e)&&!g.match(e)&&(b[f]={type:ne,message:t,ref:n,...N(ne,t)},!s))return x(t),b}if(h)if(Ne(h)){const e=Ie(await h(g,t),_);if(e&&(b[f]={...e,...N(oe,e.message)},!s))return x(e.message),b}else if(D(h)){let e={};for(const r in h){if(!ce(e)&&!s)break;const a=Ie(await h[r](g,t),_,r);a&&(e={...a,...N(r,a.message)},x(a.message),s&&(b[f]=e))}if(!ce(e)&&(b[f]={ref:_,...e},!s))return b}return x(!0),b};function Le(e,t){const s=Array.isArray(t)?t:z(t)?[t]:W(t),r=1===s.length?e:function(e,t){const s=t.slice(0,-1).length;let r=0;for(;r<s;)e=M(e)?r++:e[t[r++]];return e}(e,s),a=s.length-1,n=s[a];return r&&delete r[n],0!==a&&(D(r)&&ce(r)||Array.isArray(r)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!M(e[t]))return!1;return!0}(r))&&Le(e,s.slice(0,-1)),e}var $e=()=>{let e=[];return{get observers(){return e},next:t=>{for(const s of e)s.next&&s.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter((e=>e!==t))}}),unsubscribe:()=>{e=[]}}},Me=e=>F(e)||!Z(e);function Ue(e,t){if(Me(e)||Me(t))return e===t;if(E(e)&&E(t))return e.getTime()===t.getTime();const s=Object.keys(e),r=Object.keys(t);if(s.length!==r.length)return!1;for(const a of s){const s=e[a];if(!r.includes(a))return!1;if("ref"!==a){const e=t[a];if(E(s)&&E(e)||D(s)&&D(e)||Array.isArray(s)&&Array.isArray(e)?!Ue(s,e):s!==e)return!1}}return!0}var Be=e=>"select-multiple"===e.type,ze=e=>Ce(e)&&e.isConnected,We=e=>{for(const t in e)if(Ne(e[t]))return!0;return!1};function Ke(e,t={}){const s=Array.isArray(e);if(D(e)||s)for(const r in e)Array.isArray(e[r])||D(e[r])&&!We(e[r])?(t[r]=Array.isArray(e[r])?[]:{},Ke(e[r],t[r])):F(e[r])||(t[r]=!0);return t}function qe(e,t,s){const r=Array.isArray(e);if(D(e)||r)for(const a in e)Array.isArray(e[a])||D(e[a])&&!We(e[a])?M(t)||Me(s[a])?s[a]=Array.isArray(e[a])?Ke(e[a],[]):{...Ke(e[a])}:qe(e[a],F(t)?{}:t[a],s[a]):s[a]=!Ue(e[a],t[a]);return s}var He=(e,t)=>qe(e,t,Ke(t)),Ye=(e,{valueAsNumber:t,valueAsDate:s,setValueAs:r})=>M(e)?e:t?""===e?NaN:e?+e:e:s&&ye(e)?new Date(e):r?r(e):e;function Ge(e){const t=e.ref;if(!(e.refs?e.refs.every((e=>e.disabled)):t.disabled))return Se(t)?t.files:Te(t)?De(e.refs).value:Be(t)?[...t.selectedOptions].map((({value:e})=>e)):A(t)?Fe(e.refs).value:Ye(M(t.value)?e.ref.value:t.value,e)}var Je=e=>M(e)?e:Ve(e)?e.source:D(e)?Ve(e.value)?e.value.source:e.value:e;const Xe="AsyncFunction";function Qe(e,t,s){const r=U(e,s);if(r||z(s))return{error:r,name:s};const a=s.split(".");for(;a.length;){const r=a.join("."),n=U(t,r),i=U(e,r);if(n&&!Array.isArray(n)&&s!==r)return{name:s};if(i&&i.type)return{name:r,error:i};a.pop()}return{name:s}}const et={mode:X,reValidateMode:J,shouldFocusError:!0};function tt(e={}){let t,s={...et,...e},r={submitCount:0,isDirty:!1,isLoading:Ne(s.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:s.errors||{},disabled:s.disabled||!1},a={},n=(D(s.defaultValues)||D(s.values))&&L(s.defaultValues||s.values)||{},i=s.shouldUnregister?{}:L(n),o={action:!1,mount:!1,watch:!1},d={mount:new Set,unMount:new Set,array:new Set,watch:new Set},u=0;const l={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},c={values:$e(),array:$e(),state:$e()},m=be(s.mode),h=be(s.reValidateMode),f=s.criteriaMode===ee,p=async t=>{if(!e.disabled&&(l.isValid||t)){const e=s.resolver?ce((await x()).errors):await b(a,!0);e!==r.isValid&&c.state.next({isValid:e})}},y=(t,s)=>{e.disabled||!l.isValidating&&!l.validatingFields||((t||Array.from(d.mount)).forEach((e=>{e&&(s?K(r.validatingFields,e,s):Le(r.validatingFields,e))})),c.state.next({validatingFields:r.validatingFields,isValidating:!ce(r.validatingFields)}))},v=(e,t,s,r)=>{const d=U(a,e);if(d){const a=U(i,e,M(s)?U(n,e):s);M(a)||r&&r.defaultChecked||t?K(i,e,t?a:Ge(d._f)):j(e,a),o.mount&&p()}},g=(t,s,i,o,d)=>{let u=!1,m=!1;const h={name:t};if(!e.disabled){const e=!!(U(a,t)&&U(a,t)._f&&U(a,t)._f.disabled);if(!i||o){l.isDirty&&(m=r.isDirty,r.isDirty=h.isDirty=k(),u=m!==h.isDirty);const a=e||Ue(U(n,t),s);m=!(e||!U(r.dirtyFields,t)),a||e?Le(r.dirtyFields,t):K(r.dirtyFields,t,!0),h.dirtyFields=r.dirtyFields,u=u||l.dirtyFields&&m!==!a}if(i){const e=U(r.touchedFields,t);e||(K(r.touchedFields,t,i),h.touchedFields=r.touchedFields,u=u||l.touchedFields&&e!==i)}u&&d&&c.state.next(h)}return u?h:{}},_=(s,a,n,i)=>{const o=U(r.errors,s),d=l.isValid&&B(a)&&r.isValid!==a;var m;if(e.delayError&&n?(m=()=>((e,t)=>{K(r.errors,e,t),c.state.next({errors:r.errors})})(s,n),t=e=>{clearTimeout(u),u=setTimeout(m,e)},t(e.delayError)):(clearTimeout(u),t=null,n?K(r.errors,s,n):Le(r.errors,s)),(n?!Ue(o,n):o)||!ce(i)||d){const e={...i,...d&&B(a)?{isValid:a}:{},errors:r.errors,name:s};r={...r,...e},c.state.next(e)}},x=async e=>{y(e,!0);const t=await s.resolver(i,s.context,((e,t,s,r)=>{const a={};for(const n of e){const e=U(t,n);e&&K(a,n,e._f)}return{criteriaMode:s,names:[...e],fields:a,shouldUseNativeValidation:r}})(e||d.mount,a,s.criteriaMode,s.shouldUseNativeValidation));return y(e),t},b=async(e,t,a={valid:!0})=>{for(const o in e){const u=e[o];if(u){const{_f:e,...c}=u;if(e){const c=d.array.has(e.name),m=u._f&&!((n=u._f)&&n.validate||!(Ne(n.validate)&&n.validate.constructor.name===Xe||D(n.validate)&&Object.values(n.validate).find((e=>e.constructor.name===Xe))));m&&l.validatingFields&&y([o],!0);const h=await Re(u,i,f,s.shouldUseNativeValidation&&!t,c);if(m&&l.validatingFields&&y([o]),h[e.name]&&(a.valid=!1,t))break;!t&&(U(h,e.name)?c?je(r.errors,h,e.name):K(r.errors,e.name,h[e.name]):Le(r.errors,e.name))}!ce(c)&&await b(c,t,a)}}var n;return a.valid},k=(t,s)=>!e.disabled&&(t&&s&&K(i,t,s),!Ue(V(),n)),w=(e,t,s)=>ve(e,d,{...o.mount?i:M(t)?n:ye(e)?{[e]:t}:t},s,t),j=(e,t,s={})=>{const r=U(a,e);let n=t;if(r){const s=r._f;s&&(!s.disabled&&K(i,e,Ye(t,s)),n=Ce(s.ref)&&F(t)?"":t,Be(s.ref)?[...s.ref.options].forEach((e=>e.selected=n.includes(e.value))):s.refs?A(s.ref)?s.refs.length>1?s.refs.forEach((e=>(!e.defaultChecked||!e.disabled)&&(e.checked=Array.isArray(n)?!!n.find((t=>t===e.value)):n===e.value))):s.refs[0]&&(s.refs[0].checked=!!n):s.refs.forEach((e=>e.checked=e.value===n)):Se(s.ref)?s.ref.value="":(s.ref.value=n,s.ref.type||c.values.next({name:e,values:{...i}})))}(s.shouldDirty||s.shouldTouch)&&g(e,n,s.shouldTouch,s.shouldDirty,!0),s.shouldValidate&&T(e)},S=(e,t,s)=>{for(const r in t){const n=t[r],i=`${e}.${r}`,o=U(a,i);(d.array.has(e)||D(n)||o&&!o._f)&&!E(n)?S(i,n,s):j(i,n,s)}},N=(e,t,s={})=>{const u=U(a,e),m=d.array.has(e),h=L(t);K(i,e,h),m?(c.array.next({name:e,values:{...i}}),(l.isDirty||l.dirtyFields)&&s.shouldDirty&&c.state.next({name:e,dirtyFields:He(n,i),isDirty:k(e,h)})):!u||u._f||F(h)?j(e,h,s):S(e,h,s),ke(e,d)&&c.state.next({...r}),c.values.next({name:o.mount?e:void 0,values:{...i}})},C=async n=>{o.mount=!0;const u=n.target;let v=u.name,k=!0;const w=U(a,v),j=e=>{k=Number.isNaN(e)||E(e)&&isNaN(e.getTime())||Ue(e,U(i,v,e))};if(w){let o,N;const C=u.type?Ge(w._f):I(n),O=n.type===q||n.type===H,V=!((S=w._f).mount&&(S.required||S.min||S.max||S.maxLength||S.minLength||S.pattern||S.validate)||s.resolver||U(r.errors,v)||w._f.deps)||((e,t,s,r,a)=>!a.isOnAll&&(!s&&a.isOnTouch?!(t||e):(s?r.isOnBlur:a.isOnBlur)?!e:!(s?r.isOnChange:a.isOnChange)||e))(O,U(r.touchedFields,v),r.isSubmitted,h,m),A=ke(v,d,O);K(i,v,C),O?(w._f.onBlur&&w._f.onBlur(n),t&&t(0)):w._f.onChange&&w._f.onChange(n);const E=g(v,C,O,!1),F=!ce(E)||A;if(!O&&c.values.next({name:v,type:n.type,values:{...i}}),V)return l.isValid&&("onBlur"===e.mode?O&&p():p()),F&&c.state.next({name:v,...A?{}:E});if(!O&&A&&c.state.next({...r}),s.resolver){const{errors:e}=await x([v]);if(j(C),k){const t=Qe(r.errors,a,v),s=Qe(e,a,t.name||v);o=s.error,v=s.name,N=ce(e)}}else y([v],!0),o=(await Re(w,i,f,s.shouldUseNativeValidation))[v],y([v]),j(C),k&&(o?N=!1:l.isValid&&(N=await b(a,!0)));k&&(w._f.deps&&T(w._f.deps),_(v,N,o,E))}var S},O=(e,t)=>{if(U(r.errors,t)&&e.focus)return e.focus(),1},T=async(e,t={})=>{let n,i;const o=he(e);if(s.resolver){const t=await(async e=>{const{errors:t}=await x(e);if(e)for(const s of e){const e=U(t,s);e?K(r.errors,s,e):Le(r.errors,s)}else r.errors=t;return t})(M(e)?e:o);n=ce(t),i=e?!o.some((e=>U(t,e))):n}else e?(i=(await Promise.all(o.map((async e=>{const t=U(a,e);return await b(t&&t._f?{[e]:t}:t)})))).every(Boolean),(i||r.isValid)&&p()):i=n=await b(a);return c.state.next({...!ye(e)||l.isValid&&n!==r.isValid?{}:{name:e},...s.resolver||!e?{isValid:n}:{},errors:r.errors}),t.shouldFocus&&!i&&we(a,O,e?o:d.mount),i},V=e=>{const t={...o.mount?i:n};return M(e)?t:ye(e)?U(t,e):e.map((e=>U(t,e)))},Z=(e,t)=>({invalid:!!U((t||r).errors,e),isDirty:!!U((t||r).dirtyFields,e),error:U((t||r).errors,e),isValidating:!!U(r.validatingFields,e),isTouched:!!U((t||r).touchedFields,e)}),z=(e,t,s)=>{const n=(U(a,e,{_f:{}})._f||{}).ref,i=U(r.errors,e)||{},{ref:o,message:d,type:u,...l}=i;K(r.errors,e,{...l,...t,ref:n}),c.state.next({name:e,errors:r.errors,isValid:!1}),s&&s.shouldFocus&&n&&n.focus&&n.focus()},W=(e,t={})=>{for(const o of e?he(e):d.mount)d.mount.delete(o),d.array.delete(o),t.keepValue||(Le(a,o),Le(i,o)),!t.keepError&&Le(r.errors,o),!t.keepDirty&&Le(r.dirtyFields,o),!t.keepTouched&&Le(r.touchedFields,o),!t.keepIsValidating&&Le(r.validatingFields,o),!s.shouldUnregister&&!t.keepDefaultValue&&Le(n,o);c.values.next({values:{...i}}),c.state.next({...r,...t.keepDirty?{isDirty:k()}:{}}),!t.keepIsValid&&p()},Y=({disabled:e,name:t,field:s,fields:r,value:a})=>{if(B(e)&&o.mount||e){const n=e?void 0:M(a)?Ge(s?s._f:U(r,t)._f):a;K(i,t,n),g(t,n,!1,!1,!0)}},G=(t,r={})=>{let i=U(a,t);const u=B(r.disabled)||B(e.disabled);return K(a,t,{...i||{},_f:{...i&&i._f?i._f:{ref:{name:t}},name:t,mount:!0,...r}}),d.mount.add(t),i?Y({field:i,disabled:B(r.disabled)?r.disabled:e.disabled,name:t,value:r.value}):v(t,!0,r.value),{...u?{disabled:r.disabled||e.disabled}:{},...s.progressive?{required:!!r.required,min:Je(r.min),max:Je(r.max),minLength:Je(r.minLength),maxLength:Je(r.maxLength),pattern:Je(r.pattern)}:{},name:t,onChange:C,onBlur:C,ref:e=>{if(e){G(t,r),i=U(a,t);const s=M(e.value)&&e.querySelectorAll&&e.querySelectorAll("input,select,textarea")[0]||e,o=(e=>Te(e)||A(e))(s),d=i._f.refs||[];if(o?d.find((e=>e===s)):s===i._f.ref)return;K(a,t,{_f:{...i._f,...o?{refs:[...d.filter(ze),s,...Array.isArray(U(n,t))?[{}]:[]],ref:{type:s.type,name:t}}:{ref:s}}}),v(t,!1,void 0,s)}else i=U(a,t,{}),i._f&&(i._f.mount=!1),(s.shouldUnregister||r.shouldUnregister)&&(!P(d.array,t)||!o.action)&&d.unMount.add(t)}}},J=()=>s.shouldFocusError&&we(a,O,d.mount),X=(e,t)=>async n=>{let o;n&&(n.preventDefault&&n.preventDefault(),n.persist&&n.persist());let d=L(i);if(c.state.next({isSubmitting:!0}),s.resolver){const{errors:e,values:t}=await x();r.errors=e,d=t}else await b(a);if(Le(r.errors,"root"),ce(r.errors)){c.state.next({errors:{}});try{await e(d,n)}catch(u){o=u}}else t&&await t({...r.errors},n),J(),setTimeout(J);if(c.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:ce(r.errors)&&!o,submitCount:r.submitCount+1,errors:r.errors}),o)throw o},Q=(t,s={})=>{const u=t?L(t):n,m=L(u),h=ce(t),f=h?n:m;if(s.keepDefaultValues||(n=u),!s.keepValues){if(s.keepDirtyValues){const e=new Set([...d.mount,...Object.keys(He(n,i))]);for(const t of Array.from(e))U(r.dirtyFields,t)?K(f,t,U(i,t)):N(t,U(f,t))}else{if(R&&M(t))for(const e of d.mount){const t=U(a,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(Ce(e)){const t=e.closest("form");if(t){t.reset();break}}}}a={}}i=e.shouldUnregister?s.keepDefaultValues?L(n):{}:L(f),c.array.next({values:{...f}}),c.values.next({values:{...f}})}d={mount:s.keepDirtyValues?d.mount:new Set,unMount:new Set,array:new Set,watch:new Set,watchAll:!1,focus:""},o.mount=!l.isValid||!!s.keepIsValid||!!s.keepDirtyValues,o.watch=!!e.shouldUnregister,c.state.next({submitCount:s.keepSubmitCount?r.submitCount:0,isDirty:!h&&(s.keepDirty?r.isDirty:!(!s.keepDefaultValues||Ue(t,n))),isSubmitted:!!s.keepIsSubmitted&&r.isSubmitted,dirtyFields:h?{}:s.keepDirtyValues?s.keepDefaultValues&&i?He(n,i):r.dirtyFields:s.keepDefaultValues&&t?He(n,t):s.keepDirty?r.dirtyFields:{},touchedFields:s.keepTouched?r.touchedFields:{},errors:s.keepErrors?r.errors:{},isSubmitSuccessful:!!s.keepIsSubmitSuccessful&&r.isSubmitSuccessful,isSubmitting:!1})},te=(e,t)=>Q(Ne(e)?e(i):e,t);return{control:{register:G,unregister:W,getFieldState:Z,handleSubmit:X,setError:z,_executeSchema:x,_getWatch:w,_getDirty:k,_updateValid:p,_removeUnmounted:()=>{for(const e of d.unMount){const t=U(a,e);t&&(t._f.refs?t._f.refs.every((e=>!ze(e))):!ze(t._f.ref))&&W(e)}d.unMount=new Set},_updateFieldArray:(t,s=[],d,u,m=!0,h=!0)=>{if(u&&d&&!e.disabled){if(o.action=!0,h&&Array.isArray(U(a,t))){const e=d(U(a,t),u.argA,u.argB);m&&K(a,t,e)}if(h&&Array.isArray(U(r.errors,t))){const e=d(U(r.errors,t),u.argA,u.argB);m&&K(r.errors,t,e),((e,t)=>{!$(U(e,t)).length&&Le(e,t)})(r.errors,t)}if(l.touchedFields&&h&&Array.isArray(U(r.touchedFields,t))){const e=d(U(r.touchedFields,t),u.argA,u.argB);m&&K(r.touchedFields,t,e)}l.dirtyFields&&(r.dirtyFields=He(n,i)),c.state.next({name:t,isDirty:k(t,s),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else K(i,t,s)},_updateDisabledField:Y,_getFieldArray:t=>$(U(o.mount?i:n,t,e.shouldUnregister?U(n,t,[]):[])),_reset:Q,_resetDefaultValues:()=>Ne(s.defaultValues)&&s.defaultValues().then((e=>{te(e,s.resetOptions),c.state.next({isLoading:!1})})),_updateFormState:e=>{r={...r,...e}},_disableForm:e=>{B(e)&&(c.state.next({disabled:e}),we(a,((t,s)=>{const r=U(a,s);r&&(t.disabled=r._f.disabled||e,Array.isArray(r._f.refs)&&r._f.refs.forEach((t=>{t.disabled=r._f.disabled||e})))}),0,!1))},_subjects:c,_proxyFormState:l,_setErrors:e=>{r.errors=e,c.state.next({errors:r.errors,isValid:!1})},get _fields(){return a},get _formValues(){return i},get _state(){return o},set _state(e){o=e},get _defaultValues(){return n},get _names(){return d},set _names(e){d=e},get _formState(){return r},set _formState(e){r=e},get _options(){return s},set _options(e){s={...s,...e}}},trigger:T,register:G,handleSubmit:X,watch:(e,t)=>Ne(e)?c.values.subscribe({next:s=>e(w(void 0,t),s)}):w(e,t,!0),setValue:N,getValues:V,reset:te,resetField:(e,t={})=>{U(a,e)&&(M(t.defaultValue)?N(e,L(U(n,e))):(N(e,t.defaultValue),K(n,e,L(t.defaultValue))),t.keepTouched||Le(r.touchedFields,e),t.keepDirty||(Le(r.dirtyFields,e),r.isDirty=t.defaultValue?k(e,L(U(n,e))):k()),t.keepError||(Le(r.errors,e),l.isValid&&p()),c.state.next({...r}))},clearErrors:e=>{e&&he(e).forEach((e=>Le(r.errors,e))),c.state.next({errors:e?r.errors:{}})},unregister:W,setError:z,setFocus:(e,t={})=>{const s=U(a,e),r=s&&s._f;if(r){const e=r.refs?r.refs[0]:r.ref;e.focus&&(e.focus(),t.shouldSelect&&e.select())}},getFieldState:Z}}const st=e=>{const{children:t,...s}=e;return a.createElement(de.Provider,{value:s},t)},rt=n.createContext({}),at=({...t})=>e.jsx(rt.Provider,{value:{name:t.name},children:e.jsx(_e,{...t})}),nt=()=>{const e=n.useContext(rt),t=n.useContext(it),{getFieldState:s,formState:r}=ue(),a=s(e.name,r);if(!e)throw new Error("useFormField should be used within <FormField>");const{id:i}=t;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...a}},it=n.createContext({}),ot=n.forwardRef((({className:t,...s},r)=>{const a=n.useId();return e.jsx(it.Provider,{value:{id:a},children:e.jsx("div",{ref:r,className:c("space-y-2",t),...s})})}));ot.displayName="FormItem";const dt=n.forwardRef((({className:t,...s},r)=>{const{error:a,formItemId:n}=nt();return e.jsx(j,{ref:r,className:c(a&&"text-destructive",t),htmlFor:n,...s})}));dt.displayName="FormLabel";const ut=n.forwardRef((({...t},s)=>{const{error:r,formItemId:a,formDescriptionId:n,formMessageId:i}=nt();return e.jsx(w,{ref:s,id:a,"aria-describedby":r?`${n} ${i}`:`${n}`,"aria-invalid":!!r,...t})}));ut.displayName="FormControl";n.forwardRef((({className:t,...s},r)=>{const{formDescriptionId:a}=nt();return e.jsx("p",{ref:r,id:a,className:c("text-sm text-muted-foreground",t),...s})})).displayName="FormDescription";const lt=n.forwardRef((({className:t,children:s,...r},a)=>{const{error:n,formMessageId:i}=nt(),o=n?String(n?.message).replace(/[<>&"']/g,(e=>{switch(e){case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";case'"':return"&quot;";case"'":return"&#39;";default:return e}})):s;return o?e.jsx("p",{ref:a,id:i,className:c("text-sm font-medium text-destructive",t),...r,children:o}):null}));lt.displayName="FormMessage";const ct=(e,t,s)=>{if(e&&"reportValidity"in e){const r=U(s,t);e.setCustomValidity(r&&r.message||""),e.reportValidity()}},mt=(e,t)=>{for(const s in t.fields){const r=t.fields[s];r&&r.ref&&"reportValidity"in r.ref?ct(r.ref,s,e):r.refs&&r.refs.forEach((t=>ct(t,s,e)))}},ht=(e,t)=>{t.shouldUseNativeValidation&&mt(e,t);const s={};for(const r in e){const a=U(t.fields,r),n=Object.assign(e[r]||{},{ref:a&&a.ref});if(ft(t.names||Object.keys(e),r)){const e=Object.assign({},U(s,r));K(e,"root",n),K(s,r,e)}else K(s,r,n)}return s},ft=(e,t)=>e.some((e=>e.startsWith(t+".")));var pt,yt,vt=function(e,t){for(var s={};e.length;){var r=e[0],a=r.code,n=r.message,i=r.path.join(".");if(!s[i])if("unionErrors"in r){var o=r.unionErrors[0].errors[0];s[i]={message:o.message,type:o.code}}else s[i]={message:n,type:a};if("unionErrors"in r&&r.unionErrors.forEach((function(t){return t.errors.forEach((function(t){return e.push(t)}))})),t){var d=s[i].types,u=d&&d[r.code];s[i]=xe(i,t,s,a,u?[].concat(u,r.message):r.message)}e.shift()}return s},gt=function(e,t,s){return void 0===s&&(s={}),function(r,a,n){try{return Promise.resolve(function(a,i){try{var o=Promise.resolve(e["sync"===s.mode?"parse":"parseAsync"](r,t)).then((function(e){return n.shouldUseNativeValidation&&mt({},n),{errors:{},values:s.raw?r:e}}))}catch(ht){return i(ht)}return o&&o.then?o.then(void 0,i):o}(0,(function(e){if(function(e){return Array.isArray(null==e?void 0:e.errors)}(e))return{values:{},errors:ht(vt(e.errors,!n.shouldUseNativeValidation&&"all"===n.criteriaMode),n)};throw e})))}catch(ht){return Promise.reject(ht)}}};!function(e){e.assertEqual=e=>e,e.assertIs=function(e){},e.assertNever=function(e){throw new Error},e.arrayToEnum=e=>{const t={};for(const s of e)t[s]=s;return t},e.getValidEnumValues=t=>{const s=e.objectKeys(t).filter((e=>"number"!=typeof t[t[e]])),r={};for(const e of s)r[e]=t[e];return e.objectValues(r)},e.objectValues=t=>e.objectKeys(t).map((function(e){return t[e]})),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{const t=[];for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.push(s);return t},e.find=(e,t)=>{for(const s of e)if(t(s))return s},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map((e=>"string"==typeof e?`'${e}'`:e)).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(pt||(pt={})),function(e){e.mergeShapes=(e,t)=>({...e,...t})}(yt||(yt={}));const _t=pt.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),xt=e=>{switch(typeof e){case"undefined":return _t.undefined;case"string":return _t.string;case"number":return isNaN(e)?_t.nan:_t.number;case"boolean":return _t.boolean;case"function":return _t.function;case"bigint":return _t.bigint;case"symbol":return _t.symbol;case"object":return Array.isArray(e)?_t.array:null===e?_t.null:e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch?_t.promise:"undefined"!=typeof Map&&e instanceof Map?_t.map:"undefined"!=typeof Set&&e instanceof Set?_t.set:"undefined"!=typeof Date&&e instanceof Date?_t.date:_t.object;default:return _t.unknown}},bt=pt.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class kt extends Error{constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}get errors(){return this.issues}format(e){const t=e||function(e){return e.message},s={_errors:[]},r=e=>{for(const a of e.issues)if("invalid_union"===a.code)a.unionErrors.map(r);else if("invalid_return_type"===a.code)r(a.returnTypeError);else if("invalid_arguments"===a.code)r(a.argumentsError);else if(0===a.path.length)s._errors.push(t(a));else{let e=s,r=0;for(;r<a.path.length;){const s=a.path[r];r===a.path.length-1?(e[s]=e[s]||{_errors:[]},e[s]._errors.push(t(a))):e[s]=e[s]||{_errors:[]},e=e[s],r++}}};return r(this),s}static assert(e){if(!(e instanceof kt))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,pt.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){const t={},s=[];for(const r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):s.push(e(r));return{formErrors:s,fieldErrors:t}}get formErrors(){return this.flatten()}}kt.create=e=>new kt(e);const wt=(e,t)=>{let s;switch(e.code){case bt.invalid_type:s=e.received===_t.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case bt.invalid_literal:s=`Invalid literal value, expected ${JSON.stringify(e.expected,pt.jsonStringifyReplacer)}`;break;case bt.unrecognized_keys:s=`Unrecognized key(s) in object: ${pt.joinValues(e.keys,", ")}`;break;case bt.invalid_union:s="Invalid input";break;case bt.invalid_union_discriminator:s=`Invalid discriminator value. Expected ${pt.joinValues(e.options)}`;break;case bt.invalid_enum_value:s=`Invalid enum value. Expected ${pt.joinValues(e.options)}, received '${e.received}'`;break;case bt.invalid_arguments:s="Invalid function arguments";break;case bt.invalid_return_type:s="Invalid function return type";break;case bt.invalid_date:s="Invalid date";break;case bt.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(s=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(s=`${s} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?s=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?s=`Invalid input: must end with "${e.validation.endsWith}"`:pt.assertNever(e.validation):s="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case bt.too_small:s="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case bt.too_big:s="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case bt.custom:s="Invalid input";break;case bt.invalid_intersection_types:s="Intersection results could not be merged";break;case bt.not_multiple_of:s=`Number must be a multiple of ${e.multipleOf}`;break;case bt.not_finite:s="Number must be finite";break;default:s=t.defaultError,pt.assertNever(e)}return{message:s}};let jt=wt;function St(){return jt}const Nt=e=>{const{data:t,path:s,errorMaps:r,issueData:a}=e,n=[...s,...a.path||[]],i={...a,path:n};if(void 0!==a.message)return{...a,path:n,message:a.message};let o="";const d=r.filter((e=>!!e)).slice().reverse();for(const u of d)o=u(i,{data:t,defaultError:o}).message;return{...a,path:n,message:o}};function Ct(e,t){const s=St(),r=Nt({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,s,s===wt?void 0:wt].filter((e=>!!e))});e.common.issues.push(r)}class Ot{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){const s=[];for(const r of t){if("aborted"===r.status)return Tt;"dirty"===r.status&&e.dirty(),s.push(r.value)}return{status:e.value,value:s}}static async mergeObjectAsync(e,t){const s=[];for(const r of t){const e=await r.key,t=await r.value;s.push({key:e,value:t})}return Ot.mergeObjectSync(e,s)}static mergeObjectSync(e,t){const s={};for(const r of t){const{key:t,value:a}=r;if("aborted"===t.status)return Tt;if("aborted"===a.status)return Tt;"dirty"===t.status&&e.dirty(),"dirty"===a.status&&e.dirty(),"__proto__"===t.value||void 0===a.value&&!r.alwaysSet||(s[t.value]=a.value)}return{status:e.value,value:s}}}const Tt=Object.freeze({status:"aborted"}),Vt=e=>({status:"dirty",value:e}),At=e=>({status:"valid",value:e}),Et=e=>"aborted"===e.status,Ft=e=>"dirty"===e.status,Zt=e=>"valid"===e.status,Dt=e=>"undefined"!=typeof Promise&&e instanceof Promise;function It(e,t,s,r){if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t.get(e)}function Pt(e,t,s,r,a){if("function"==typeof t?e!==t||!a:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return t.set(e,s),s}var Rt,Lt,$t;"function"==typeof SuppressedError&&SuppressedError,function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:null==e?void 0:e.message}(Rt||(Rt={}));class Mt{constructor(e,t,s,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=s,this._key=r}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Ut=(e,t)=>{if(Zt(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new kt(e.common.issues);return this._error=t,this._error}}};function Bt(e){if(!e)return{};const{errorMap:t,invalid_type_error:s,required_error:r,description:a}=e;if(t&&(s||r))throw new Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');if(t)return{errorMap:t,description:a};return{errorMap:(t,a)=>{var n,i;const{message:o}=e;return"invalid_enum_value"===t.code?{message:null!=o?o:a.defaultError}:void 0===a.data?{message:null!==(n=null!=o?o:r)&&void 0!==n?n:a.defaultError}:"invalid_type"!==t.code?{message:a.defaultError}:{message:null!==(i=null!=o?o:s)&&void 0!==i?i:a.defaultError}},description:a}}class zt{constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this)}get description(){return this._def.description}_getType(e){return xt(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:xt(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new Ot,ctx:{common:e.parent.common,data:e.data,parsedType:xt(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(Dt(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const s=this.safeParse(e,t);if(s.success)return s.data;throw s.error}safeParse(e,t){var s;const r={common:{issues:[],async:null!==(s=null==t?void 0:t.async)&&void 0!==s&&s,contextualErrorMap:null==t?void 0:t.errorMap},path:(null==t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:xt(e)},a=this._parseSync({data:e,path:r.path,parent:r});return Ut(r,a)}async parseAsync(e,t){const s=await this.safeParseAsync(e,t);if(s.success)return s.data;throw s.error}async safeParseAsync(e,t){const s={common:{issues:[],contextualErrorMap:null==t?void 0:t.errorMap,async:!0},path:(null==t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:xt(e)},r=this._parse({data:e,path:s.path,parent:s}),a=await(Dt(r)?r:Promise.resolve(r));return Ut(s,a)}refine(e,t){const s=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement(((t,r)=>{const a=e(t),n=()=>r.addIssue({code:bt.custom,...s(t)});return"undefined"!=typeof Promise&&a instanceof Promise?a.then((e=>!!e||(n(),!1))):!!a||(n(),!1)}))}refinement(e,t){return this._refinement(((s,r)=>!!e(s)||(r.addIssue("function"==typeof t?t(s,r):t),!1)))}_refinement(e){return new Zs({schema:this,typeName:Bs.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}optional(){return Ds.create(this,this._def)}nullable(){return Is.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return _s.create(this,this._def)}promise(){return Fs.create(this,this._def)}or(e){return ks.create([this,e],this._def)}and(e){return js.create(this,e,this._def)}transform(e){return new Zs({...Bt(this._def),schema:this,typeName:Bs.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t="function"==typeof e?e:()=>e;return new Ps({...Bt(this._def),innerType:this,defaultValue:t,typeName:Bs.ZodDefault})}brand(){return new $s({typeName:Bs.ZodBranded,type:this,...Bt(this._def)})}catch(e){const t="function"==typeof e?e:()=>e;return new Rs({...Bt(this._def),innerType:this,catchValue:t,typeName:Bs.ZodCatch})}describe(e){return new(0,this.constructor)({...this._def,description:e})}pipe(e){return Ms.create(this,e)}readonly(){return Us.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const Wt=/^c[^\s-]{8,}$/i,Kt=/^[0-9a-z]+$/,qt=/^[0-9A-HJKMNP-TV-Z]{26}$/,Ht=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Yt=/^[a-z0-9_-]{21}$/i,Gt=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Jt=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let Xt;const Qt=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,es=/^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,ts=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,ss="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",rs=new RegExp(`^${ss}$`);function as(e){let t="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`),t}function ns(e){let t=`${ss}T${as(e)}`;const s=[];return s.push(e.local?"Z?":"Z"),e.offset&&s.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${s.join("|")})`,new RegExp(`^${t}$`)}class is extends zt{_parse(e){this._def.coerce&&(e.data=String(e.data));if(this._getType(e)!==_t.string){const t=this._getOrReturnCtx(e);return Ct(t,{code:bt.invalid_type,expected:_t.string,received:t.parsedType}),Tt}const t=new Ot;let s;for(const i of this._def.checks)if("min"===i.kind)e.data.length<i.value&&(s=this._getOrReturnCtx(e,s),Ct(s,{code:bt.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if("max"===i.kind)e.data.length>i.value&&(s=this._getOrReturnCtx(e,s),Ct(s,{code:bt.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if("length"===i.kind){const r=e.data.length>i.value,a=e.data.length<i.value;(r||a)&&(s=this._getOrReturnCtx(e,s),r?Ct(s,{code:bt.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):a&&Ct(s,{code:bt.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),t.dirty())}else if("email"===i.kind)Jt.test(e.data)||(s=this._getOrReturnCtx(e,s),Ct(s,{validation:"email",code:bt.invalid_string,message:i.message}),t.dirty());else if("emoji"===i.kind)Xt||(Xt=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),Xt.test(e.data)||(s=this._getOrReturnCtx(e,s),Ct(s,{validation:"emoji",code:bt.invalid_string,message:i.message}),t.dirty());else if("uuid"===i.kind)Ht.test(e.data)||(s=this._getOrReturnCtx(e,s),Ct(s,{validation:"uuid",code:bt.invalid_string,message:i.message}),t.dirty());else if("nanoid"===i.kind)Yt.test(e.data)||(s=this._getOrReturnCtx(e,s),Ct(s,{validation:"nanoid",code:bt.invalid_string,message:i.message}),t.dirty());else if("cuid"===i.kind)Wt.test(e.data)||(s=this._getOrReturnCtx(e,s),Ct(s,{validation:"cuid",code:bt.invalid_string,message:i.message}),t.dirty());else if("cuid2"===i.kind)Kt.test(e.data)||(s=this._getOrReturnCtx(e,s),Ct(s,{validation:"cuid2",code:bt.invalid_string,message:i.message}),t.dirty());else if("ulid"===i.kind)qt.test(e.data)||(s=this._getOrReturnCtx(e,s),Ct(s,{validation:"ulid",code:bt.invalid_string,message:i.message}),t.dirty());else if("url"===i.kind)try{new URL(e.data)}catch(n){s=this._getOrReturnCtx(e,s),Ct(s,{validation:"url",code:bt.invalid_string,message:i.message}),t.dirty()}else if("regex"===i.kind){i.regex.lastIndex=0;i.regex.test(e.data)||(s=this._getOrReturnCtx(e,s),Ct(s,{validation:"regex",code:bt.invalid_string,message:i.message}),t.dirty())}else if("trim"===i.kind)e.data=e.data.trim();else if("includes"===i.kind)e.data.includes(i.value,i.position)||(s=this._getOrReturnCtx(e,s),Ct(s,{code:bt.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),t.dirty());else if("toLowerCase"===i.kind)e.data=e.data.toLowerCase();else if("toUpperCase"===i.kind)e.data=e.data.toUpperCase();else if("startsWith"===i.kind)e.data.startsWith(i.value)||(s=this._getOrReturnCtx(e,s),Ct(s,{code:bt.invalid_string,validation:{startsWith:i.value},message:i.message}),t.dirty());else if("endsWith"===i.kind)e.data.endsWith(i.value)||(s=this._getOrReturnCtx(e,s),Ct(s,{code:bt.invalid_string,validation:{endsWith:i.value},message:i.message}),t.dirty());else if("datetime"===i.kind){ns(i).test(e.data)||(s=this._getOrReturnCtx(e,s),Ct(s,{code:bt.invalid_string,validation:"datetime",message:i.message}),t.dirty())}else if("date"===i.kind){rs.test(e.data)||(s=this._getOrReturnCtx(e,s),Ct(s,{code:bt.invalid_string,validation:"date",message:i.message}),t.dirty())}else if("time"===i.kind){new RegExp(`^${as(i)}$`).test(e.data)||(s=this._getOrReturnCtx(e,s),Ct(s,{code:bt.invalid_string,validation:"time",message:i.message}),t.dirty())}else"duration"===i.kind?Gt.test(e.data)||(s=this._getOrReturnCtx(e,s),Ct(s,{validation:"duration",code:bt.invalid_string,message:i.message}),t.dirty()):"ip"===i.kind?(r=e.data,("v4"!==(a=i.version)&&a||!Qt.test(r))&&("v6"!==a&&a||!es.test(r))&&(s=this._getOrReturnCtx(e,s),Ct(s,{validation:"ip",code:bt.invalid_string,message:i.message}),t.dirty())):"base64"===i.kind?ts.test(e.data)||(s=this._getOrReturnCtx(e,s),Ct(s,{validation:"base64",code:bt.invalid_string,message:i.message}),t.dirty()):pt.assertNever(i);var r,a;return{status:t.value,value:e.data}}_regex(e,t,s){return this.refinement((t=>e.test(t)),{validation:t,code:bt.invalid_string,...Rt.errToObj(s)})}_addCheck(e){return new is({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...Rt.errToObj(e)})}url(e){return this._addCheck({kind:"url",...Rt.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...Rt.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...Rt.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...Rt.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...Rt.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...Rt.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...Rt.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...Rt.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...Rt.errToObj(e)})}datetime(e){var t,s;return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===(null==e?void 0:e.precision)?null:null==e?void 0:e.precision,offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t&&t,local:null!==(s=null==e?void 0:e.local)&&void 0!==s&&s,...Rt.errToObj(null==e?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===(null==e?void 0:e.precision)?null:null==e?void 0:e.precision,...Rt.errToObj(null==e?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...Rt.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...Rt.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:null==t?void 0:t.position,...Rt.errToObj(null==t?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...Rt.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...Rt.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...Rt.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...Rt.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...Rt.errToObj(t)})}nonempty(e){return this.min(1,Rt.errToObj(e))}trim(){return new is({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new is({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new is({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find((e=>"datetime"===e.kind))}get isDate(){return!!this._def.checks.find((e=>"date"===e.kind))}get isTime(){return!!this._def.checks.find((e=>"time"===e.kind))}get isDuration(){return!!this._def.checks.find((e=>"duration"===e.kind))}get isEmail(){return!!this._def.checks.find((e=>"email"===e.kind))}get isURL(){return!!this._def.checks.find((e=>"url"===e.kind))}get isEmoji(){return!!this._def.checks.find((e=>"emoji"===e.kind))}get isUUID(){return!!this._def.checks.find((e=>"uuid"===e.kind))}get isNANOID(){return!!this._def.checks.find((e=>"nanoid"===e.kind))}get isCUID(){return!!this._def.checks.find((e=>"cuid"===e.kind))}get isCUID2(){return!!this._def.checks.find((e=>"cuid2"===e.kind))}get isULID(){return!!this._def.checks.find((e=>"ulid"===e.kind))}get isIP(){return!!this._def.checks.find((e=>"ip"===e.kind))}get isBase64(){return!!this._def.checks.find((e=>"base64"===e.kind))}get minLength(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}function os(e,t){const s=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,a=s>r?s:r;return parseInt(e.toFixed(a).replace(".",""))%parseInt(t.toFixed(a).replace(".",""))/Math.pow(10,a)}is.create=e=>{var t;return new is({checks:[],typeName:Bs.ZodString,coerce:null!==(t=null==e?void 0:e.coerce)&&void 0!==t&&t,...Bt(e)})};class ds extends zt{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){this._def.coerce&&(e.data=Number(e.data));if(this._getType(e)!==_t.number){const t=this._getOrReturnCtx(e);return Ct(t,{code:bt.invalid_type,expected:_t.number,received:t.parsedType}),Tt}let t;const s=new Ot;for(const r of this._def.checks)if("int"===r.kind)pt.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),Ct(t,{code:bt.invalid_type,expected:"integer",received:"float",message:r.message}),s.dirty());else if("min"===r.kind){(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),Ct(t,{code:bt.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),s.dirty())}else if("max"===r.kind){(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),Ct(t,{code:bt.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),s.dirty())}else"multipleOf"===r.kind?0!==os(e.data,r.value)&&(t=this._getOrReturnCtx(e,t),Ct(t,{code:bt.not_multiple_of,multipleOf:r.value,message:r.message}),s.dirty()):"finite"===r.kind?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),Ct(t,{code:bt.not_finite,message:r.message}),s.dirty()):pt.assertNever(r);return{status:s.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,Rt.toString(t))}gt(e,t){return this.setLimit("min",e,!1,Rt.toString(t))}lte(e,t){return this.setLimit("max",e,!0,Rt.toString(t))}lt(e,t){return this.setLimit("max",e,!1,Rt.toString(t))}setLimit(e,t,s,r){return new ds({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:Rt.toString(r)}]})}_addCheck(e){return new ds({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:Rt.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:Rt.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:Rt.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:Rt.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:Rt.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:Rt.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:Rt.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:Rt.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:Rt.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find((e=>"int"===e.kind||"multipleOf"===e.kind&&pt.isInteger(e.value)))}get isFinite(){let e=null,t=null;for(const s of this._def.checks){if("finite"===s.kind||"int"===s.kind||"multipleOf"===s.kind)return!0;"min"===s.kind?(null===t||s.value>t)&&(t=s.value):"max"===s.kind&&(null===e||s.value<e)&&(e=s.value)}return Number.isFinite(t)&&Number.isFinite(e)}}ds.create=e=>new ds({checks:[],typeName:Bs.ZodNumber,coerce:(null==e?void 0:e.coerce)||!1,...Bt(e)});class us extends zt{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){this._def.coerce&&(e.data=BigInt(e.data));if(this._getType(e)!==_t.bigint){const t=this._getOrReturnCtx(e);return Ct(t,{code:bt.invalid_type,expected:_t.bigint,received:t.parsedType}),Tt}let t;const s=new Ot;for(const r of this._def.checks)if("min"===r.kind){(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),Ct(t,{code:bt.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),s.dirty())}else if("max"===r.kind){(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),Ct(t,{code:bt.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),s.dirty())}else"multipleOf"===r.kind?e.data%r.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),Ct(t,{code:bt.not_multiple_of,multipleOf:r.value,message:r.message}),s.dirty()):pt.assertNever(r);return{status:s.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,Rt.toString(t))}gt(e,t){return this.setLimit("min",e,!1,Rt.toString(t))}lte(e,t){return this.setLimit("max",e,!0,Rt.toString(t))}lt(e,t){return this.setLimit("max",e,!1,Rt.toString(t))}setLimit(e,t,s,r){return new us({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:Rt.toString(r)}]})}_addCheck(e){return new us({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:Rt.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:Rt.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:Rt.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:Rt.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:Rt.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}us.create=e=>{var t;return new us({checks:[],typeName:Bs.ZodBigInt,coerce:null!==(t=null==e?void 0:e.coerce)&&void 0!==t&&t,...Bt(e)})};class ls extends zt{_parse(e){this._def.coerce&&(e.data=Boolean(e.data));if(this._getType(e)!==_t.boolean){const t=this._getOrReturnCtx(e);return Ct(t,{code:bt.invalid_type,expected:_t.boolean,received:t.parsedType}),Tt}return At(e.data)}}ls.create=e=>new ls({typeName:Bs.ZodBoolean,coerce:(null==e?void 0:e.coerce)||!1,...Bt(e)});class cs extends zt{_parse(e){this._def.coerce&&(e.data=new Date(e.data));if(this._getType(e)!==_t.date){const t=this._getOrReturnCtx(e);return Ct(t,{code:bt.invalid_type,expected:_t.date,received:t.parsedType}),Tt}if(isNaN(e.data.getTime())){return Ct(this._getOrReturnCtx(e),{code:bt.invalid_date}),Tt}const t=new Ot;let s;for(const r of this._def.checks)"min"===r.kind?e.data.getTime()<r.value&&(s=this._getOrReturnCtx(e,s),Ct(s,{code:bt.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),t.dirty()):"max"===r.kind?e.data.getTime()>r.value&&(s=this._getOrReturnCtx(e,s),Ct(s,{code:bt.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),t.dirty()):pt.assertNever(r);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new cs({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:Rt.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:Rt.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}cs.create=e=>new cs({checks:[],coerce:(null==e?void 0:e.coerce)||!1,typeName:Bs.ZodDate,...Bt(e)});class ms extends zt{_parse(e){if(this._getType(e)!==_t.symbol){const t=this._getOrReturnCtx(e);return Ct(t,{code:bt.invalid_type,expected:_t.symbol,received:t.parsedType}),Tt}return At(e.data)}}ms.create=e=>new ms({typeName:Bs.ZodSymbol,...Bt(e)});class hs extends zt{_parse(e){if(this._getType(e)!==_t.undefined){const t=this._getOrReturnCtx(e);return Ct(t,{code:bt.invalid_type,expected:_t.undefined,received:t.parsedType}),Tt}return At(e.data)}}hs.create=e=>new hs({typeName:Bs.ZodUndefined,...Bt(e)});class fs extends zt{_parse(e){if(this._getType(e)!==_t.null){const t=this._getOrReturnCtx(e);return Ct(t,{code:bt.invalid_type,expected:_t.null,received:t.parsedType}),Tt}return At(e.data)}}fs.create=e=>new fs({typeName:Bs.ZodNull,...Bt(e)});class ps extends zt{constructor(){super(...arguments),this._any=!0}_parse(e){return At(e.data)}}ps.create=e=>new ps({typeName:Bs.ZodAny,...Bt(e)});class ys extends zt{constructor(){super(...arguments),this._unknown=!0}_parse(e){return At(e.data)}}ys.create=e=>new ys({typeName:Bs.ZodUnknown,...Bt(e)});class vs extends zt{_parse(e){const t=this._getOrReturnCtx(e);return Ct(t,{code:bt.invalid_type,expected:_t.never,received:t.parsedType}),Tt}}vs.create=e=>new vs({typeName:Bs.ZodNever,...Bt(e)});class gs extends zt{_parse(e){if(this._getType(e)!==_t.undefined){const t=this._getOrReturnCtx(e);return Ct(t,{code:bt.invalid_type,expected:_t.void,received:t.parsedType}),Tt}return At(e.data)}}gs.create=e=>new gs({typeName:Bs.ZodVoid,...Bt(e)});class _s extends zt{_parse(e){const{ctx:t,status:s}=this._processInputParams(e),r=this._def;if(t.parsedType!==_t.array)return Ct(t,{code:bt.invalid_type,expected:_t.array,received:t.parsedType}),Tt;if(null!==r.exactLength){const e=t.data.length>r.exactLength.value,a=t.data.length<r.exactLength.value;(e||a)&&(Ct(t,{code:e?bt.too_big:bt.too_small,minimum:a?r.exactLength.value:void 0,maximum:e?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),s.dirty())}if(null!==r.minLength&&t.data.length<r.minLength.value&&(Ct(t,{code:bt.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),s.dirty()),null!==r.maxLength&&t.data.length>r.maxLength.value&&(Ct(t,{code:bt.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),s.dirty()),t.common.async)return Promise.all([...t.data].map(((e,s)=>r.type._parseAsync(new Mt(t,e,t.path,s))))).then((e=>Ot.mergeArray(s,e)));const a=[...t.data].map(((e,s)=>r.type._parseSync(new Mt(t,e,t.path,s))));return Ot.mergeArray(s,a)}get element(){return this._def.type}min(e,t){return new _s({...this._def,minLength:{value:e,message:Rt.toString(t)}})}max(e,t){return new _s({...this._def,maxLength:{value:e,message:Rt.toString(t)}})}length(e,t){return new _s({...this._def,exactLength:{value:e,message:Rt.toString(t)}})}nonempty(e){return this.min(1,e)}}function xs(e){if(e instanceof bs){const t={};for(const s in e.shape){const r=e.shape[s];t[s]=Ds.create(xs(r))}return new bs({...e._def,shape:()=>t})}return e instanceof _s?new _s({...e._def,type:xs(e.element)}):e instanceof Ds?Ds.create(xs(e.unwrap())):e instanceof Is?Is.create(xs(e.unwrap())):e instanceof Ss?Ss.create(e.items.map((e=>xs(e)))):e}_s.create=(e,t)=>new _s({type:e,minLength:null,maxLength:null,exactLength:null,typeName:Bs.ZodArray,...Bt(t)});class bs extends zt{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;const e=this._def.shape(),t=pt.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==_t.object){const t=this._getOrReturnCtx(e);return Ct(t,{code:bt.invalid_type,expected:_t.object,received:t.parsedType}),Tt}const{status:t,ctx:s}=this._processInputParams(e),{shape:r,keys:a}=this._getCached(),n=[];if(!(this._def.catchall instanceof vs&&"strip"===this._def.unknownKeys))for(const o in s.data)a.includes(o)||n.push(o);const i=[];for(const o of a){const e=r[o],t=s.data[o];i.push({key:{status:"valid",value:o},value:e._parse(new Mt(s,t,s.path,o)),alwaysSet:o in s.data})}if(this._def.catchall instanceof vs){const e=this._def.unknownKeys;if("passthrough"===e)for(const t of n)i.push({key:{status:"valid",value:t},value:{status:"valid",value:s.data[t]}});else if("strict"===e)n.length>0&&(Ct(s,{code:bt.unrecognized_keys,keys:n}),t.dirty());else if("strip"!==e)throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const e=this._def.catchall;for(const t of n){const r=s.data[t];i.push({key:{status:"valid",value:t},value:e._parse(new Mt(s,r,s.path,t)),alwaysSet:t in s.data})}}return s.common.async?Promise.resolve().then((async()=>{const e=[];for(const t of i){const s=await t.key,r=await t.value;e.push({key:s,value:r,alwaysSet:t.alwaysSet})}return e})).then((e=>Ot.mergeObjectSync(t,e))):Ot.mergeObjectSync(t,i)}get shape(){return this._def.shape()}strict(e){return Rt.errToObj,new bs({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,s)=>{var r,a,n,i;const o=null!==(n=null===(a=(r=this._def).errorMap)||void 0===a?void 0:a.call(r,t,s).message)&&void 0!==n?n:s.defaultError;return"unrecognized_keys"===t.code?{message:null!==(i=Rt.errToObj(e).message)&&void 0!==i?i:o}:{message:o}}}:{}})}strip(){return new bs({...this._def,unknownKeys:"strip"})}passthrough(){return new bs({...this._def,unknownKeys:"passthrough"})}extend(e){return new bs({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new bs({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:Bs.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new bs({...this._def,catchall:e})}pick(e){const t={};return pt.objectKeys(e).forEach((s=>{e[s]&&this.shape[s]&&(t[s]=this.shape[s])})),new bs({...this._def,shape:()=>t})}omit(e){const t={};return pt.objectKeys(this.shape).forEach((s=>{e[s]||(t[s]=this.shape[s])})),new bs({...this._def,shape:()=>t})}deepPartial(){return xs(this)}partial(e){const t={};return pt.objectKeys(this.shape).forEach((s=>{const r=this.shape[s];e&&!e[s]?t[s]=r:t[s]=r.optional()})),new bs({...this._def,shape:()=>t})}required(e){const t={};return pt.objectKeys(this.shape).forEach((s=>{if(e&&!e[s])t[s]=this.shape[s];else{let e=this.shape[s];for(;e instanceof Ds;)e=e._def.innerType;t[s]=e}})),new bs({...this._def,shape:()=>t})}keyof(){return Vs(pt.objectKeys(this.shape))}}bs.create=(e,t)=>new bs({shape:()=>e,unknownKeys:"strip",catchall:vs.create(),typeName:Bs.ZodObject,...Bt(t)}),bs.strictCreate=(e,t)=>new bs({shape:()=>e,unknownKeys:"strict",catchall:vs.create(),typeName:Bs.ZodObject,...Bt(t)}),bs.lazycreate=(e,t)=>new bs({shape:e,unknownKeys:"strip",catchall:vs.create(),typeName:Bs.ZodObject,...Bt(t)});class ks extends zt{_parse(e){const{ctx:t}=this._processInputParams(e),s=this._def.options;if(t.common.async)return Promise.all(s.map((async e=>{const s={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:s}),ctx:s}}))).then((function(e){for(const t of e)if("valid"===t.result.status)return t.result;for(const r of e)if("dirty"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;const s=e.map((e=>new kt(e.ctx.common.issues)));return Ct(t,{code:bt.invalid_union,unionErrors:s}),Tt}));{let e;const r=[];for(const n of s){const s={...t,common:{...t.common,issues:[]},parent:null},a=n._parseSync({data:t.data,path:t.path,parent:s});if("valid"===a.status)return a;"dirty"!==a.status||e||(e={result:a,ctx:s}),s.common.issues.length&&r.push(s.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;const a=r.map((e=>new kt(e)));return Ct(t,{code:bt.invalid_union,unionErrors:a}),Tt}}get options(){return this._def.options}}ks.create=(e,t)=>new ks({options:e,typeName:Bs.ZodUnion,...Bt(t)});function ws(e,t){const s=xt(e),r=xt(t);if(e===t)return{valid:!0,data:e};if(s===_t.object&&r===_t.object){const s=pt.objectKeys(t),r=pt.objectKeys(e).filter((e=>-1!==s.indexOf(e))),a={...e,...t};for(const n of r){const s=ws(e[n],t[n]);if(!s.valid)return{valid:!1};a[n]=s.data}return{valid:!0,data:a}}if(s===_t.array&&r===_t.array){if(e.length!==t.length)return{valid:!1};const s=[];for(let r=0;r<e.length;r++){const a=ws(e[r],t[r]);if(!a.valid)return{valid:!1};s.push(a.data)}return{valid:!0,data:s}}return s===_t.date&&r===_t.date&&+e==+t?{valid:!0,data:e}:{valid:!1}}class js extends zt{_parse(e){const{status:t,ctx:s}=this._processInputParams(e),r=(e,r)=>{if(Et(e)||Et(r))return Tt;const a=ws(e.value,r.value);return a.valid?((Ft(e)||Ft(r))&&t.dirty(),{status:t.value,value:a.data}):(Ct(s,{code:bt.invalid_intersection_types}),Tt)};return s.common.async?Promise.all([this._def.left._parseAsync({data:s.data,path:s.path,parent:s}),this._def.right._parseAsync({data:s.data,path:s.path,parent:s})]).then((([e,t])=>r(e,t))):r(this._def.left._parseSync({data:s.data,path:s.path,parent:s}),this._def.right._parseSync({data:s.data,path:s.path,parent:s}))}}js.create=(e,t,s)=>new js({left:e,right:t,typeName:Bs.ZodIntersection,...Bt(s)});class Ss extends zt{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==_t.array)return Ct(s,{code:bt.invalid_type,expected:_t.array,received:s.parsedType}),Tt;if(s.data.length<this._def.items.length)return Ct(s,{code:bt.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),Tt;!this._def.rest&&s.data.length>this._def.items.length&&(Ct(s,{code:bt.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const r=[...s.data].map(((e,t)=>{const r=this._def.items[t]||this._def.rest;return r?r._parse(new Mt(s,e,s.path,t)):null})).filter((e=>!!e));return s.common.async?Promise.all(r).then((e=>Ot.mergeArray(t,e))):Ot.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new Ss({...this._def,rest:e})}}Ss.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Ss({items:e,typeName:Bs.ZodTuple,rest:null,...Bt(t)})};class Ns extends zt{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==_t.map)return Ct(s,{code:bt.invalid_type,expected:_t.map,received:s.parsedType}),Tt;const r=this._def.keyType,a=this._def.valueType,n=[...s.data.entries()].map((([e,t],n)=>({key:r._parse(new Mt(s,e,s.path,[n,"key"])),value:a._parse(new Mt(s,t,s.path,[n,"value"]))})));if(s.common.async){const e=new Map;return Promise.resolve().then((async()=>{for(const s of n){const r=await s.key,a=await s.value;if("aborted"===r.status||"aborted"===a.status)return Tt;"dirty"!==r.status&&"dirty"!==a.status||t.dirty(),e.set(r.value,a.value)}return{status:t.value,value:e}}))}{const e=new Map;for(const s of n){const r=s.key,a=s.value;if("aborted"===r.status||"aborted"===a.status)return Tt;"dirty"!==r.status&&"dirty"!==a.status||t.dirty(),e.set(r.value,a.value)}return{status:t.value,value:e}}}}Ns.create=(e,t,s)=>new Ns({valueType:t,keyType:e,typeName:Bs.ZodMap,...Bt(s)});class Cs extends zt{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==_t.set)return Ct(s,{code:bt.invalid_type,expected:_t.set,received:s.parsedType}),Tt;const r=this._def;null!==r.minSize&&s.data.size<r.minSize.value&&(Ct(s,{code:bt.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),null!==r.maxSize&&s.data.size>r.maxSize.value&&(Ct(s,{code:bt.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());const a=this._def.valueType;function n(e){const s=new Set;for(const r of e){if("aborted"===r.status)return Tt;"dirty"===r.status&&t.dirty(),s.add(r.value)}return{status:t.value,value:s}}const i=[...s.data.values()].map(((e,t)=>a._parse(new Mt(s,e,s.path,t))));return s.common.async?Promise.all(i).then((e=>n(e))):n(i)}min(e,t){return new Cs({...this._def,minSize:{value:e,message:Rt.toString(t)}})}max(e,t){return new Cs({...this._def,maxSize:{value:e,message:Rt.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}Cs.create=(e,t)=>new Cs({valueType:e,minSize:null,maxSize:null,typeName:Bs.ZodSet,...Bt(t)});class Os extends zt{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}Os.create=(e,t)=>new Os({getter:e,typeName:Bs.ZodLazy,...Bt(t)});class Ts extends zt{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return Ct(t,{received:t.data,code:bt.invalid_literal,expected:this._def.value}),Tt}return{status:"valid",value:e.data}}get value(){return this._def.value}}function Vs(e,t){return new As({values:e,typeName:Bs.ZodEnum,...Bt(t)})}Ts.create=(e,t)=>new Ts({value:e,typeName:Bs.ZodLiteral,...Bt(t)});class As extends zt{constructor(){super(...arguments),Lt.set(this,void 0)}_parse(e){if("string"!=typeof e.data){const t=this._getOrReturnCtx(e),s=this._def.values;return Ct(t,{expected:pt.joinValues(s),received:t.parsedType,code:bt.invalid_type}),Tt}if(It(this,Lt)||Pt(this,Lt,new Set(this._def.values)),!It(this,Lt).has(e.data)){const t=this._getOrReturnCtx(e),s=this._def.values;return Ct(t,{received:t.data,code:bt.invalid_enum_value,options:s}),Tt}return At(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return As.create(e,{...this._def,...t})}exclude(e,t=this._def){return As.create(this.options.filter((t=>!e.includes(t))),{...this._def,...t})}}Lt=new WeakMap,As.create=Vs;class Es extends zt{constructor(){super(...arguments),$t.set(this,void 0)}_parse(e){const t=pt.getValidEnumValues(this._def.values),s=this._getOrReturnCtx(e);if(s.parsedType!==_t.string&&s.parsedType!==_t.number){const e=pt.objectValues(t);return Ct(s,{expected:pt.joinValues(e),received:s.parsedType,code:bt.invalid_type}),Tt}if(It(this,$t)||Pt(this,$t,new Set(pt.getValidEnumValues(this._def.values))),!It(this,$t).has(e.data)){const e=pt.objectValues(t);return Ct(s,{received:s.data,code:bt.invalid_enum_value,options:e}),Tt}return At(e.data)}get enum(){return this._def.values}}$t=new WeakMap,Es.create=(e,t)=>new Es({values:e,typeName:Bs.ZodNativeEnum,...Bt(t)});class Fs extends zt{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==_t.promise&&!1===t.common.async)return Ct(t,{code:bt.invalid_type,expected:_t.promise,received:t.parsedType}),Tt;const s=t.parsedType===_t.promise?t.data:Promise.resolve(t.data);return At(s.then((e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap}))))}}Fs.create=(e,t)=>new Fs({type:e,typeName:Bs.ZodPromise,...Bt(t)});class Zs extends zt{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===Bs.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:s}=this._processInputParams(e),r=this._def.effect||null,a={addIssue:e=>{Ct(s,e),e.fatal?t.abort():t.dirty()},get path(){return s.path}};if(a.addIssue=a.addIssue.bind(a),"preprocess"===r.type){const e=r.transform(s.data,a);if(s.common.async)return Promise.resolve(e).then((async e=>{if("aborted"===t.value)return Tt;const r=await this._def.schema._parseAsync({data:e,path:s.path,parent:s});return"aborted"===r.status?Tt:"dirty"===r.status||"dirty"===t.value?Vt(r.value):r}));{if("aborted"===t.value)return Tt;const r=this._def.schema._parseSync({data:e,path:s.path,parent:s});return"aborted"===r.status?Tt:"dirty"===r.status||"dirty"===t.value?Vt(r.value):r}}if("refinement"===r.type){const e=e=>{const t=r.refinement(e,a);if(s.common.async)return Promise.resolve(t);if(t instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1===s.common.async){const r=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});return"aborted"===r.status?Tt:("dirty"===r.status&&t.dirty(),e(r.value),{status:t.value,value:r.value})}return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then((s=>"aborted"===s.status?Tt:("dirty"===s.status&&t.dirty(),e(s.value).then((()=>({status:t.value,value:s.value}))))))}if("transform"===r.type){if(!1===s.common.async){const e=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});if(!Zt(e))return e;const n=r.transform(e.value,a);if(n instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:n}}return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then((e=>Zt(e)?Promise.resolve(r.transform(e.value,a)).then((e=>({status:t.value,value:e}))):e))}pt.assertNever(r)}}Zs.create=(e,t,s)=>new Zs({schema:e,typeName:Bs.ZodEffects,effect:t,...Bt(s)}),Zs.createWithPreprocess=(e,t,s)=>new Zs({schema:t,effect:{type:"preprocess",transform:e},typeName:Bs.ZodEffects,...Bt(s)});class Ds extends zt{_parse(e){return this._getType(e)===_t.undefined?At(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Ds.create=(e,t)=>new Ds({innerType:e,typeName:Bs.ZodOptional,...Bt(t)});class Is extends zt{_parse(e){return this._getType(e)===_t.null?At(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Is.create=(e,t)=>new Is({innerType:e,typeName:Bs.ZodNullable,...Bt(t)});class Ps extends zt{_parse(e){const{ctx:t}=this._processInputParams(e);let s=t.data;return t.parsedType===_t.undefined&&(s=this._def.defaultValue()),this._def.innerType._parse({data:s,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}Ps.create=(e,t)=>new Ps({innerType:e,typeName:Bs.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...Bt(t)});class Rs extends zt{_parse(e){const{ctx:t}=this._processInputParams(e),s={...t,common:{...t.common,issues:[]}},r=this._def.innerType._parse({data:s.data,path:s.path,parent:{...s}});return Dt(r)?r.then((e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new kt(s.common.issues)},input:s.data})}))):{status:"valid",value:"valid"===r.status?r.value:this._def.catchValue({get error(){return new kt(s.common.issues)},input:s.data})}}removeCatch(){return this._def.innerType}}Rs.create=(e,t)=>new Rs({innerType:e,typeName:Bs.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...Bt(t)});class Ls extends zt{_parse(e){if(this._getType(e)!==_t.nan){const t=this._getOrReturnCtx(e);return Ct(t,{code:bt.invalid_type,expected:_t.nan,received:t.parsedType}),Tt}return{status:"valid",value:e.data}}}Ls.create=e=>new Ls({typeName:Bs.ZodNaN,...Bt(e)});class $s extends zt{_parse(e){const{ctx:t}=this._processInputParams(e),s=t.data;return this._def.type._parse({data:s,path:t.path,parent:t})}unwrap(){return this._def.type}}class Ms extends zt{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.common.async){return(async()=>{const e=await this._def.in._parseAsync({data:s.data,path:s.path,parent:s});return"aborted"===e.status?Tt:"dirty"===e.status?(t.dirty(),Vt(e.value)):this._def.out._parseAsync({data:e.value,path:s.path,parent:s})})()}{const e=this._def.in._parseSync({data:s.data,path:s.path,parent:s});return"aborted"===e.status?Tt:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:s.path,parent:s})}}static create(e,t){return new Ms({in:e,out:t,typeName:Bs.ZodPipeline})}}class Us extends zt{_parse(e){const t=this._def.innerType._parse(e),s=e=>(Zt(e)&&(e.value=Object.freeze(e.value)),e);return Dt(t)?t.then((e=>s(e))):s(t)}unwrap(){return this._def.innerType}}var Bs;Us.create=(e,t)=>new Us({innerType:e,typeName:Bs.ZodReadonly,...Bt(t)}),bs.lazycreate,function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(Bs||(Bs={}));const zs=is.create;ds.create,Ls.create,us.create,ls.create,cs.create,ms.create,hs.create,fs.create,ps.create,ys.create,vs.create,gs.create,_s.create;const Ws=bs.create;bs.strictCreate,ks.create,js.create,Ss.create,Ns.create,Cs.create,Os.create,Ts.create,As.create,Es.create,Fs.create,Zs.create,Ds.create,Is.create,Zs.createWithPreprocess,Ms.create;const Ks="X-CSRF-Token";async function qs(){try{const{data:e,error:t}=await p.functions.invoke("csrf-token");if(t)throw new Error("CSRF token generation failed");return e.token}catch(e){throw new Error("CSRF token generation failed")}}function Hs(){const[e,t]=a.useState(null),[s,r]=a.useState(!0);a.useEffect((()=>{(async()=>{try{const e=await qs();t(e)}catch(e){}finally{r(!1)}})()}),[]);const n=a.useCallback((async e=>await async function(e){try{const{data:t,error:s}=await p.functions.invoke("validate-csrf",{headers:{[Ks]:e}});return!s&&!0===t.valid}catch(t){return!1}}(e)),[]);return{csrfToken:e,isLoading:s,validateFormSubmission:n,refreshToken:a.useCallback((async()=>{try{r(!0);const e=await qs();return t(e),e}catch(e){return null}finally{r(!1)}}),[])}}const Ys=void 0,Gs=Ws({name:zs().min(2,{message:"Name must be at least 2 characters."}).max(100,{message:"Name must not exceed 100 characters."}).refine((e=>!/[<>]/.test(e)),{message:"Name contains invalid characters."}),email:zs().email({message:"Please enter a valid email address."}).max(255,{message:"Email address is too long."}).refine((e=>S(e)),{message:"Please enter a valid email format."}),company:zs().max(100,{message:"Company name must not exceed 100 characters."}).refine((e=>!e||!/[<>]/.test(e)),{message:"Company name contains invalid characters."}).optional(),message:zs().min(10,{message:"Message must be at least 10 characters."}).max(1e3,{message:"Message must not exceed 1000 characters."}).refine((e=>!/[<>]/.test(e)),{message:"Message contains invalid characters."}),_csrf:zs(),access_key:zs().optional(),subject:zs().optional(),from_name:zs().optional()});function Js(){const[e,t]=n.useState(!1),[s,r]=n.useState(null),{toast:i}=y(),{csrfToken:o,validateFormSubmission:d}=Hs(),u=function(e={}){const t=a.useRef(),s=a.useRef(),[r,n]=a.useState({isDirty:!1,isValidating:!1,isLoading:Ne(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,defaultValues:Ne(e.defaultValues)?void 0:e.defaultValues});t.current||(t.current={...tt(e),formState:r});const i=t.current.control;return i._options=e,pe({subject:i._subjects.state,next:e=>{me(e,i._proxyFormState,i._updateFormState,!0)&&n({...i._formState})}}),a.useEffect((()=>i._disableForm(e.disabled)),[i,e.disabled]),a.useEffect((()=>{if(i._proxyFormState.isDirty){const e=i._getDirty();e!==r.isDirty&&i._subjects.state.next({isDirty:e})}}),[i,r.isDirty]),a.useEffect((()=>{e.values&&!Ue(e.values,s.current)?(i._reset(e.values,i._options.resetOptions),s.current=e.values,n((e=>({...e})))):i._resetDefaultValues()}),[e.values,i]),a.useEffect((()=>{e.errors&&i._setErrors(e.errors)}),[e.errors,i]),a.useEffect((()=>{i._state.mount||(i._updateValid(),i._state.mount=!0),i._state.watch&&(i._state.watch=!1,i._subjects.state.next({...i._formState})),i._removeUnmounted()})),a.useEffect((()=>{e.shouldUnregister&&i._subjects.values.next({values:i._getWatch()})}),[e.shouldUnregister,i]),a.useEffect((()=>{t.current&&(t.current.watch=t.current.watch.bind({}))}),[r]),t.current.formState=le(r,i),t.current}({resolver:gt(Gs),defaultValues:{name:"",email:"",company:"",message:"",_csrf:o,access_key:Ys,subject:"New message from BlackVeil website",from_name:"BlackVeil Contact Form"}});n.useEffect((()=>{u.setValue("_csrf",o)}),[o,u]);return{form:u,isSubmitting:e,formError:s,onSubmit:u.handleSubmit((async e=>{t(!0),r(null);try{if(!d(e._csrf))throw new Error("Security validation failed. Please refresh the page and try again.");N(e.name),N(e.email),e.company&&N(e.company),N(e.message);throw new Error("Web3Forms configuration is missing. Please check environment variables.")}catch(s){let e="An unexpected error occurred. Please try again later.";s instanceof Error&&(s.message,e=s.message.includes("Security validation failed")?"Security check failed. Please refresh the page and try again.":s.message.includes("Web3Forms configuration is missing")?"Form configuration error. Please contact support.":s.message.includes("Failed to submit the form")?s.message||"Failed to send message. Please check your details and try again.":!1===navigator.onLine?"Network connection error. Please check your internet connection.":"There was a problem sending your message. Please try again."),r(e),i({title:"Submission Failed",description:e,variant:"destructive"})}finally{t(!1)}}))}}const Xs=({form:t,onEasterEggTrigger:s})=>{const r=e=>"speculative execution"===e.trim().toLowerCase();return e.jsxs(e.Fragment,{children:[e.jsx("input",{type:"hidden",name:"_csrf",...t.register("_csrf")}),e.jsx("input",{type:"hidden",name:"access_key",...t.register("access_key")}),e.jsx("input",{type:"hidden",name:"subject",...t.register("subject")}),e.jsx("input",{type:"hidden",name:"from_name",...t.register("from_name")}),e.jsx("div",{className:"hidden",children:e.jsx("input",{type:"checkbox",name:"botcheck",id:"botcheck",className:"hidden"})}),e.jsx(at,{control:t.control,name:"name",render:({field:t})=>{const{onBlur:a,value:n,...i}=t;return e.jsxs(ot,{children:[e.jsx(dt,{children:"Name"}),e.jsx(ut,{children:e.jsx(C,{placeholder:"Your name",className:"cyber-input",maxLength:100,value:n,onBlur:e=>{a&&a(),s&&r(e.target.value)&&s()},...i})}),e.jsx(lt,{})]})}}),e.jsx(at,{control:t.control,name:"email",render:({field:t})=>{const{onBlur:a,value:n,...i}=t;return e.jsxs(ot,{children:[e.jsx(dt,{children:"Email"}),e.jsx(ut,{children:e.jsx(C,{type:"email",placeholder:"<EMAIL>",className:"cyber-input",maxLength:255,value:n,onBlur:e=>{a&&a(),s&&r(e.target.value)&&s()},...i})}),e.jsx(lt,{})]})}}),e.jsx(at,{control:t.control,name:"company",render:({field:t})=>{const{onBlur:a,value:n,...i}=t;return e.jsxs(ot,{children:[e.jsxs(dt,{children:["Company ",e.jsx("span",{className:"text-gray-400",children:"(Optional)"})]}),e.jsx(ut,{children:e.jsx(C,{placeholder:"Your company",className:"cyber-input",maxLength:100,value:n,onBlur:e=>{a&&a(),s&&r(e.target.value)&&s()},...i})}),e.jsx(lt,{})]})}}),e.jsx(at,{control:t.control,name:"message",render:({field:t})=>{const{onBlur:a,value:n,...i}=t;return e.jsxs(ot,{children:[e.jsx(dt,{children:"Message"}),e.jsx(ut,{children:e.jsx(O,{placeholder:"How can we help you?",className:"cyber-input min-h-32",maxLength:1e3,value:n,onBlur:e=>{a&&a(),s&&r(e.target.value)&&s()},...i})}),e.jsx(lt,{})]})}})]})},Qs=()=>{const{form:t,isSubmitting:s,formError:r,onSubmit:u}=Js(),[l,c]=n.useState(!1),[m,h]=n.useState(!1),f=n.useRef(null),p=()=>{m&&h(!1)};return a.useEffect((()=>()=>{f.current&&clearTimeout(f.current)}),[]),e.jsxs("div",{className:"cyber-card relative overflow-visible",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Send a Message"}),r&&e.jsxs(v,{variant:"destructive",className:"mb-6",children:[e.jsx(i,{className:"h-4 w-4"}),e.jsx(g,{children:"Error"}),e.jsx(_,{children:r})]}),e.jsx(st,{...t,children:e.jsxs("form",{onSubmit:u,className:"space-y-4 "+(l?"spectre-glitch":""),autoComplete:"off",onFocus:p,onInput:p,children:[e.jsx(Xs,{form:t,onEasterEggTrigger:()=>{l||m||(c(!0),f.current=setTimeout((()=>{c(!1),h(!0)}),400))}}),e.jsx(x,{type:"submit",className:"cyber-button w-full flex items-center justify-center",disabled:s,children:s?e.jsxs(e.Fragment,{children:[e.jsx(o,{className:"h-4 w-4 mr-2 animate-spin"}),"Sending..."]}):e.jsxs(e.Fragment,{children:[e.jsx(d,{className:"h-4 w-4 mr-2"}),"Schedule Your Free Consultation"]})})]})}),m&&e.jsxs("div",{className:"spectre-tooltip",children:["Careful, your secrets might leak!"," ",e.jsx("a",{href:"https://example.com/spectre-meltdown-writeup",target:"_blank",rel:"noopener noreferrer",className:"underline text-blue-400",children:"Learn more"})]}),l&&e.jsx("div",{className:"spectre-glitch-overlay pointer-events-none"})]})},er=()=>e.jsxs("div",{children:[e.jsx(b,{title:"Contact Us | BlackVeil",description:"Let's Strengthen Your Cybersecurity Together—Connect With Our Experts Now",canonicalUrl:"https://blackveil.co.nz/contact",keywords:"contact, cybersecurity consultation, security experts, New Zealand, email security"}),e.jsx(k,{items:[{name:"Home",url:"https://blackveil.co.nz/"},{name:"Contact",url:"https://blackveil.co.nz/contact"}]}),e.jsx(T,{}),e.jsxs(m,{className:"pb-6 xs:pb-8 sm:pb-10",children:[e.jsx("div",{className:"max-w-4xl mx-auto mb-12 p-5 xs:p-6 sm:p-8 rounded-lg border border-green-muted/30 bg-gradient-to-br from-black-soft to-green-dark/10",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-6 items-center",children:[e.jsxs("div",{className:"md:w-3/4",children:[e.jsx("h2",{className:"text-xl xs:text-2xl font-bold mb-3 cyber-glow-text",children:"Cyber Incident Response Guide"}),e.jsx("p",{className:"text-white/80 mb-4",children:"Download our comprehensive guide to help your business prepare for and respond to cyber security incidents effectively."}),e.jsxs("div",{className:"flex flex-wrap gap-3",children:[e.jsx(f,{variant:"contact",layout:"horizontal",showDmarc:!1,showEmergency:!1}),e.jsxs("a",{href:"https://alexanderpr.co.nz/blog/contribution-emergency-cyber-security-response-guide-for-businesses",target:"_blank",rel:"noopener noreferrer",className:"text-white/80 hover:text-green-bright flex items-center transition-colors px-4 py-2",children:[e.jsx("span",{children:"As featured on Alexander PR"}),e.jsx(u,{className:"ml-2 w-4 h-4"})]})]})]}),e.jsx("div",{className:"md:w-1/4 flex justify-center",children:e.jsx("div",{className:"w-20 h-20 sm:w-24 sm:h-24 bg-black-soft rounded-full flex items-center justify-center border border-green-muted/30",children:e.jsx(l,{className:"w-10 h-10 sm:w-12 sm:h-12 text-green-bright"})})})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 xs:gap-10 sm:gap-12 max-w-5xl mx-auto",children:[e.jsx(V,{}),e.jsx(Qs,{})]})]})]});export{er as default};
