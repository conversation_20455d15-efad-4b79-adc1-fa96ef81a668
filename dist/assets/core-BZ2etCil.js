import{r as t,j as e,d as n,G as s,S as i,Z as r,U as o,R as a,L as l,E as c,C as u,A as h,e as d,f as p,g as m}from"./vendor-TvU2VNGP.js";const f=t.createContext({});function g(e){const n=t.useRef(null);return null===n.current&&(n.current=e()),n.current}const y="undefined"!=typeof window,v=y?t.useLayoutEffect:t.useEffect,x=t.createContext(null),b=t.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class w extends t.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const t=e.offsetParent,n=t instanceof HTMLElement&&t.offsetWidth||0,s=this.props.sizeRef.current;s.height=e.offsetHeight||0,s.width=e.offsetWidth||0,s.top=e.offsetTop,s.left=e.offsetLeft,s.right=n-s.width-s.left}return null}componentDidUpdate(){}render(){return this.props.children}}function T({children:n,isPresent:s,anchorX:i}){const r=t.useId(),o=t.useRef(null),a=t.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=t.useContext(b);return t.useInsertionEffect((()=>{const{width:t,height:e,top:n,left:c,right:u}=a.current;if(s||!o.current||!t||!e)return;const h="left"===i?`left: ${c}`:`right: ${u}`;o.current.dataset.motionPopId=r;const d=document.createElement("style");return l&&(d.nonce=l),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`\n          [data-motion-pop-id="${r}"] {\n            position: absolute !important;\n            width: ${t}px !important;\n            height: ${e}px !important;\n            ${h}px !important;\n            top: ${n}px !important;\n          }\n        `),()=>{document.head.removeChild(d)}}),[s]),e.jsx(w,{isPresent:s,childRef:o,sizeRef:a,children:t.cloneElement(n,{ref:o})})}const S=({children:n,initial:s,isPresent:i,onExitComplete:r,custom:o,presenceAffectsLayout:a,mode:l,anchorX:c})=>{const u=g(P),h=t.useId();let d=!0,p=t.useMemo((()=>(d=!1,{id:h,initial:s,isPresent:i,custom:o,onExitComplete:t=>{u.set(t,!0);for(const e of u.values())if(!e)return;r&&r()},register:t=>(u.set(t,!1),()=>u.delete(t))})),[i,u,r]);return a&&d&&(p={...p}),t.useMemo((()=>{u.forEach(((t,e)=>u.set(e,!1)))}),[i]),t.useEffect((()=>{!i&&!u.size&&r&&r()}),[i]),"popLayout"===l&&(n=e.jsx(T,{isPresent:i,anchorX:c,children:n})),e.jsx(x.Provider,{value:p,children:n})};function P(){return new Map}function k(e=!0){const n=t.useContext(x);if(null===n)return[!0,null];const{isPresent:s,onExitComplete:i,register:r}=n,o=t.useId();t.useEffect((()=>{if(e)return r(o)}),[e]);const a=t.useCallback((()=>e&&i&&i(o)),[o,i,e]);return!s&&i?[!1,a]:[!0]}const E=t=>t.key||"";function C(e){const n=[];return t.Children.forEach(e,(e=>{t.isValidElement(e)&&n.push(e)})),n}const M=({children:n,custom:s,initial:i=!0,onExitComplete:r,presenceAffectsLayout:o=!0,mode:a="sync",propagate:l=!1,anchorX:c="left"})=>{const[u,h]=k(l),d=t.useMemo((()=>C(n)),[n]),p=l&&!u?[]:d.map(E),m=t.useRef(!0),y=t.useRef(d),x=g((()=>new Map)),[b,w]=t.useState(d),[T,P]=t.useState(d);v((()=>{m.current=!1,y.current=d;for(let t=0;t<T.length;t++){const e=E(T[t]);p.includes(e)?x.delete(e):!0!==x.get(e)&&x.set(e,!1)}}),[T,p.length,p.join("-")]);const M=[];if(d!==b){let t=[...d];for(let e=0;e<T.length;e++){const n=T[e],s=E(n);p.includes(s)||(t.splice(e,0,n),M.push(n))}return"wait"===a&&M.length&&(t=M),P(C(t)),w(d),null}const{forceRender:j}=t.useContext(f);return e.jsx(e.Fragment,{children:T.map((t=>{const n=E(t),f=!(l&&!u)&&(d===T||p.includes(n));return e.jsx(S,{isPresent:f,initial:!(m.current&&!i)&&void 0,custom:s,presenceAffectsLayout:o,mode:a,onExitComplete:f?void 0:()=>{if(!x.has(n))return;x.set(n,!0);let t=!0;x.forEach((e=>{e||(t=!1)})),t&&(j?.(),P(y.current),l&&h?.(),r&&r())},anchorX:c,children:t},n)}))})};function j(t,e){-1===t.indexOf(e)&&t.push(e)}function A(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const V=(t,e,n)=>n>e?e:n<t?t:n;const D={},R=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),L=t=>/^0[^.\s]+$/u.test(t);function N(t){let e;return()=>(void 0===e&&(e=t()),e)}const B=t=>t,F=(t,e)=>n=>e(t(n)),O=(...t)=>t.reduce(F),I=(t,e,n)=>{const s=e-t;return 0===s?1:(n-t)/s};class W{constructor(){this.subscriptions=[]}add(t){return j(this.subscriptions,t),()=>A(this.subscriptions,t)}notify(t,e,n){const s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,n);else for(let i=0;i<s;i++){const s=this.subscriptions[i];s&&s(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const z=t=>1e3*t,U=t=>t/1e3;function $(t,e){return e?t*(1e3/e):0}const H=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function X(t,e,n,s){if(t===e&&n===s)return B;const i=e=>function(t,e,n,s,i){let r,o,a=0;do{o=e+(n-e)/2,r=H(o,s,i)-t,r>0?n=o:e=o}while(Math.abs(r)>1e-7&&++a<12);return o}(e,0,1,t,n);return t=>0===t||1===t?t:H(i(t),e,s)}const Y=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,G=t=>e=>1-t(1-e),K=X(.33,1.53,.69,.99),q=G(K),_=Y(q),Z=t=>(t*=2)<1?.5*q(t):.5*(2-Math.pow(2,-10*(t-1))),Q=t=>1-Math.sin(Math.acos(t)),J=G(Q),tt=Y(Q),et=X(.42,0,1,1),nt=X(0,0,.58,1),st=X(.42,0,.58,1),it=t=>Array.isArray(t)&&"number"==typeof t[0],rt={linear:B,easeIn:et,easeInOut:st,easeOut:nt,circIn:Q,circInOut:tt,circOut:J,backIn:q,backInOut:_,backOut:K,anticipate:Z},ot=t=>{if(it(t)){t.length;const[e,n,s,i]=t;return X(e,n,s,i)}return"string"==typeof t?rt[t]:t},at=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],lt={value:null,addProjectionMetrics:null};function ct(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},r=()=>n=!0,o=at.reduce(((t,n)=>(t[n]=function(t,e){let n=new Set,s=new Set,i=!1,r=!1;const o=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function c(e){o.has(e)&&(u.schedule(e),t()),l++,e(a)}const u={schedule:(t,e=!1,r=!1)=>{const a=r&&i?n:s;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{s.delete(t),o.delete(t)},process:t=>{a=t,i?r=!0:(i=!0,[n,s]=[s,n],n.forEach(c),e&&lt.value&&lt.value.frameloop[e].push(l),l=0,n.clear(),i=!1,r&&(r=!1,u.process(t)))}};return u}(r,e?n:void 0),t)),{}),{setup:a,read:l,resolveKeyframes:c,preUpdate:u,update:h,preRender:d,render:p,postRender:m}=o,f=()=>{const r=D.useManualTiming?i.timestamp:performance.now();n=!1,D.useManualTiming||(i.delta=s?1e3/60:Math.max(Math.min(r-i.timestamp,40),1)),i.timestamp=r,i.isProcessing=!0,a.process(i),l.process(i),c.process(i),u.process(i),h.process(i),d.process(i),p.process(i),m.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(f))};return{schedule:at.reduce(((e,r)=>{const a=o[r];return e[r]=(e,r=!1,o=!1)=>(n||(n=!0,s=!0,i.isProcessing||t(f)),a.schedule(e,r,o)),e}),{}),cancel:t=>{for(let e=0;e<at.length;e++)o[at[e]].cancel(t)},state:i,steps:o}}const{schedule:ut,cancel:ht,state:dt,steps:pt}=ct("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:B,!0);let mt;function ft(){mt=void 0}const gt={now:()=>(void 0===mt&&gt.set(dt.isProcessing||D.useManualTiming?dt.timestamp:performance.now()),mt),set:t=>{mt=t,queueMicrotask(ft)}},yt=t=>e=>"string"==typeof e&&e.startsWith(t),vt=yt("--"),xt=yt("var(--"),bt=t=>!!xt(t)&&wt.test(t.split("/*")[0].trim()),wt=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Tt={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},St={...Tt,transform:t=>V(0,1,t)},Pt={...Tt,default:1},kt=t=>Math.round(1e5*t)/1e5,Et=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const Ct=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Mt=(t,e)=>n=>Boolean("string"==typeof n&&Ct.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),jt=(t,e,n)=>s=>{if("string"!=typeof s)return s;const[i,r,o,a]=s.match(Et);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},At={...Tt,transform:t=>Math.round((t=>V(0,255,t))(t))},Vt={test:Mt("rgb","red"),parse:jt("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+At.transform(t)+", "+At.transform(e)+", "+At.transform(n)+", "+kt(St.transform(s))+")"};const Dt={test:Mt("#"),parse:function(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}},transform:Vt.transform},Rt=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),Lt=Rt("deg"),Nt=Rt("%"),Bt=Rt("px"),Ft=Rt("vh"),Ot=Rt("vw"),It=(()=>({...Nt,parse:t=>Nt.parse(t)/100,transform:t=>Nt.transform(100*t)}))(),Wt={test:Mt("hsl","hue"),parse:jt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+Nt.transform(kt(e))+", "+Nt.transform(kt(n))+", "+kt(St.transform(s))+")"},zt={test:t=>Vt.test(t)||Dt.test(t)||Wt.test(t),parse:t=>Vt.test(t)?Vt.parse(t):Wt.test(t)?Wt.parse(t):Dt.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?Vt.transform(t):Wt.transform(t)},Ut=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const $t="number",Ht="color",Xt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Yt(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let r=0;const o=e.replace(Xt,(t=>(zt.test(t)?(s.color.push(r),i.push(Ht),n.push(zt.parse(t))):t.startsWith("var(")?(s.var.push(r),i.push("var"),n.push(t)):(s.number.push(r),i.push($t),n.push(parseFloat(t))),++r,"${}"))).split("${}");return{values:n,split:o,indexes:s,types:i}}function Gt(t){return Yt(t).values}function Kt(t){const{split:e,types:n}=Yt(t),s=e.length;return t=>{let i="";for(let r=0;r<s;r++)if(i+=e[r],void 0!==t[r]){const e=n[r];i+=e===$t?kt(t[r]):e===Ht?zt.transform(t[r]):t[r]}return i}}const qt=t=>"number"==typeof t?0:t;const _t={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Et)?.length||0)+(t.match(Ut)?.length||0)>0},parse:Gt,createTransformer:Kt,getAnimatableNone:function(t){const e=Gt(t);return Kt(t)(e.map(qt))}};function Zt(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Qt(t,e){return n=>n>0?e:t}const Jt=(t,e,n)=>t+(e-t)*n,te=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},ee=[Dt,Vt,Wt];function ne(t){const e=(n=t,ee.find((t=>t.test(n))));var n;if(!Boolean(e))return!1;let s=e.parse(t);return e===Wt&&(s=function({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,n/=100;let i=0,r=0,o=0;if(e/=100){const s=n<.5?n*(1+e):n+e-n*e,a=2*n-s;i=Zt(a,s,t+1/3),r=Zt(a,s,t),o=Zt(a,s,t-1/3)}else i=r=o=n;return{red:Math.round(255*i),green:Math.round(255*r),blue:Math.round(255*o),alpha:s}}(s)),s}const se=(t,e)=>{const n=ne(t),s=ne(e);if(!n||!s)return Qt(t,e);const i={...n};return t=>(i.red=te(n.red,s.red,t),i.green=te(n.green,s.green,t),i.blue=te(n.blue,s.blue,t),i.alpha=Jt(n.alpha,s.alpha,t),Vt.transform(i))},ie=new Set(["none","hidden"]);function re(t,e){return n=>Jt(t,e,n)}function oe(t){return"number"==typeof t?re:"string"==typeof t?bt(t)?Qt:zt.test(t)?se:ce:Array.isArray(t)?ae:"object"==typeof t?zt.test(t)?se:le:Qt}function ae(t,e){const n=[...t],s=n.length,i=t.map(((t,n)=>oe(t)(t,e[n])));return t=>{for(let e=0;e<s;e++)n[e]=i[e](t);return n}}function le(t,e){const n={...t,...e},s={};for(const i in n)void 0!==t[i]&&void 0!==e[i]&&(s[i]=oe(t[i])(t[i],e[i]));return t=>{for(const e in s)n[e]=s[e](t);return n}}const ce=(t,e)=>{const n=_t.createTransformer(e),s=Yt(t),i=Yt(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?ie.has(t)&&!i.values.length||ie.has(e)&&!s.values.length?function(t,e){return ie.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):O(ae(function(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const r=e.types[i],o=t.indexes[r][s[r]],a=t.values[o]??0;n[i]=a,s[r]++}return n}(s,i),i.values),n):Qt(t,e)};function ue(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Jt(t,e,n);return oe(t)(t,e)}const he=t=>{const e=({timestamp:e})=>t(e);return{start:()=>ut.update(e,!0),stop:()=>ht(e),now:()=>dt.isProcessing?dt.timestamp:gt.now()}},de=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let r=0;r<i;r++)s+=t(r/(i-1))+", ";return`linear(${s.substring(0,s.length-2)})`},pe=2e4;function me(t){let e=0;let n=t.next(e);for(;!n.done&&e<pe;)e+=50,n=t.next(e);return e>=pe?1/0:e}function fe(t,e,n){const s=Math.max(e-5,0);return $(n-t(s),e-s)}const ge=100,ye=10,ve=1,xe=0,be=800,we=.3,Te=.3,Se={granular:.01,default:2},Pe={granular:.005,default:.5},ke=.01,Ee=10,Ce=.05,Me=1,je=.001;function Ae({duration:t=be,bounce:e=we,velocity:n=xe,mass:s=ve}){let i,r,o=1-e;o=V(Ce,Me,o),t=V(ke,Ee,U(t)),o<1?(i=e=>{const s=e*o,i=s*t,r=s-n,a=De(e,o),l=Math.exp(-i);return je-r/a*l},r=e=>{const s=e*o*t,r=s*n+n,a=Math.pow(o,2)*Math.pow(e,2)*t,l=Math.exp(-s),c=De(Math.pow(e,2),o);return(-i(e)+je>0?-1:1)*((r-a)*l)/c}):(i=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,r=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let s=n;for(let i=1;i<Ve;i++)s-=t(s)/e(s);return s}(i,r,5/t);if(t=z(t),isNaN(a))return{stiffness:ge,damping:ye,duration:t};{const e=Math.pow(a,2)*s;return{stiffness:e,damping:2*o*Math.sqrt(s*e),duration:t}}}const Ve=12;function De(t,e){return t*Math.sqrt(1-e*e)}const Re=["duration","bounce"],Le=["stiffness","damping","mass"];function Ne(t,e){return e.some((e=>void 0!==t[e]))}function Be(t=Te,e=we){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:i}=n;const r=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],a={done:!1,value:r},{stiffness:l,damping:c,mass:u,duration:h,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:xe,stiffness:ge,damping:ye,mass:ve,isResolvedFromDuration:!1,...t};if(!Ne(t,Le)&&Ne(t,Re))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(1.2*n),i=s*s,r=2*V(.05,1,1-(t.bounce||0))*Math.sqrt(i);e={...e,mass:ve,stiffness:i,damping:r}}else{const n=Ae(t);e={...e,...n,mass:ve},e.isResolvedFromDuration=!0}return e}({...n,velocity:-U(n.velocity||0)}),m=d||0,f=c/(2*Math.sqrt(l*u)),g=o-r,y=U(Math.sqrt(l/u)),v=Math.abs(g)<5;let x;if(s||(s=v?Se.granular:Se.default),i||(i=v?Pe.granular:Pe.default),f<1){const t=De(y,f);x=e=>{const n=Math.exp(-f*y*e);return o-n*((m+f*y*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}}else if(1===f)x=t=>o-Math.exp(-y*t)*(g+(m+y*g)*t);else{const t=y*Math.sqrt(f*f-1);x=e=>{const n=Math.exp(-f*y*e),s=Math.min(t*e,300);return o-n*((m+f*y*g)*Math.sinh(s)+t*g*Math.cosh(s))/t}}const b={calculatedDuration:p&&h||null,next:t=>{const e=x(t);if(p)a.done=t>=h;else{let n=0===t?m:0;f<1&&(n=0===t?z(m):fe(x,t,e));const r=Math.abs(n)<=s,l=Math.abs(o-e)<=i;a.done=r&&l}return a.value=a.done?o:e,a},toString:()=>{const t=Math.min(me(b),pe),e=de((e=>b.next(t*e).value),t,30);return t+"ms "+e},toTransition:()=>{}};return b}function Fe({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:c=.5,restSpeed:u}){const h=t[0],d={done:!1,value:h},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=h+m,g=void 0===o?f:o(f);g!==f&&(m=g-h);const y=t=>-m*Math.exp(-t/s),v=t=>g+y(t),x=t=>{const e=y(t),n=v(t);d.done=Math.abs(e)<=c,d.value=d.done?g:n};let b,w;const T=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(b=t,w=Be({keyframes:[d.value,p(d.value)],velocity:fe(v,t,d.value),damping:i,stiffness:r,restDelta:c,restSpeed:u}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return w||void 0!==b||(e=!0,x(t),T(t)),void 0!==b&&t>=b?w.next(t-b):(!e&&x(t),d)}}}function Oe(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const r=t.length;if(e.length,1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];const o=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,n){const s=[],i=n||D.mix||ue,r=t.length-1;for(let o=0;o<r;o++){let n=i(t[o],t[o+1]);if(e){const t=Array.isArray(e)?e[o]||B:e;n=O(t,n)}s.push(n)}return s}(e,s,i),l=a.length,c=n=>{if(o&&n<t[0])return e[0];let s=0;if(l>1)for(;s<t.length-2&&!(n<t[s+1]);s++);const i=I(t[s],t[s+1],n);return a[s](i)};return n?e=>c(V(t[0],t[r-1],e)):c}function Ie(t){const e=[0];return function(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=I(0,e,s);t.push(Jt(n,1,i))}}(e,t.length-1),e}function We({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=(t=>Array.isArray(t)&&"number"!=typeof t[0])(s)?s.map(ot):ot(s),r={done:!1,value:e[0]},o=function(t,e){return t.map((t=>t*e))}(n&&n.length===e.length?n:Ie(e),t),a=Oe(o,e,{ease:Array.isArray(i)?i:(l=e,c=i,l.map((()=>c||st)).splice(0,l.length-1))});var l,c;return{calculatedDuration:t,next:e=>(r.value=a(e),r.done=e>=t,r)}}Be.applyToOptions=t=>{const e=function(t,e=100,n){const s=n({...t,keyframes:[0,e]}),i=Math.min(me(s),pe);return{type:"keyframes",ease:t=>s.next(i*t).value/e,duration:U(i)}}(t,100,Be);return t.ease=e.ease,t.duration=z(e.duration),t.type="keyframes",t};const ze=t=>null!==t;function Ue(t,{repeat:e,repeatType:n="loop"},s,i=1){const r=t.filter(ze),o=i<0||e&&"loop"!==n&&e%2==1?0:r.length-1;return o&&void 0!==s?s:r[o]}const $e={decay:Fe,inertia:Fe,tween:We,keyframes:We,spring:Be};function He(t){"string"==typeof t.type&&(t.type=$e[t.type])}class Xe{constructor(){this.count=0,this.updateFinished()}get finished(){return this._finished}updateFinished(){this.count++,this._finished=new Promise((t=>{this.resolve=t}))}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const Ye=t=>t/100;class Ge extends Xe{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;if(t&&t.updatedAt!==gt.now()&&this.tick(gt.now()),this.isStopped=!0,"idle"===this.state)return;this.teardown();const{onStop:e}=this.options;e&&e()},this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;He(t);const{type:e=We,repeat:n=0,repeatDelay:s=0,repeatType:i,velocity:r=0}=t;let{keyframes:o}=t;const a=e||We;a!==We&&"number"!=typeof o[0]&&(this.mixKeyframes=O(Ye,ue(o[0],o[1])),o=[0,100]);const l=a({...t,keyframes:o});"mirror"===i&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-r})),null===l.calculatedDuration&&(l.calculatedDuration=me(l));const{calculatedDuration:c}=l;this.calculatedDuration=c,this.resolvedDuration=c+s,this.totalDuration=this.resolvedDuration*(n+1)-s,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:n,totalDuration:s,mixKeyframes:i,mirroredGenerator:r,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);const{delay:l=0,keyframes:c,repeat:u,repeatType:h,repeatDelay:d,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>s;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let v=this.currentTime,x=n;if(u){const t=Math.min(this.currentTime,s)/o;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,u+1);Boolean(e%2)&&("reverse"===h?(n=1-n,d&&(n-=d/o)):"mirror"===h&&(x=r)),v=V(0,1,n)*o}const b=y?{done:!1,value:c[0]}:x.next(v);i&&(b.value=i(b.value));let{done:w}=b;y||null===a||(w=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);const T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return T&&p!==Fe&&(b.value=Ue(c,this.options,f,this.speed)),m&&m(b.value),T&&this.finish(),b}then(t,e){return this.finished.then(t,e)}get duration(){return U(this.calculatedDuration)}get time(){return U(this.currentTime)}set time(t){t=z(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(gt.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=U(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=he,onPlay:e,startTime:n}=this.options;this.driver||(this.driver=t((t=>this.tick(t)))),e&&e();const s=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=s):null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime||(this.startTime=n??s),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(gt.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown()}teardown(){this.notifyFinished(),this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),t.observe(this)}}const Ke=t=>180*t/Math.PI,qe=t=>{const e=Ke(Math.atan2(t[1],t[0]));return Ze(e)},_e={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:qe,rotateZ:qe,skewX:t=>Ke(Math.atan(t[1])),skewY:t=>Ke(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Ze=t=>((t%=360)<0&&(t+=360),t),Qe=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Je=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),tn={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Qe,scaleY:Je,scale:t=>(Qe(t)+Je(t))/2,rotateX:t=>Ze(Ke(Math.atan2(t[6],t[5]))),rotateY:t=>Ze(Ke(Math.atan2(-t[2],t[0]))),rotateZ:qe,rotate:qe,skewX:t=>Ke(Math.atan(t[4])),skewY:t=>Ke(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function en(t){return t.includes("scale")?1:0}function nn(t,e){if(!t||"none"===t)return en(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=tn,i=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=_e,i=e}if(!i)return en(e);const r=s[e],o=i[1].split(",").map(sn);return"function"==typeof r?r(o):o[r]}function sn(t){return parseFloat(t.trim())}const rn=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],on=(()=>new Set(rn))(),an=t=>t===Tt||t===Bt,ln=new Set(["x","y","z"]),cn=rn.filter((t=>!ln.has(t)));const un={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>nn(e,"x"),y:(t,{transform:e})=>nn(e,"y")};un.translateX=un.x,un.translateY=un.y;const hn=new Set;let dn=!1,pn=!1,mn=!1;function fn(){if(pn){const t=Array.from(hn).filter((t=>t.needsMeasurement)),e=new Set(t.map((t=>t.element))),n=new Map;e.forEach((t=>{const e=function(t){const e=[];return cn.forEach((n=>{const s=t.getValue(n);void 0!==s&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))})),e}(t);e.length&&(n.set(t,e),t.render())})),t.forEach((t=>t.measureInitialState())),e.forEach((t=>{t.render();const e=n.get(t);e&&e.forEach((([e,n])=>{t.getValue(e)?.set(n)}))})),t.forEach((t=>t.measureEndState())),t.forEach((t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)}))}pn=!1,dn=!1,hn.forEach((t=>t.complete(mn))),hn.clear()}function gn(){hn.forEach((t=>{t.readKeyframes(),t.needsMeasurement&&(pn=!0)}))}class yn{constructor(t,e,n,s,i,r=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=s,this.element=i,this.isAsync=r}scheduleResolve(){this.isScheduled=!0,this.isAsync?(hn.add(this),dn||(dn=!0,ut.read(gn),ut.resolveKeyframes(fn))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:s}=this;if(null===t[0]){const i=s?.get(),r=t[t.length-1];if(void 0!==i)t[0]=i;else if(n&&e){const s=n.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===i&&s.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),hn.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,hn.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const vn=N((()=>void 0!==window.ScrollTimeline)),xn={};function bn(t,e){const n=N(t);return()=>xn[e]??n()}const wn=bn((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}),"linearEasing"),Tn=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,Sn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Tn([0,.65,.55,1]),circOut:Tn([.55,0,1,.45]),backIn:Tn([.31,.01,.66,-.59]),backOut:Tn([.33,1.53,.69,.99])};function Pn(t,e){return t?"function"==typeof t?wn()?de(t,e):"ease-out":it(t)?Tn(t):Array.isArray(t)?t.map((t=>Pn(t,e)||Sn.easeOut)):Sn[t]:void 0}function kn(t,e,n,{delay:s=0,duration:i=300,repeat:r=0,repeatType:o="loop",ease:a="easeOut",times:l}={},c=void 0){const u={[e]:n};l&&(u.offset=l);const h=Pn(a,i);Array.isArray(h)&&(u.easing=h);const d={delay:s,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"};c&&(d.pseudoElement=c);return t.animate(u,d)}function En(t){return"function"==typeof t&&"applyToOptions"in t}class Cn extends Xe{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:s,pseudoElement:i,allowFlatten:r=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=Boolean(i),this.allowFlatten=r,this.options=t,t.type;const l=function({type:t,...e}){return En(t)&&wn()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=kn(e,n,s,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){const t=Ue(s,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,n){(t=>t.startsWith("--"))(e)?t.style.setProperty(e,n):t.style[e]=n}(e,n,t),this.animation.cancel()}a?.(),this.notifyFinished()},this.animation.oncancel=()=>this.notifyFinished()}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return U(Number(t))}get time(){return U(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=z(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&vn()?(this.animation.timeline=t,B):e(this)}}const Mn={anticipate:Z,backInOut:_,circInOut:tt};function jn(t){"string"==typeof t.ease&&t.ease in Mn&&(t.ease=Mn[t.ease])}class An extends Cn{constructor(t){jn(t),He(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:s,element:i,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const o=new Ge({...r,autoplay:!1}),a=z(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}const Vn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!_t.test(t)&&"0"!==t||t.startsWith("url(")));const Dn=new Set(["opacity","clipPath","filter","transform"]),Rn=N((()=>Object.hasOwnProperty.call(Element.prototype,"animate")));class Ln extends Xe{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:r="loop",keyframes:o,name:a,motionValue:l,element:c,...u}){super(),this.stop=()=>{this._animation?(this._animation.stop(),this.stopTimeline?.()):this.keyframeResolver?.cancel()},this.createdAt=gt.now();const h={autoplay:t,delay:e,type:n,repeat:s,repeatDelay:i,repeatType:r,name:a,motionValue:l,element:c,...u},d=c?.KeyframeResolver||yn;this.keyframeResolver=new d(o,((t,e,n)=>this.onKeyframesResolved(t,e,h,!n)),a,l,c),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,n,s){this.keyframeResolver=void 0;const{name:i,type:r,velocity:o,delay:a,isHandoff:l,onUpdate:c}=n;this.resolvedAt=gt.now(),function(t,e,n,s){const i=t[0];if(null===i)return!1;if("display"===e||"visibility"===e)return!0;const r=t[t.length-1],o=Vn(i,e),a=Vn(r,e);return!(!o||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||En(n))&&s)}(t,i,r,o)||(!D.instantAnimations&&a||c?.(Ue(t,n,e)),t[0]=t[t.length-1],n.duration=0,n.repeat=0);const u={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},h=!l&&function(t){const{motionValue:e,name:n,repeatDelay:s,repeatType:i,damping:r,type:o}=t;if(!(e&&e.owner&&e.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return Rn()&&n&&Dn.has(n)&&("transform"!==n||!l)&&!a&&!s&&"mirror"!==i&&0!==r&&"inertia"!==o}(u)?new An({...u,element:u.motionValue.owner.current}):new Ge(u);h.finished.then((()=>this.notifyFinished())).catch(B),this.pendingTimeline&&(this.stopTimeline=h.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=h}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then((()=>{}))}get animation(){return this._animation||(mn=!0,gn(),fn(),mn=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this.animation.cancel()}}const Nn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Bn(t,e,n=1){const[s,i]=function(t){const e=Nn.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}(t);if(!s)return;const r=window.getComputedStyle(e).getPropertyValue(s);if(r){const t=r.trim();return R(t)?parseFloat(t):t}return bt(i)?Bn(i,e,n+1):i}function Fn(t,e){return t?.[e]??t?.default??t}const On=new Set(["width","height","top","left","right","bottom",...rn]),In=t=>e=>e.test(t),Wn=[Tt,Bt,Nt,Lt,Ot,Ft,{test:t=>"auto"===t,parse:t=>t}],zn=t=>Wn.find(In(t));const Un=new Set(["brightness","contrast","saturate","opacity"]);function $n(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[s]=n.match(Et)||[];if(!s)return t;const i=n.replace(s,"");let r=Un.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const Hn=/\b([a-z-]*)\(.*?\)/gu,Xn={..._t,getAnimatableNone:t=>{const e=t.match(Hn);return e?e.map($n).join(" "):t}},Yn={...Tt,transform:Math.round},Gn={borderWidth:Bt,borderTopWidth:Bt,borderRightWidth:Bt,borderBottomWidth:Bt,borderLeftWidth:Bt,borderRadius:Bt,radius:Bt,borderTopLeftRadius:Bt,borderTopRightRadius:Bt,borderBottomRightRadius:Bt,borderBottomLeftRadius:Bt,width:Bt,maxWidth:Bt,height:Bt,maxHeight:Bt,top:Bt,right:Bt,bottom:Bt,left:Bt,padding:Bt,paddingTop:Bt,paddingRight:Bt,paddingBottom:Bt,paddingLeft:Bt,margin:Bt,marginTop:Bt,marginRight:Bt,marginBottom:Bt,marginLeft:Bt,backgroundPositionX:Bt,backgroundPositionY:Bt,...{rotate:Lt,rotateX:Lt,rotateY:Lt,rotateZ:Lt,scale:Pt,scaleX:Pt,scaleY:Pt,scaleZ:Pt,skew:Lt,skewX:Lt,skewY:Lt,distance:Bt,translateX:Bt,translateY:Bt,translateZ:Bt,x:Bt,y:Bt,z:Bt,perspective:Bt,transformPerspective:Bt,opacity:St,originX:It,originY:It,originZ:Bt},zIndex:Yn,fillOpacity:St,strokeOpacity:St,numOctaves:Yn},Kn={...Gn,color:zt,backgroundColor:zt,outlineColor:zt,fill:zt,stroke:zt,borderColor:zt,borderTopColor:zt,borderRightColor:zt,borderBottomColor:zt,borderLeftColor:zt,filter:Xn,WebkitFilter:Xn},qn=t=>Kn[t];function _n(t,e){let n=qn(t);return n!==Xn&&(n=_t),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Zn=new Set(["auto","none","0"]);class Qn extends yn{constructor(t,e,n,s,i){super(t,e,n,s,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let a=0;a<t.length;a++){let n=t[a];if("string"==typeof n&&(n=n.trim(),bt(n))){const s=Bn(n,e.current);void 0!==s&&(t[a]=s),a===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!On.has(n)||2!==t.length)return;const[s,i]=t,r=zn(s),o=zn(i);if(r!==o)if(an(r)&&an(o))for(let a=0;a<t.length;a++){const e=t[a];"string"==typeof e&&(t[a]=parseFloat(e))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let i=0;i<t.length;i++)(null===t[i]||("number"==typeof(s=t[i])?0===s:null===s||"none"===s||"0"===s||L(s)))&&n.push(i);var s;n.length&&function(t,e,n){let s,i=0;for(;i<t.length&&!s;){const e=t[i];"string"==typeof e&&!Zn.has(e)&&Yt(e).values.length&&(s=t[i]),i++}if(s&&n)for(const r of e)t[r]=_n(n,s)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=un[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const s=e[e.length-1];void 0!==s&&t.getValue(n,s).jump(s,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);const i=n.length-1,r=n[i];n[i]=un[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach((([e,n])=>{t.getValue(e).set(n)})),this.resolveNoneKeyframes()}}function Jn(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){const e=document.querySelectorAll(t);return e?Array.from(e):[]}return Array.from(t)}const{schedule:ts,cancel:es}=ct(queueMicrotask,!1),ns={x:!1,y:!1};function ss(){return ns.x||ns.y}function is(t,e){const n=Jn(t),s=new AbortController;return[n,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function rs(t){return!("touch"===t.pointerType||ss())}const os=(t,e)=>!!e&&(t===e||os(t,e.parentElement)),as=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,ls=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const cs=new WeakSet;function us(t){return e=>{"Enter"===e.key&&t(e)}}function hs(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function ds(t){return as(t)&&!ss()}function ps(t,e,n={}){const[s,i,r]=is(t,n),o=t=>{const s=t.currentTarget;if(!ds(t)||cs.has(s))return;cs.add(s);const r=e(s,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),ds(t)&&cs.has(s)&&(cs.delete(s),"function"==typeof r&&r(t,{success:e}))},a=t=>{o(t,s===window||s===document||n.useGlobalTarget||os(s,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",l,i)};return s.forEach((t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",o,i),t instanceof HTMLElement&&(t.addEventListener("focus",(t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const s=us((()=>{if(cs.has(n))return;hs(n,"down");const t=us((()=>{hs(n,"up")}));n.addEventListener("keyup",t,e),n.addEventListener("blur",(()=>hs(n,"cancel")),e)}));n.addEventListener("keydown",s,e),n.addEventListener("blur",(()=>n.removeEventListener("keydown",s)),e)})(t,i))),e=t,ls.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))})),r}function ms(t,e){let n;const s=()=>{const{currentTime:s}=e,i=(null===s?0:s.value)/100;n!==i&&t(i),n=i};return ut.preUpdate(s,!0),()=>ht(s)}const fs={current:void 0};class gs{constructor(t,e={}){this.version="12.9.1",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=gt.now();this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change?.notify(this.current),e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=gt.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new W);const n=this.events[t].add(e);return"change"===t?()=>{n(),ut.read((()=>{this.events.change.getSize()||this.stop()}))}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return fs.current&&fs.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=gt.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return $(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise((e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ys(t,e){return new gs(t,e)}const vs=[...Wn,zt,_t],xs=(t,e)=>e&&"number"==typeof t?e.transform(t):t,bs=t.createContext({strict:!1}),ws={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Ts={};for(const sc in ws)Ts[sc]={isEnabled:t=>ws[sc].some((e=>!!t[e]))};const Ss=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ps(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||Ss.has(t)}let ks=t=>!Ps(t);try{(Es=require("@emotion/is-prop-valid").default)&&(ks=t=>t.startsWith("on")?!Ps(t):Es(t))}catch{}var Es;function Cs(t){if("undefined"==typeof Proxy)return t;const e=new Map;return new Proxy(((...e)=>t(...e)),{get:(n,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}const Ms=t.createContext({});function js(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function As(t){return"string"==typeof t||Array.isArray(t)}const Vs=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Ds=["initial",...Vs];function Rs(t){return js(t.animate)||Ds.some((e=>As(t[e])))}function Ls(t){return Boolean(Rs(t)||t.variants)}function Ns(e){const{initial:n,animate:s}=function(t,e){if(Rs(t)){const{initial:e,animate:n}=t;return{initial:!1===e||As(e)?e:void 0,animate:As(n)?n:void 0}}return!1!==t.inherit?e:{}}(e,t.useContext(Ms));return t.useMemo((()=>({initial:n,animate:s})),[Bs(n),Bs(s)])}function Bs(t){return Array.isArray(t)?t.join(" "):t}const Fs=Symbol.for("motionComponentSymbol");function Os(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function Is(e,n,s){return t.useCallback((t=>{t&&e.onMount&&e.onMount(t),n&&(t?n.mount(t):n.unmount()),s&&("function"==typeof s?s(t):Os(s)&&(s.current=t))}),[n])}const Ws=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),zs="data-"+Ws("framerAppearId"),Us=t.createContext({});function $s(e,n,s,i,r){const{visualElement:o}=t.useContext(Ms),a=t.useContext(bs),l=t.useContext(x),c=t.useContext(b).reducedMotion,u=t.useRef(null);i=i||a.renderer,!u.current&&i&&(u.current=i(e,{visualState:n,parent:o,props:s,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:c}));const h=u.current,d=t.useContext(Us);!h||h.projection||!r||"html"!==h.type&&"svg"!==h.type||function(t,e,n,s){const{layoutId:i,layout:r,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:c,layoutCrossfade:u}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Hs(t.parent)),t.projection.setOptions({layoutId:i,layout:r,alwaysMeasureLayout:Boolean(o)||a&&Os(a),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:s,crossfade:u,layoutScroll:l,layoutRoot:c})}(u.current,s,r,d);const p=t.useRef(!1);t.useInsertionEffect((()=>{h&&p.current&&h.update(s,l)}));const m=s[zs],f=t.useRef(Boolean(m)&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return v((()=>{h&&(p.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),ts.render(h.render),f.current&&h.animationState&&h.animationState.animateChanges())})),t.useEffect((()=>{h&&(!f.current&&h.animationState&&h.animationState.animateChanges(),f.current&&(queueMicrotask((()=>{window.MotionHandoffMarkAsComplete?.(m)})),f.current=!1))})),h}function Hs(t){if(t)return!1!==t.options.allowProjection?t.projection:Hs(t.parent)}function Xs({preloadedFeatures:n,createVisualElement:s,useRender:i,useVisualState:r,Component:o}){function a(n,a){let l;const c={...t.useContext(b),...n,layoutId:Ys(n)},{isStatic:u}=c,h=Ns(n),d=r(n,u);if(!u&&y){t.useContext(bs).strict;const e=function(t){const{drag:e,layout:n}=Ts;if(!e&&!n)return{};const s={...e,...n};return{MeasureLayout:e?.isEnabled(t)||n?.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(c);l=e.MeasureLayout,h.visualElement=$s(o,d,c,s,e.ProjectionNode)}return e.jsxs(Ms.Provider,{value:h,children:[l&&h.visualElement?e.jsx(l,{visualElement:h.visualElement,...c}):null,i(o,n,Is(d,h.visualElement,a),d,u,h.visualElement)]})}n&&function(t){for(const e in t)Ts[e]={...Ts[e],...t[e]}}(n),a.displayName=`motion.${"string"==typeof o?o:`create(${o.displayName??o.name??""})`}`;const l=t.forwardRef(a);return l[Fs]=o,l}function Ys({layoutId:e}){const n=t.useContext(f).id;return n&&void 0!==e?n+"-"+e:e}const Gs={};function Ks(t,{layout:e,layoutId:n}){return on.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!Gs[t]||"opacity"===t)}const qs=t=>Boolean(t&&t.getVelocity),_s={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Zs=rn.length;function Qs(t,e,n){const{style:s,vars:i,transformOrigin:r}=t;let o=!1,a=!1;for(const l in e){const t=e[l];if(on.has(l))o=!0;else if(vt(l))i[l]=t;else{const e=xs(t,Gn[l]);l.startsWith("origin")?(a=!0,r[l]=e):s[l]=e}}if(e.transform||(o||n?s.transform=function(t,e,n){let s="",i=!0;for(let r=0;r<Zs;r++){const o=rn[r],a=t[o];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(o.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=xs(a,Gn[o]);l||(i=!1,s+=`${_s[o]||o}(${t}) `),n&&(e[o]=t)}}return s=s.trim(),n?s=n(e,i?"":s):i&&(s="none"),s}(e,t.transform,n):s.transform&&(s.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=r;s.transformOrigin=`${t} ${e} ${n}`}}const Js=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ti(t,e,n){for(const s in e)qs(e[s])||Ks(s,n)||(t[s]=e[s])}function ei(e,n){const s={};return ti(s,e.style||{},e),Object.assign(s,function({transformTemplate:e},n){return t.useMemo((()=>{const t={style:{},transform:{},transformOrigin:{},vars:{}};return Qs(t,n,e),Object.assign({},t.vars,t.style)}),[n])}(e,n)),s}function ni(t,e){const n={},s=ei(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=s,n}const si=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ii(t){return"string"==typeof t&&!t.includes("-")&&!!(si.indexOf(t)>-1||/[A-Z]/u.test(t))}const ri={offset:"stroke-dashoffset",array:"stroke-dasharray"},oi={offset:"strokeDashoffset",array:"strokeDasharray"};function ai(t,{attrX:e,attrY:n,attrScale:s,pathLength:i,pathSpacing:r=1,pathOffset:o=0,...a},l,c){if(Qs(t,a,c),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:u,style:h}=t;u.transform&&(h.transform=u.transform,delete u.transform),(h.transform||u.transformOrigin)&&(h.transformOrigin=u.transformOrigin??"50% 50%",delete u.transformOrigin),h.transform&&(h.transformBox="fill-box",delete u.transformBox),void 0!==e&&(u.x=e),void 0!==n&&(u.y=n),void 0!==s&&(u.scale=s),void 0!==i&&function(t,e,n=1,s=0,i=!0){t.pathLength=1;const r=i?ri:oi;t[r.offset]=Bt.transform(-s);const o=Bt.transform(e),a=Bt.transform(n);t[r.array]=`${o} ${a}`}(u,i,r,o,!1)}const li=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),ci=t=>"string"==typeof t&&"svg"===t.toLowerCase();function ui(e,n,s,i){const r=t.useMemo((()=>{const t={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return ai(t,n,ci(i),e.transformTemplate),{...t.attrs,style:{...t.style}}}),[n]);if(e.style){const t={};ti(t,e.style,e),r.style={...t,...r.style}}return r}function hi(e=!1){return(n,s,i,{latestValues:r},o)=>{const a=(ii(n)?ui:ni)(s,r,o,n),l=function(t,e,n){const s={};for(const i in t)"values"===i&&"object"==typeof t.values||(ks(i)||!0===n&&Ps(i)||!e&&!Ps(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}(s,"string"==typeof n,e),c=n!==t.Fragment?{...l,...a,ref:i}:{},{children:u}=s,h=t.useMemo((()=>qs(u)?u.get():u),[u]);return t.createElement(n,{...c,children:h})}}function di(t){const e=[{},{}];return t?.values.forEach(((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()})),e}function pi(t,e,n,s){if("function"==typeof e){const[i,r]=di(s);e=e(void 0!==n?n:t.custom,i,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[i,r]=di(s);e=e(void 0!==n?n:t.custom,i,r)}return e}function mi(t){return qs(t)?t.get():t}const fi=e=>(n,s)=>{const i=t.useContext(Ms),r=t.useContext(x),o=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e},n,s,i){return{latestValues:gi(n,s,i,t),renderState:e()}}(e,n,i,r);return s?o():g(o)};function gi(t,e,n,s){const i={},r=s(t,{});for(const d in r)i[d]=mi(r[d]);let{initial:o,animate:a}=t;const l=Rs(t),c=Ls(t);e&&c&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let u=!!n&&!1===n.initial;u=u||!1===o;const h=u?a:o;if(h&&"boolean"!=typeof h&&!js(h)){const e=Array.isArray(h)?h:[h];for(let n=0;n<e.length;n++){const s=pi(t,e[n]);if(s){const{transitionEnd:t,transition:e,...n}=s;for(const s in n){let t=n[s];if(Array.isArray(t)){t=t[u?t.length-1:0]}null!==t&&(i[s]=t)}for(const s in t)i[s]=t[s]}}}return i}function yi(t,e,n){const{style:s}=t,i={};for(const r in s)(qs(s[r])||e.style&&qs(e.style[r])||Ks(r,t)||void 0!==n?.getValue(r)?.liveStyle)&&(i[r]=s[r]);return i}const vi={useVisualState:fi({scrapeMotionValuesFromProps:yi,createRenderState:Js})};function xi(t,e,n){const s=yi(t,e,n);for(const i in t)if(qs(t[i])||qs(e[i])){s[-1!==rn.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]}return s}const bi={useVisualState:fi({scrapeMotionValuesFromProps:xi,createRenderState:li})};function wi(t,e){return function(n,{forwardMotionProps:s}={forwardMotionProps:!1}){return Xs({...ii(n)?bi:vi,preloadedFeatures:t,useRender:hi(s),createVisualElement:e,Component:n})}}function Ti(t,e,n){const s=t.getProps();return pi(s,e,void 0!==n?n:s.custom,t)}const Si=t=>Array.isArray(t);function Pi(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,ys(n))}function ki(t,e){const n=t.getValue("willChange");if(s=n,Boolean(qs(s)&&s.add))return n.add(e);if(!n&&D.WillChange){const n=new D.WillChange("auto");t.addValue("willChange",n),n.add(e)}var s}function Ei(t){return t.props[zs]}const Ci=t=>null!==t;const Mi={type:"spring",stiffness:500,damping:25,restSpeed:10},ji={type:"keyframes",duration:.8},Ai={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Vi=(t,{keyframes:e})=>e.length>2?ji:on.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:Mi:Ai;const Di=(t,e,n,s={},i,r)=>o=>{const a=Fn(s,t)||{},l=a.delay||s.delay||0;let{elapsed:c=0}=s;c-=z(l);const u={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:i};(function({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length})(a)||Object.assign(u,Vi(t,u)),u.duration&&(u.duration=z(u.duration)),u.repeatDelay&&(u.repeatDelay=z(u.repeatDelay)),void 0!==u.from&&(u.keyframes[0]=u.from);let h=!1;if((!1===u.type||0===u.duration&&!u.repeatDelay)&&(u.duration=0,0===u.delay&&(h=!0)),(D.instantAnimations||D.skipAnimations)&&(h=!0,u.duration=0,u.delay=0),u.allowFlatten=!a.type&&!a.ease,h&&!r&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(Ci),r=e&&"loop"!==n&&e%2==1?0:i.length-1;return r&&void 0!==s?s:i[r]}(u.keyframes,a);if(void 0!==t)return void ut.update((()=>{u.onUpdate(t),u.onComplete()}))}return new Ln(u)};function Ri({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,s}function Li(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:o,...a}=e;s&&(r=s);const l=[],c=i&&t.animationState&&t.animationState.getState()[i];for(const u in a){const e=t.getValue(u,t.latestValues[u]??null),s=a[u];if(void 0===s||c&&Ri(c,u))continue;const i={delay:n,...Fn(r||{},u)},o=e.get();if(void 0!==o&&!e.isAnimating&&!Array.isArray(s)&&s===o&&!i.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){const e=Ei(t);if(e){const t=window.MotionHandoffAnimation(e,u,ut);null!==t&&(i.startTime=t,h=!0)}}ki(t,u),e.start(Di(u,e,s,t.shouldReduceMotion&&On.has(u)?{type:!1}:i,t,h));const d=e.animation;d&&l.push(d)}return o&&Promise.all(l).then((()=>{ut.update((()=>{o&&function(t,e){const n=Ti(t,e);let{transitionEnd:s={},transition:i={},...r}=n||{};r={...r,...s};for(const a in r)Pi(t,a,(o=r[a],Si(o)?o[o.length-1]||0:o));var o}(t,o)}))})),l}function Ni(t,e,n={}){const s=Ti(t,e,"exit"===n.type?t.presenceContext?.custom:void 0);let{transition:i=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const r=s?()=>Promise.all(Li(t,s,n)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(s=0)=>{const{delayChildren:r=0,staggerChildren:o,staggerDirection:a}=i;return function(t,e,n=0,s=0,i=1,r){const o=[],a=(t.variantChildren.size-1)*s,l=1===i?(t=0)=>t*s:(t=0)=>a-t*s;return Array.from(t.variantChildren).sort(Bi).forEach(((t,s)=>{t.notify("AnimationStart",e),o.push(Ni(t,e,{...r,delay:n+l(s)}).then((()=>t.notify("AnimationComplete",e))))})),Promise.all(o)}(t,e,r+s,o,a,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[t,e]="beforeChildren"===a?[r,o]:[o,r];return t().then((()=>e()))}return Promise.all([r(),o(n.delay)])}function Bi(t,e){return t.sortNodePosition(e)}function Fi(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}const Oi=Ds.length;function Ii(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&Ii(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let n=0;n<Oi;n++){const s=Ds[n],i=t.props[s];(As(i)||!1===i)&&(e[s]=i)}return e}const Wi=[...Vs].reverse(),zi=Vs.length;function Ui(t){return e=>Promise.all(e.map((({animation:e,options:n})=>function(t,e,n={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e)){const i=e.map((e=>Ni(t,e,n)));s=Promise.all(i)}else if("string"==typeof e)s=Ni(t,e,n);else{const i="function"==typeof e?Ti(t,e,n.custom):e;s=Promise.all(Li(t,i,n))}return s.then((()=>{t.notify("AnimationComplete",e)}))}(t,e,n))))}function $i(t){let e=Ui(t),n=Yi(),s=!0;const i=e=>(n,s)=>{const i=Ti(t,s,"exit"===e?t.presenceContext?.custom:void 0);if(i){const{transition:t,transitionEnd:e,...s}=i;n={...n,...s,...e}}return n};function r(r){const{props:o}=t,a=Ii(t.parent)||{},l=[],c=new Set;let u={},h=1/0;for(let e=0;e<zi;e++){const d=Wi[e],p=n[d],m=void 0!==o[d]?o[d]:a[d],f=As(m),g=d===r?p.isActive:null;!1===g&&(h=e);let y=m===a[d]&&m!==o[d]&&f;if(y&&s&&t.manuallyAnimateOnMount&&(y=!1),p.protectedKeys={...u},!p.isActive&&null===g||!m&&!p.prevProp||js(m)||"boolean"==typeof m)continue;const v=Hi(p.prevProp,m);let x=v||d===r&&p.isActive&&!y&&f||e>h&&f,b=!1;const w=Array.isArray(m)?m:[m];let T=w.reduce(i(d),{});!1===g&&(T={});const{prevResolvedValues:S={}}=p,P={...S,...T},k=e=>{x=!0,c.has(e)&&(b=!0,c.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in P){const e=T[t],n=S[t];if(u.hasOwnProperty(t))continue;let s=!1;s=Si(e)&&Si(n)?!Fi(e,n):e!==n,s?null!=e?k(t):c.add(t):void 0!==e&&c.has(t)?k(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=T,p.isActive&&(u={...u,...T}),s&&t.blockInitialAnimation&&(x=!1);x&&(!(y&&v)||b)&&l.push(...w.map((t=>({animation:t,options:{type:d}}))))}if(c.size){const e={};if("boolean"!=typeof o.initial){const n=Ti(t,Array.isArray(o.initial)?o.initial[0]:o.initial);n&&n.transition&&(e.transition=n.transition)}c.forEach((n=>{const s=t.getBaseTarget(n),i=t.getValue(n);i&&(i.liveStyle=!0),e[n]=s??null})),l.push({animation:e})}let d=Boolean(l.length);return!s||!1!==o.initial&&o.initial!==o.animate||t.manuallyAnimateOnMount||(d=!1),s=!1,d?e(l):Promise.resolve()}return{animateChanges:r,setActive:function(e,s){if(n[e].isActive===s)return Promise.resolve();t.variantChildren?.forEach((t=>t.animationState?.setActive(e,s))),n[e].isActive=s;const i=r(e);for(const t in n)n[t].protectedKeys={};return i},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=Yi(),s=!0}}}function Hi(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!Fi(e,t)}function Xi(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Yi(){return{animate:Xi(!0),whileInView:Xi(),whileHover:Xi(),whileTap:Xi(),whileDrag:Xi(),whileFocus:Xi(),exit:Xi()}}class Gi{constructor(t){this.isMounted=!1,this.node=t}update(){}}let Ki=0;const qi={animation:{Feature:class extends Gi{constructor(t){super(t),t.animationState||(t.animationState=$i(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();js(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends Gi{constructor(){super(...arguments),this.id=Ki++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then((()=>{e(this.id)}))}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function _i(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}function Zi(t){return{point:{x:t.pageX,y:t.pageY}}}function Qi(t,e,n,s){return _i(t,e,(t=>e=>as(e)&&t(e,Zi(e)))(n),s)}function Ji({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function tr(t){return t.max-t.min}function er(t,e,n,s=.5){t.origin=s,t.originPoint=Jt(e.min,e.max,t.origin),t.scale=tr(n)/tr(e),t.translate=Jt(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function nr(t,e,n,s){er(t.x,e.x,n.x,s?s.originX:void 0),er(t.y,e.y,n.y,s?s.originY:void 0)}function sr(t,e,n){t.min=n.min+e.min,t.max=t.min+tr(e)}function ir(t,e,n){t.min=e.min-n.min,t.max=t.min+tr(e)}function rr(t,e,n){ir(t.x,e.x,n.x),ir(t.y,e.y,n.y)}const or=()=>({x:{min:0,max:0},y:{min:0,max:0}});function ar(t){return[t("x"),t("y")]}function lr(t){return void 0===t||1===t}function cr({scale:t,scaleX:e,scaleY:n}){return!lr(t)||!lr(e)||!lr(n)}function ur(t){return cr(t)||hr(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function hr(t){return dr(t.x)||dr(t.y)}function dr(t){return t&&"0%"!==t}function pr(t,e,n){return n+e*(t-n)}function mr(t,e,n,s,i){return void 0!==i&&(t=pr(t,i,s)),pr(t,n,s)+e}function fr(t,e=0,n=1,s,i){t.min=mr(t.min,e,n,s,i),t.max=mr(t.max,e,n,s,i)}function gr(t,{x:e,y:n}){fr(t.x,e.translate,e.scale,e.originPoint),fr(t.y,n.translate,n.scale,n.originPoint)}const yr=.999999999999,vr=1.0000000000001;function xr(t,e){t.min=t.min+e,t.max=t.max+e}function br(t,e,n,s,i=.5){fr(t,e,n,Jt(t.min,t.max,i),s)}function wr(t,e){br(t.x,e.x,e.scaleX,e.scale,e.originX),br(t.y,e.y,e.scaleY,e.scale,e.originY)}function Tr(t,e){return Ji(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}const Sr=({current:t})=>t?t.ownerDocument.defaultView:null,Pr=(t,e)=>Math.abs(t-e);class kr{constructor(t,e,{transformPagePoint:n,contextWindow:s,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=Mr(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=function(t,e){const n=Pr(t.x,e.x),s=Pr(t.y,e.y);return Math.sqrt(n**2+s**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;const{point:s}=t,{timestamp:i}=dt;this.history.push({...s,timestamp:i});const{onStart:r,onMove:o}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=Er(e,this.transformPagePoint),ut.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:s,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const r=Mr("pointercancel"===t.type?this.lastMoveEventInfo:Er(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,r),s&&s(t,r)},!as(t))return;this.dragSnapToOrigin=i,this.handlers=e,this.transformPagePoint=n,this.contextWindow=s||window;const r=Er(Zi(t),this.transformPagePoint),{point:o}=r,{timestamp:a}=dt;this.history=[{...o,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,Mr(r,this.history)),this.removeListeners=O(Qi(this.contextWindow,"pointermove",this.handlePointerMove),Qi(this.contextWindow,"pointerup",this.handlePointerUp),Qi(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),ht(this.updatePoint)}}function Er(t,e){return e?{point:e(t.point)}:t}function Cr(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Mr({point:t},e){return{point:t,delta:Cr(t,Ar(e)),offset:Cr(t,jr(e)),velocity:Vr(e,.1)}}function jr(t){return t[0]}function Ar(t){return t[t.length-1]}function Vr(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=Ar(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>z(e)));)n--;if(!s)return{x:0,y:0};const r=U(i.timestamp-s.timestamp);if(0===r)return{x:0,y:0};const o={x:(i.x-s.x)/r,y:(i.y-s.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function Dr(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function Rr(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}const Lr=.35;function Nr(t,e,n){return{min:Br(t,e),max:Br(t,n)}}function Br(t,e){return"number"==typeof t?t:t[e]||0}const Fr=new WeakMap;class Or{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:s}=this.getProps();this.panSession=new kr(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(Zi(t).point)},onStart:(t,e)=>{const{drag:n,dragPropagation:s,onDragStart:i}=this.getProps();if(n&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(r=n)||"y"===r?ns[r]?null:(ns[r]=!0,()=>{ns[r]=!1}):ns.x||ns.y?null:(ns.x=ns.y=!0,()=>{ns.x=ns.y=!1}),!this.openDragLock))return;var r;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ar((t=>{let e=this.getAxisMotionValue(t).get()||0;if(Nt.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const s=n.layout.layoutBox[t];if(s){e=tr(s)*(parseFloat(e)/100)}}}this.originPoint[t]=e})),i&&ut.postRender((()=>i(t,e))),ki(this.visualElement,"transform");const{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:n,dragDirectionLock:s,onDirectionLock:i,onDrag:r}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:o}=e;if(s&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(o),void(null!==this.currentDirection&&i&&i(this.currentDirection));this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>ar((t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play()))},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:Sr(this.visualElement)})}stop(t,e){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:s}=e;this.startAnimation(s);const{onDragEnd:i}=this.getProps();i&&ut.postRender((()=>i(t,e)))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:s}=this.getProps();if(!n||!Ir(t,s,this.currentDirection))return;const i=this.getAxisMotionValue(t);let r=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:n},s){return void 0!==e&&t<e?t=s?Jt(e,t,s.min):Math.max(t,e):void 0!==n&&t>n&&(t=s?Jt(n,t,s.max):Math.min(t,n)),t}(r,this.constraints[t],this.elastic[t])),i.set(r)}resolveConstraints(){const{dragConstraints:t,dragElastic:e}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;t&&Os(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!n)&&function(t,{top:e,left:n,bottom:s,right:i}){return{x:Dr(t.x,n,i),y:Dr(t.y,e,s)}}(n.layoutBox,t),this.elastic=function(t=Lr){return!1===t?t=0:!0===t&&(t=Lr),{x:Nr(t,"left","right"),y:Nr(t,"top","bottom")}}(e),s!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&ar((t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(n.layoutBox[t],this.constraints[t]))}))}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!Os(t))return!1;const n=t.current,{projection:s}=this.visualElement;if(!s||!s.layout)return!1;const i=function(t,e,n){const s=Tr(t,n),{scroll:i}=e;return i&&(xr(s.x,i.offset.x),xr(s.y,i.offset.y)),s}(n,s.root,this.visualElement.getTransformPagePoint());let r=function(t,e){return{x:Rr(t.x,e.x),y:Rr(t.y,e.y)}}(s.layout.layoutBox,i);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(r));this.hasMutatedConstraints=!!t,t&&(r=Ji(t))}return r}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:s,dragTransition:i,dragSnapToOrigin:r,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{},l=ar((o=>{if(!Ir(o,e,this.currentDirection))return;let l=a&&a[o]||{};r&&(l={min:0,max:0});const c=s?200:1e6,u=s?40:1e7,h={type:"inertia",velocity:n?t[o]:0,bounceStiffness:c,bounceDamping:u,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(o,h)}));return Promise.all(l).then(o)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return ki(this.visualElement,t),n.start(Di(t,n,0,e,this.visualElement,!1))}stopAnimation(){ar((t=>this.getAxisMotionValue(t).stop()))}pauseAnimation(){ar((t=>this.getAxisMotionValue(t).animation?.pause()))}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),s=n[e];return s||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){ar((e=>{const{drag:n}=this.getProps();if(!Ir(e,n,this.currentDirection))return;const{projection:s}=this.visualElement,i=this.getAxisMotionValue(e);if(s&&s.layout){const{min:n,max:r}=s.layout.layoutBox[e];i.set(t[e]-Jt(n,r,.5))}}))}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!Os(e)||!n||!this.constraints)return;this.stopAnimation();const s={x:0,y:0};ar((t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();s[t]=function(t,e){let n=.5;const s=tr(t),i=tr(e);return i>s?n=I(e.min,e.max-s,t.min):s>i&&(n=I(t.min,t.max-i,e.min)),V(0,1,n)}({min:n,max:n},this.constraints[t])}}));const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),ar((e=>{if(!Ir(e,t,null))return;const n=this.getAxisMotionValue(e),{min:i,max:r}=this.constraints[e];n.set(Jt(i,r,s[e]))}))}addListeners(){if(!this.visualElement.current)return;Fr.set(this.visualElement,this);const t=Qi(this.visualElement.current,"pointerdown",(t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)})),e=()=>{const{dragConstraints:t}=this.getProps();Os(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,s=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),ut.read(e);const i=_i(window,"resize",(()=>this.scalePositionWithinConstraints())),r=n.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(ar((e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))})),this.visualElement.render())}));return()=>{i(),t(),s(),r&&r()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:s=!1,dragConstraints:i=!1,dragElastic:r=Lr,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:s,dragConstraints:i,dragElastic:r,dragMomentum:o}}}function Ir(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const Wr=t=>(e,n)=>{t&&ut.postRender((()=>t(e,n)))};const zr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Ur(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const $r={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!Bt.test(t))return t;t=parseFloat(t)}return`${Ur(t,e.target.x)}% ${Ur(t,e.target.y)}%`}},Hr={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=_t.parse(t);if(i.length>5)return s;const r=_t.createTransformer(t),o="number"!=typeof i[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;i[0+o]/=a,i[1+o]/=l;const c=Jt(a,l,.5);return"number"==typeof i[2+o]&&(i[2+o]/=c),"number"==typeof i[3+o]&&(i[3+o]/=c),r(i)}};class Xr extends t.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:s}=this.props,{projection:i}=t;!function(t){for(const e in t)Gs[e]=t[e],vt(e)&&(Gs[e].isCSSVariable=!0)}(Gr),i&&(e.group&&e.group.add(i),n&&n.register&&s&&n.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",(()=>{this.safeToRemove()})),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),zr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:s,isPresent:i}=this.props,r=n.projection;return r?(r.isPresent=i,s||t.layoutDependency!==e||void 0===e||t.isPresent!==i?r.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?r.promote():r.relegate()||ut.postRender((()=>{const t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),ts.postRender((()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()})))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),n&&n.deregister&&n.deregister(s))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Yr(n){const[s,i]=k(),r=t.useContext(f);return e.jsx(Xr,{...n,layoutGroup:r,switchLayoutGroup:t.useContext(Us),isPresent:s,safeToRemove:i})}const Gr={borderRadius:{...$r,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:$r,borderTopRightRadius:$r,borderBottomLeftRadius:$r,borderBottomRightRadius:$r,boxShadow:Hr};const Kr=(t,e)=>t.depth-e.depth;class qr{constructor(){this.children=[],this.isDirty=!1}add(t){j(this.children,t),this.isDirty=!0}remove(t){A(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Kr),this.isDirty=!1,this.children.forEach(t)}}const _r=["TopLeft","TopRight","BottomLeft","BottomRight"],Zr=_r.length,Qr=t=>"string"==typeof t?parseFloat(t):t,Jr=t=>"number"==typeof t||Bt.test(t);function to(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const eo=so(0,.5,J),no=so(.5,.95,B);function so(t,e,n){return s=>s<t?0:s>e?1:n(I(t,e,s))}function io(t,e){t.min=e.min,t.max=e.max}function ro(t,e){io(t.x,e.x),io(t.y,e.y)}function oo(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function ao(t,e,n,s,i){return t=pr(t-=e,1/n,s),void 0!==i&&(t=pr(t,1/i,s)),t}function lo(t,e,[n,s,i],r,o){!function(t,e=0,n=1,s=.5,i,r=t,o=t){Nt.test(e)&&(e=parseFloat(e),e=Jt(o.min,o.max,e/100)-o.min);if("number"!=typeof e)return;let a=Jt(r.min,r.max,s);t===r&&(a-=e),t.min=ao(t.min,e,n,a,i),t.max=ao(t.max,e,n,a,i)}(t,e[n],e[s],e[i],e.scale,r,o)}const co=["x","scaleX","originX"],uo=["y","scaleY","originY"];function ho(t,e,n,s){lo(t.x,e,co,n?n.x:void 0,s?s.x:void 0),lo(t.y,e,uo,n?n.y:void 0,s?s.y:void 0)}function po(t){return 0===t.translate&&1===t.scale}function mo(t){return po(t.x)&&po(t.y)}function fo(t,e){return t.min===e.min&&t.max===e.max}function go(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function yo(t,e){return go(t.x,e.x)&&go(t.y,e.y)}function vo(t){return tr(t.x)/tr(t.y)}function xo(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class bo{constructor(){this.members=[]}add(t){j(this.members,t),t.scheduleRender()}remove(t){if(A(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex((e=>t===e));if(0===e)return!1;let n;for(let s=e;s>=0;s--){const t=this.members[s];if(!1!==t.isPresent){n=t;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:s}=t.options;!1===s&&n.hide()}}exitAnimationComplete(){this.members.forEach((t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()}))}scheduleRender(){this.members.forEach((t=>{t.instance&&t.scheduleRender(!1)}))}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const wo=["","X","Y","Z"],To={visibility:"hidden"};let So=0;function Po(t,e,n,s){const{latestValues:i}=e;i[t]&&(n[t]=i[t],e.setStaticValue(t,0),s&&(s[t]=0))}function ko(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=Ei(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:e,layoutId:s}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",ut,!(e||s))}const{parent:s}=t;s&&!s.hasCheckedOptimisedAppear&&ko(s)}function Eo({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(t={},n=e?.()){this.id=So++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(jo),this.nodes.forEach(Bo),this.nodes.forEach(Fo),this.nodes.forEach(Ao)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new qr)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new W),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,n=this.root.hasTreeAnimated){if(this.instance)return;var s;this.isSVG=(s=e)instanceof SVGElement&&"svg"!==s.tagName,this.instance=e;const{layoutId:i,layout:r,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(r||i)&&(this.isLayoutDirty=!0),t){let n;const s=()=>this.root.updateBlockedByResize=!1;t(e,(()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(t,e){const n=gt.now(),s=({timestamp:i})=>{const r=i-n;r>=e&&(ht(s),t(r-e))};return ut.setup(s,!0),()=>ht(s)}(s,250),zr.hasAnimatedSinceResize&&(zr.hasAnimatedSinceResize=!1,this.nodes.forEach(No))}))}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&o&&(i||r)&&this.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:n,layout:s})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const i=this.options.transition||o.getDefaultTransition()||$o,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!yo(this.targetLayout,s),c=!e&&n;if(this.options.layoutRoot||this.resumeFrom||c||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,c);const e={...Fn(i,"layout"),onPlay:r,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||No(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s}))}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,ht(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Oo),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&ko(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let i=0;i<this.path.length;i++){const t=this.path[i];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(Do);this.isUpdating||this.nodes.forEach(Ro),this.isUpdating=!1,this.nodes.forEach(Lo),this.nodes.forEach(Co),this.nodes.forEach(Mo),this.clearAllSnapshots();const t=gt.now();dt.delta=V(0,1e3/60,t-dt.timestamp),dt.timestamp=t,dt.isProcessing=!0,pt.update.process(dt),pt.preRender.process(dt),pt.render.process(dt),dt.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ts.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Vo),this.sharedNodes.forEach(Io)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,ut.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){ut.postRender((()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()}))}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||tr(this.snapshot.measuredBox.x)||tr(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let n=0;n<this.path.length;n++){this.path[n].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){const e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!i)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!mo(this.projectionDelta),n=this.getTransformTemplate(),s=n?n(this.latestValues,""):void 0,r=s!==this.prevTransformTemplateValue;t&&(e||ur(this.latestValues)||r)&&(i(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var s;return t&&(n=this.removeTransform(n)),Yo((s=n).x),Yo(s.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(Ko))){const{scroll:t}=this.root;t&&(xr(e.x,t.offset.x),xr(e.y,t.offset.y))}return e}removeElementScroll(t){const e={x:{min:0,max:0},y:{min:0,max:0}};if(ro(e,t),this.scroll?.wasRoot)return e;for(let n=0;n<this.path.length;n++){const s=this.path[n],{scroll:i,options:r}=s;s!==this.root&&i&&r.layoutScroll&&(i.wasRoot&&ro(e,t),xr(e.x,i.offset.x),xr(e.y,i.offset.y))}return e}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};ro(n,t);for(let s=0;s<this.path.length;s++){const t=this.path[s];!e&&t.options.layoutScroll&&t.scroll&&t!==t.root&&wr(n,{x:-t.scroll.offset.x,y:-t.scroll.offset.y}),ur(t.latestValues)&&wr(n,t.latestValues)}return ur(this.latestValues)&&wr(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};ro(e,t);for(let n=0;n<this.path.length;n++){const t=this.path[n];if(!t.instance)continue;if(!ur(t.latestValues))continue;cr(t.latestValues)&&t.updateSnapshot();const s={x:{min:0,max:0},y:{min:0,max:0}};ro(s,t.measurePageBox()),ho(e,t.latestValues,t.snapshot?t.snapshot.layoutBox:void 0,s)}return ur(this.latestValues)&&ho(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==dt.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){const e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);const n=Boolean(this.resumingFrom)||this!==e;if(!(t||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:s,layoutId:i}=this.options;if(this.layout&&(s||i)){if(this.resolvedRelativeTargetAt=dt.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},rr(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),ro(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}var r,o,a;if(this.relativeTarget||this.targetDelta)if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),r=this.target,o=this.relativeTarget,a=this.relativeParent.target,sr(r.x,o.x,a.x),sr(r.y,o.y,a.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):ro(this.target,this.layout.layoutBox),gr(this.target,this.targetDelta)):ro(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},rr(this.relativeTargetOrigin,this.target,t.target),ro(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(this.parent&&!cr(this.parent.latestValues)&&!hr(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const t=this.getLead(),e=Boolean(this.resumingFrom)||this!==t;let n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===dt.timestamp&&(n=!1),n)return;const{layout:s,layoutId:i}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!s&&!i)return;ro(this.layoutCorrected,this.layout.layoutBox);const r=this.treeScale.x,o=this.treeScale.y;!function(t,e,n,s=!1){const i=n.length;if(!i)return;let r,o;e.x=e.y=1;for(let a=0;a<i;a++){r=n[a],o=r.projectionDelta;const{visualElement:i}=r.options;i&&i.props.style&&"contents"===i.props.style.display||(s&&r.options.layoutScroll&&r.scroll&&r!==r.root&&wr(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),o&&(e.x*=o.x.scale,e.y*=o.y.scale,gr(t,o)),s&&ur(r.latestValues)&&wr(t,r.latestValues))}e.x<vr&&e.x>yr&&(e.x=1),e.y<vr&&e.y>yr&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,e),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox,t.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:a}=t;a?(this.projectionDelta&&this.prevProjectionDelta?(oo(this.prevProjectionDelta.x,this.projectionDelta.x),oo(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nr(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===r&&this.treeScale.y===o&&xo(this.projectionDelta.x,this.prevProjectionDelta.x)&&xo(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a))):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const n=this.snapshot,s=n?n.latestValues:{},i={...this.latestValues},r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const o={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),c=!l||l.members.length<=1,u=Boolean(a&&!c&&!0===this.options.crossfade&&!this.path.some(Uo));let h;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d,p,m,f,g;Wo(r.x,t.x,n),Wo(r.y,t.y,n),this.setTargetDelta(r),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(rr(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,g=n,zo(p.x,m.x,f.x,g),zo(p.y,m.y,f.y,g),h&&(l=this.relativeTarget,d=h,fo(l.x,d.x)&&fo(l.y,d.y))&&(this.isProjectionDirty=!1),h||(h={x:{min:0,max:0},y:{min:0,max:0}}),ro(h,this.relativeTarget)),a&&(this.animationValues=i,function(t,e,n,s,i,r){i?(t.opacity=Jt(0,n.opacity??1,eo(s)),t.opacityExit=Jt(e.opacity??1,0,no(s))):r&&(t.opacity=Jt(e.opacity??1,n.opacity??1,s));for(let o=0;o<Zr;o++){const i=`border${_r[o]}Radius`;let r=to(e,i),a=to(n,i);void 0===r&&void 0===a||(r||(r=0),a||(a=0),0===r||0===a||Jr(r)===Jr(a)?(t[i]=Math.max(Jt(Qr(r),Qr(a),s),0),(Nt.test(a)||Nt.test(r))&&(t[i]+="%")):t[i]=a)}(e.rotate||n.rotate)&&(t.rotate=Jt(e.rotate||0,n.rotate||0,s))}(i,s,this.latestValues,n,u,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(ht(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ut.update((()=>{zr.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,n){const s=qs(t)?t:ys(t);return s.start(Di("",s,e,n)),s.animation}(0,1e3,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0}))}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:s,latestValues:i}=t;if(e&&n&&s){if(this!==t&&this.layout&&s&&Go(this.options.animationType,this.layout.layoutBox,s.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=tr(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const s=tr(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+s}ro(e,n),wr(e,i),nr(this.projectionDeltaWithTransform,this.layoutCorrected,e,i)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new bo);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){const{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){const{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const s=this.getStack();s&&s.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;const s={};n.z&&Po("z",t,s,this.animationValues);for(let i=0;i<wo.length;i++)Po(`rotate${wo[i]}`,t,s,this.animationValues),Po(`skew${wo[i]}`,t,s,this.animationValues);t.render();for(const i in s)t.setStaticValue(i,s[i]),this.animationValues&&(this.animationValues[i]=s[i]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return To;const e={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=mi(t?.pointerEvents)||"",e.transform=n?n(this.latestValues,""):"none",e;const s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=mi(t?.pointerEvents)||""),this.hasProjected&&!ur(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}const i=s.animationValues||s.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,n){let s="";const i=t.x.translate/e.x,r=t.y.translate/e.y,o=n?.z||0;if((i||r||o)&&(s=`translate3d(${i}px, ${r}px, ${o}px) `),1===e.x&&1===e.y||(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:t,rotate:e,rotateX:i,rotateY:r,skewX:o,skewY:a}=n;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),i&&(s+=`rotateX(${i}deg) `),r&&(s+=`rotateY(${r}deg) `),o&&(s+=`skewX(${o}deg) `),a&&(s+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,i),n&&(e.transform=n(i,e.transform));const{x:r,y:o}=this.projectionDelta;e.transformOrigin=`${100*r.origin}% ${100*o.origin}% 0`,s.animationValues?e.opacity=s===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:e.opacity=s===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0;for(const a in Gs){if(void 0===i[a])continue;const{correct:t,applyTo:n,isCSSVariable:r}=Gs[a],o="none"===e.transform?i[a]:t(i[a],s);if(n){const t=n.length;for(let s=0;s<t;s++)e[n[s]]=o}else r?this.options.visualElement.renderState.vars[a]=o:e[a]=o}return this.options.layoutId&&(e.pointerEvents=s===this?mi(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach((t=>t.currentAnimation?.stop())),this.root.nodes.forEach(Do),this.root.sharedNodes.clear()}}}function Co(t){t.updateLayout()}function Mo(t){const e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:s}=t.layout,{animationType:i}=t.options,r=e.source!==t.layout.source;"size"===i?ar((t=>{const s=r?e.measuredBox[t]:e.layoutBox[t],i=tr(s);s.min=n[t].min,s.max=s.min+i})):Go(i,e.layoutBox,n)&&ar((s=>{const i=r?e.measuredBox[s]:e.layoutBox[s],o=tr(n[s]);i.max=i.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+o)}));const o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};nr(o,n,e.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};r?nr(a,t.applyTransform(s,!0),e.measuredBox):nr(a,n,e.layoutBox);const l=!mo(o);let c=!1;if(!t.resumeFrom){const s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){const{snapshot:i,layout:r}=s;if(i&&r){const o={x:{min:0,max:0},y:{min:0,max:0}};rr(o,e.layoutBox,i.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};rr(a,n,r.layoutBox),yo(o,a)||(c=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:n,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:c})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function jo(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Ao(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Vo(t){t.clearSnapshot()}function Do(t){t.clearMeasurements()}function Ro(t){t.isLayoutDirty=!1}function Lo(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function No(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Bo(t){t.resolveTargetDelta()}function Fo(t){t.calcProjection()}function Oo(t){t.resetSkewAndRotation()}function Io(t){t.removeLeadSnapshot()}function Wo(t,e,n){t.translate=Jt(e.translate,0,n),t.scale=Jt(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function zo(t,e,n,s){t.min=Jt(e.min,n.min,s),t.max=Jt(e.max,n.max,s)}function Uo(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const $o={duration:.45,ease:[.4,0,.1,1]},Ho=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Xo=Ho("applewebkit/")&&!Ho("chrome/")?Math.round:B;function Yo(t){t.min=Xo(t.min),t.max=Xo(t.max)}function Go(t,e,n){return"position"===t||"preserve-aspect"===t&&(s=vo(e),i=vo(n),r=.2,!(Math.abs(s-i)<=r));var s,i,r}function Ko(t){return t!==t.root&&t.scroll?.wasRoot}const qo=Eo({attachResizeListener:(t,e)=>_i(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),_o={current:void 0},Zo=Eo({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!_o.current){const t=new qo({});t.mount(window),t.setOptions({layoutScroll:!0}),_o.current=t}return _o.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)}),Qo={pan:{Feature:class extends Gi{constructor(){super(...arguments),this.removePointerDownListener=B}onPointerDown(t){this.session=new kr(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Sr(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:s}=this.node.getProps();return{onSessionStart:Wr(t),onStart:Wr(e),onMove:n,onEnd:(t,e)=>{delete this.session,s&&ut.postRender((()=>s(t,e)))}}}mount(){this.removePointerDownListener=Qi(this.node.current,"pointerdown",(t=>this.onPointerDown(t)))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Gi{constructor(t){super(t),this.removeGroupControls=B,this.removeListeners=B,this.controls=new Or(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||B}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:Zo,MeasureLayout:Yr}};function Jo(t,e,n){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===n);const i=s["onHover"+n];i&&ut.postRender((()=>i(e,Zi(e))))}function ta(t,e,n){const{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===n);const i=s["onTap"+("End"===n?"":n)];i&&ut.postRender((()=>i(e,Zi(e))))}const ea=new WeakMap,na=new WeakMap,sa=t=>{const e=ea.get(t.target);e&&e(t)},ia=t=>{t.forEach(sa)};function ra(t,e,n){const s=function({root:t,...e}){const n=t||document;na.has(n)||na.set(n,{});const s=na.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(ia,{root:t,...e})),s[i]}(e);return ea.set(t,n),s.observe(t),()=>{ea.delete(t),s.unobserve(t)}}const oa={some:0,all:1};const aa={inView:{Feature:class extends Gi{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:s="some",once:i}=t,r={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof s?s:oa[s]};return ra(this.node.current,r,(t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,i&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:s}=this.node.getProps(),r=e?n:s;r&&r(t)}))}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Gi{mount(){const{current:t}=this.node;t&&(this.unmount=ps(t,((t,e)=>(ta(this.node,e,"Start"),(t,{success:e})=>ta(this.node,t,e?"End":"Cancel"))),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends Gi{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=O(_i(this.node.current,"focus",(()=>this.onFocus())),_i(this.node.current,"blur",(()=>this.onBlur())))}unmount(){}}},hover:{Feature:class extends Gi{mount(){const{current:t}=this.node;t&&(this.unmount=function(t,e,n={}){const[s,i,r]=is(t,n),o=t=>{if(!rs(t))return;const{target:n}=t,s=e(n,t);if("function"!=typeof s||!n)return;const r=t=>{rs(t)&&(s(t),n.removeEventListener("pointerleave",r))};n.addEventListener("pointerleave",r,i)};return s.forEach((t=>{t.addEventListener("pointerenter",o,i)})),r}(t,((t,e)=>(Jo(this.node,e,"Start"),t=>Jo(this.node,t,"End")))))}unmount(){}}}},la={layout:{ProjectionNode:Zo,MeasureLayout:Yr}},ca={current:null},ua={current:!1};const ha=new WeakMap;const da=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class pa{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:s,blockInitialAnimation:i,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=yn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=gt.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,ut.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=r;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=Boolean(i),this.isControllingVariants=Rs(e),this.isVariantNode=Ls(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:c,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(const h in u){const t=u[h];void 0!==a[h]&&qs(t)&&t.set(a[h],!1)}}mount(t){this.current=t,ha.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((t,e)=>this.bindToMotionValue(e,t))),ua.current||function(){if(ua.current=!0,y)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ca.current=t.matches;t.addListener(e),e()}else ca.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ca.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),ht(this.notifyUpdate),ht(this.render),this.valueSubscriptions.forEach((t=>t())),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=on.has(t);n&&this.onBindTransform&&this.onBindTransform();const s=e.on("change",(e=>{this.latestValues[t]=e,this.props.onUpdate&&ut.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)})),i=e.on("renderRequest",this.scheduleRender);let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,(()=>{s(),i(),r&&r(),e.owner&&e.stop()}))}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in Ts){const e=Ts[t];if(!e)continue;const{isEnabled:n,Feature:s}=e;if(!this.features[t]&&s&&n(this.props)&&(this.features[t]=new s(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let n=0;n<da.length;n++){const e=da[n];this.propEventSubscriptions[e]&&(this.propEventSubscriptions[e](),delete this.propEventSubscriptions[e]);const s=t["on"+e];s&&(this.propEventSubscriptions[e]=this.on(e,s))}this.prevMotionValues=function(t,e,n){for(const s in e){const i=e[s],r=n[s];if(qs(i))t.addValue(s,i);else if(qs(r))t.addValue(s,ys(i,{owner:t}));else if(r!==i)if(t.hasValue(s)){const e=t.getValue(s);!0===e.liveStyle?e.jump(i):e.hasAnimated||e.set(i)}else{const e=t.getStaticValue(s);t.addValue(s,ys(void 0!==e?e:i,{owner:t}))}}for(const s in n)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=ys(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var s;return null!=n&&("string"==typeof n&&(R(n)||L(n))?n=parseFloat(n):(s=n,!vs.find(In(s))&&_t.test(e)&&(n=_n(t,e))),this.setBaseTarget(t,qs(n)?n.get():n)),qs(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let n;if("string"==typeof e||"object"==typeof e){const s=pi(this.props,e,this.presenceContext?.custom);s&&(n=s[t])}if(e&&void 0!==n)return n;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||qs(s)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new W),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class ma extends pa{constructor(){super(...arguments),this.KeyframeResolver=Qn}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;qs(t)&&(this.childSubscription=t.on("change",(t=>{this.current&&(this.current.textContent=`${t}`)})))}}function fa(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const r in n)t.style.setProperty(r,n[r])}class ga extends ma{constructor(){super(...arguments),this.type="html",this.renderInstance=fa}readValueFromInstance(t,e){if(on.has(e))return((t,e)=>{const{transform:n="none"}=getComputedStyle(t);return nn(n,e)})(t,e);{const s=(n=t,window.getComputedStyle(n)),i=(vt(e)?s.getPropertyValue(e):s[e])||0;return"string"==typeof i?i.trim():i}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return Tr(t,e)}build(t,e,n){Qs(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return yi(t,e,n)}}const ya=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class va extends ma{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=or}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(on.has(e)){const t=qn(e);return t&&t.default||0}return e=ya.has(e)?e:Ws(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return xi(t,e,n)}build(t,e,n){ai(t,e,this.isSVGTag,n.transformTemplate)}renderInstance(t,e,n,s){!function(t,e,n,s){fa(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(ya.has(i)?i:Ws(i),e.attrs[i])}(t,e,0,s)}mount(t){this.isSVGTag=ci(t.tagName),super.mount(t)}}const xa=Cs(wi({...qi,...aa,...Qo,...la},((e,n)=>ii(e)?new va(n):new ga(n,{allowProjection:e!==t.Fragment})))),ba=new WeakMap;let wa;function Ta({target:t,contentRect:e,borderBoxSize:n}){ba.get(t)?.forEach((s=>{s({target:t,contentSize:e,get size(){return function(t,e){if(e){const{inlineSize:t,blockSize:n}=e[0];return{width:t,height:n}}return t instanceof SVGElement&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}(t,n)}})}))}function Sa(t){t.forEach(Ta)}function Pa(t,e){wa||"undefined"!=typeof ResizeObserver&&(wa=new ResizeObserver(Sa));const n=Jn(t);return n.forEach((t=>{let n=ba.get(t);n||(n=new Set,ba.set(t,n)),n.add(e),wa?.observe(t)})),()=>{n.forEach((t=>{const n=ba.get(t);n?.delete(e),n?.size||wa?.unobserve(t)}))}}const ka=new Set;let Ea;function Ca(t){return ka.add(t),Ea||(Ea=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};ka.forEach((t=>t(e)))},window.addEventListener("resize",Ea)),()=>{ka.delete(t),!ka.size&&Ea&&(Ea=void 0)}}const Ma={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function ja(t,e,n,s){const i=n[e],{length:r,position:o}=Ma[e],a=i.current,l=n.time;i.current=t[`scroll${o}`],i.scrollLength=t[`scroll${r}`]-t[`client${r}`],i.offset.length=0,i.offset[0]=0,i.offset[1]=i.scrollLength,i.progress=I(0,i.scrollLength,i.current);const c=s-l;i.velocity=c>50?0:$(i.current-a,c)}const Aa={start:0,center:.5,end:1};function Va(t,e,n=0){let s=0;if(t in Aa&&(t=Aa[t]),"string"==typeof t){const e=parseFloat(t);t.endsWith("px")?s=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?s=e/100*document.documentElement.clientWidth:t.endsWith("vh")?s=e/100*document.documentElement.clientHeight:t=e}return"number"==typeof t&&(s=e*t),n+s}const Da=[0,0];function Ra(t,e,n,s){let i=Array.isArray(t)?t:Da,r=0,o=0;return"number"==typeof t?i=[t,t]:"string"==typeof t&&(i=(t=t.trim()).includes(" ")?t.split(" "):[t,Aa[t]?t:"0"]),r=Va(i[0],n,s),o=Va(i[1],e),r-o}const La={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},Na={x:0,y:0};function Ba(t,e,n){const{offset:s=La.All}=n,{target:i=t,axis:r="y"}=n,o="y"===r?"height":"width",a=i!==t?function(t,e){const n={x:0,y:0};let s=t;for(;s&&s!==e;)if(s instanceof HTMLElement)n.x+=s.offsetLeft,n.y+=s.offsetTop,s=s.offsetParent;else if("svg"===s.tagName){const t=s.getBoundingClientRect();s=s.parentElement;const e=s.getBoundingClientRect();n.x+=t.left-e.left,n.y+=t.top-e.top}else{if(!(s instanceof SVGGraphicsElement))break;{const{x:t,y:e}=s.getBBox();n.x+=t,n.y+=e;let i=null,r=s.parentNode;for(;!i;)"svg"===r.tagName&&(i=r),r=s.parentNode;s=i}}return n}(i,t):Na,l=i===t?{width:t.scrollWidth,height:t.scrollHeight}:function(t){return"getBBox"in t&&"svg"!==t.tagName?t.getBBox():{width:t.clientWidth,height:t.clientHeight}}(i),c={width:t.clientWidth,height:t.clientHeight};e[r].offset.length=0;let u=!e[r].interpolate;const h=s.length;for(let d=0;d<h;d++){const t=Ra(s[d],c[o],l[o],a[r]);u||t===e[r].interpolatorOffsets[d]||(u=!0),e[r].offset[d]=t}u&&(e[r].interpolate=Oe(e[r].offset,Ie(s),{clamp:!1}),e[r].interpolatorOffsets=[...e[r].offset]),e[r].progress=V(0,1,e[r].interpolate(e[r].current))}function Fa(t,e,n,s={}){return{measure:()=>function(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let s=e;for(;s&&s!==t;)n.x.targetOffset+=s.offsetLeft,n.y.targetOffset+=s.offsetTop,s=s.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}(t,s.target,n),update:e=>{!function(t,e,n){ja(t,"x",e,n),ja(t,"y",e,n),e.time=n}(t,n,e),(s.offset||s.target)&&Ba(t,n,s)},notify:()=>e(n)}}const Oa=new WeakMap,Ia=new WeakMap,Wa=new WeakMap,za=t=>t===document.documentElement?window:t;function Ua(t,{container:e=document.documentElement,...n}={}){let s=Wa.get(e);s||(s=new Set,Wa.set(e,s));const i=Fa(e,t,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},n);if(s.add(i),!Oa.has(e)){const t=()=>{for(const t of s)t.measure()},n=()=>{for(const t of s)t.update(dt.timestamp)},i=()=>{for(const t of s)t.notify()},a=()=>{ut.read(t,!1,!0),ut.read(n,!1,!0),ut.preUpdate(i,!1,!0)};Oa.set(e,a);const l=za(e);window.addEventListener("resize",a,{passive:!0}),e!==document.documentElement&&Ia.set(e,(o=a,"function"==typeof(r=e)?Ca(r):Pa(r,o))),l.addEventListener("scroll",a,{passive:!0})}var r,o;const a=Oa.get(e);return ut.read(a,!1,!0),()=>{ht(a);const t=Wa.get(e);if(!t)return;if(t.delete(i),t.size)return;const n=Oa.get(e);Oa.delete(e),n&&(za(e).removeEventListener("scroll",n),Ia.get(e)?.(),window.removeEventListener("resize",n))}}const $a=new Map;function Ha({source:t,container:e,...n}){const{axis:s}=n;t&&(e=t);const i=$a.get(e)??new Map;$a.set(e,i);const r=n.target??"self",o=i.get(r)??{},a=s+(n.offset??[]).join(",");return o[a]||(o[a]=!n.target&&vn()?new ScrollTimeline({source:e,axis:s}):function(t){const e={value:0},n=Ua((n=>{e.value=100*n[t.axis].progress}),t);return{currentTime:e,cancel:n}}({container:e,...n})),o[a]}function Xa(t,{axis:e="y",container:n=document.documentElement,...s}={}){n===document.documentElement&&("y"===e&&n.scrollHeight===n.clientHeight||"x"===e&&n.scrollWidth===n.clientWidth)&&(n=document.body);const i={axis:e,container:n,...s};return"function"==typeof t?function(t,e){return function(t){return 2===t.length}(t)?Ua((n=>{t(n[e.axis].progress,n)}),e):ms(t,Ha(e))}(t,i):function(t,e){const n=Ha(e);return t.attachTimeline({timeline:e.target?void 0:n,observe:t=>(t.pause(),ms((e=>{t.time=t.duration*e}),n))})}(t,i)}function Ya(t,e){Boolean(!e||e.current)}const Ga=()=>({scrollX:ys(0),scrollY:ys(0),scrollXProgress:ys(0),scrollYProgress:ys(0)});function Ka({container:e,target:n,layoutEffect:s=!0,...i}={}){const r=g(Ga);return(s?v:t.useEffect)((()=>(Ya(0,n),Ya(0,e),Xa(((t,{x:e,y:n})=>{r.scrollX.set(e.current),r.scrollXProgress.set(e.progress),r.scrollY.set(n.current),r.scrollYProgress.set(n.progress)}),{...i,container:e?.current||void 0,target:n?.current||void 0}))),[e,n,JSON.stringify(i.offset)]),r}function qa(e,n){const s=function(e){const n=g((()=>ys(e))),{isStatic:s}=t.useContext(b);if(s){const[,s]=t.useState(e);t.useEffect((()=>n.on("change",s)),[])}return n}(n()),i=()=>s.set(n());return i(),v((()=>{const t=()=>ut.preRender(i,!1,!0),n=e.map((e=>e.on("change",t)));return()=>{n.forEach((t=>t())),ht(i)}})),s}function _a(t,e,n,s){if("function"==typeof t)return function(t){fs.current=[],t();const e=qa(fs.current,t);return fs.current=void 0,e}(t);const i="function"==typeof e?e:function(...t){const e=!Array.isArray(t[0]),n=e?0:-1,s=t[0+n],i=Oe(t[1+n],t[2+n],t[3+n]);return e?i(s):i}(e,n,s);return Array.isArray(t)?Za(t,i):Za([t],(([t])=>i(t)))}function Za(t,e){const n=g((()=>[]));return qa(t,(()=>{n.length=0;const s=t.length;for(let e=0;e<s;e++)n[e]=t[e].get();return e(n)}))}const Qa=t=>{const e=nl(t),{conflictingClassGroups:n,conflictingClassGroupModifiers:s}=t;return{getClassGroupId:t=>{const n=t.split("-");return""===n[0]&&1!==n.length&&n.shift(),Ja(n,e)||el(t)},getConflictingClassGroupIds:(t,e)=>{const i=n[t]||[];return e&&s[t]?[...i,...s[t]]:i}}},Ja=(t,e)=>{if(0===t.length)return e.classGroupId;const n=t[0],s=e.nextPart.get(n),i=s?Ja(t.slice(1),s):void 0;if(i)return i;if(0===e.validators.length)return;const r=t.join("-");return e.validators.find((({validator:t})=>t(r)))?.classGroupId},tl=/^\[(.+)\]$/,el=t=>{if(tl.test(t)){const e=tl.exec(t)[1],n=e?.substring(0,e.indexOf(":"));if(n)return"arbitrary.."+n}},nl=t=>{const{theme:e,prefix:n}=t,s={nextPart:new Map,validators:[]};return ol(Object.entries(t.classGroups),n).forEach((([t,n])=>{sl(n,s,t,e)})),s},sl=(t,e,n,s)=>{t.forEach((t=>{if("string"!=typeof t){if("function"==typeof t)return rl(t)?void sl(t(s),e,n,s):void e.validators.push({validator:t,classGroupId:n});Object.entries(t).forEach((([t,i])=>{sl(i,il(e,t),n,s)}))}else{(""===t?e:il(e,t)).classGroupId=n}}))},il=(t,e)=>{let n=t;return e.split("-").forEach((t=>{n.nextPart.has(t)||n.nextPart.set(t,{nextPart:new Map,validators:[]}),n=n.nextPart.get(t)})),n},rl=t=>t.isThemeGetter,ol=(t,e)=>e?t.map((([t,n])=>[t,n.map((t=>"string"==typeof t?e+t:"object"==typeof t?Object.fromEntries(Object.entries(t).map((([t,n])=>[e+t,n]))):t))])):t,al=t=>{if(t<1)return{get:()=>{},set:()=>{}};let e=0,n=new Map,s=new Map;const i=(i,r)=>{n.set(i,r),e++,e>t&&(e=0,s=n,n=new Map)};return{get(t){let e=n.get(t);return void 0!==e?e:void 0!==(e=s.get(t))?(i(t,e),e):void 0},set(t,e){n.has(t)?n.set(t,e):i(t,e)}}},ll=t=>{const{separator:e,experimentalParseClassName:n}=t,s=1===e.length,i=e[0],r=e.length,o=t=>{const n=[];let o,a=0,l=0;for(let h=0;h<t.length;h++){let c=t[h];if(0===a){if(c===i&&(s||t.slice(h,h+r)===e)){n.push(t.slice(l,h)),l=h+r;continue}if("/"===c){o=h;continue}}"["===c?a++:"]"===c&&a--}const c=0===n.length?t:t.substring(l),u=c.startsWith("!");return{modifiers:n,hasImportantModifier:u,baseClassName:u?c.substring(1):c,maybePostfixModifierPosition:o&&o>l?o-l:void 0}};return n?t=>n({className:t,parseClassName:o}):o},cl=t=>{if(t.length<=1)return t;const e=[];let n=[];return t.forEach((t=>{"["===t[0]?(e.push(...n.sort(),t),n=[]):n.push(t)})),e.push(...n.sort()),e},ul=/\s+/;function hl(){let t,e,n=0,s="";for(;n<arguments.length;)(t=arguments[n++])&&(e=dl(t))&&(s&&(s+=" "),s+=e);return s}const dl=t=>{if("string"==typeof t)return t;let e,n="";for(let s=0;s<t.length;s++)t[s]&&(e=dl(t[s]))&&(n&&(n+=" "),n+=e);return n};function pl(t,...e){let n,s,i,r=function(a){const l=e.reduce(((t,e)=>e(t)),t());return n=(t=>({cache:al(t.cacheSize),parseClassName:ll(t),...Qa(t)}))(l),s=n.cache.get,i=n.cache.set,r=o,o(a)};function o(t){const e=s(t);if(e)return e;const r=((t,e)=>{const{parseClassName:n,getClassGroupId:s,getConflictingClassGroupIds:i}=e,r=[],o=t.trim().split(ul);let a="";for(let l=o.length-1;l>=0;l-=1){const t=o[l],{modifiers:e,hasImportantModifier:c,baseClassName:u,maybePostfixModifierPosition:h}=n(t);let d=Boolean(h),p=s(d?u.substring(0,h):u);if(!p){if(!d){a=t+(a.length>0?" "+a:a);continue}if(p=s(u),!p){a=t+(a.length>0?" "+a:a);continue}d=!1}const m=cl(e).join(":"),f=c?m+"!":m,g=f+p;if(r.includes(g))continue;r.push(g);const y=i(p,d);for(let n=0;n<y.length;++n){const t=y[n];r.push(f+t)}a=t+(a.length>0?" "+a:a)}return a})(t,n);return i(t,r),r}return function(){return r(hl.apply(null,arguments))}}const ml=t=>{const e=e=>e[t]||[];return e.isThemeGetter=!0,e},fl=/^\[(?:([a-z-]+):)?(.+)\]$/i,gl=/^\d+\/\d+$/,yl=new Set(["px","full","screen"]),vl=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,xl=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,bl=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,wl=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Tl=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Sl=t=>kl(t)||yl.has(t)||gl.test(t),Pl=t=>Ol(t,"length",Il),kl=t=>Boolean(t)&&!Number.isNaN(Number(t)),El=t=>Ol(t,"number",kl),Cl=t=>Boolean(t)&&Number.isInteger(Number(t)),Ml=t=>t.endsWith("%")&&kl(t.slice(0,-1)),jl=t=>fl.test(t),Al=t=>vl.test(t),Vl=new Set(["length","size","percentage"]),Dl=t=>Ol(t,Vl,Wl),Rl=t=>Ol(t,"position",Wl),Ll=new Set(["image","url"]),Nl=t=>Ol(t,Ll,Ul),Bl=t=>Ol(t,"",zl),Fl=()=>!0,Ol=(t,e,n)=>{const s=fl.exec(t);return!!s&&(s[1]?"string"==typeof e?s[1]===e:e.has(s[1]):n(s[2]))},Il=t=>xl.test(t)&&!bl.test(t),Wl=()=>!1,zl=t=>wl.test(t),Ul=t=>Tl.test(t),$l=()=>{const t=ml("colors"),e=ml("spacing"),n=ml("blur"),s=ml("brightness"),i=ml("borderColor"),r=ml("borderRadius"),o=ml("borderSpacing"),a=ml("borderWidth"),l=ml("contrast"),c=ml("grayscale"),u=ml("hueRotate"),h=ml("invert"),d=ml("gap"),p=ml("gradientColorStops"),m=ml("gradientColorStopPositions"),f=ml("inset"),g=ml("margin"),y=ml("opacity"),v=ml("padding"),x=ml("saturate"),b=ml("scale"),w=ml("sepia"),T=ml("skew"),S=ml("space"),P=ml("translate"),k=()=>["auto",jl,e],E=()=>[jl,e],C=()=>["",Sl,Pl],M=()=>["auto",kl,jl],j=()=>["","0",jl],A=()=>[kl,jl];return{cacheSize:500,separator:":",theme:{colors:[Fl],spacing:[Sl,Pl],blur:["none","",Al,jl],brightness:A(),borderColor:[t],borderRadius:["none","","full",Al,jl],borderSpacing:E(),borderWidth:C(),contrast:A(),grayscale:j(),hueRotate:A(),invert:j(),gap:E(),gradientColorStops:[t],gradientColorStopPositions:[Ml,Pl],inset:k(),margin:k(),opacity:A(),padding:E(),saturate:A(),scale:A(),sepia:j(),skew:A(),space:E(),translate:E()},classGroups:{aspect:[{aspect:["auto","square","video",jl]}],container:["container"],columns:[{columns:[Al]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",jl]}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[f]}],"inset-x":[{"inset-x":[f]}],"inset-y":[{"inset-y":[f]}],start:[{start:[f]}],end:[{end:[f]}],top:[{top:[f]}],right:[{right:[f]}],bottom:[{bottom:[f]}],left:[{left:[f]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Cl,jl]}],basis:[{basis:k()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",jl]}],grow:[{grow:j()}],shrink:[{shrink:j()}],order:[{order:["first","last","none",Cl,jl]}],"grid-cols":[{"grid-cols":[Fl]}],"col-start-end":[{col:["auto",{span:["full",Cl,jl]},jl]}],"col-start":[{"col-start":M()}],"col-end":[{"col-end":M()}],"grid-rows":[{"grid-rows":[Fl]}],"row-start-end":[{row:["auto",{span:[Cl,jl]},jl]}],"row-start":[{"row-start":M()}],"row-end":[{"row-end":M()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",jl]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",jl]}],gap:[{gap:[d]}],"gap-x":[{"gap-x":[d]}],"gap-y":[{"gap-y":[d]}],"justify-content":[{justify:["normal","start","end","center","between","around","evenly","stretch"]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal","start","end","center","between","around","evenly","stretch","baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":["start","end","center","between","around","evenly","stretch","baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[S]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[S]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",jl,e]}],"min-w":[{"min-w":[jl,e,"min","max","fit"]}],"max-w":[{"max-w":[jl,e,"none","full","min","max","fit","prose",{screen:[Al]},Al]}],h:[{h:[jl,e,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[jl,e,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[jl,e,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[jl,e,"auto","min","max","fit"]}],"font-size":[{text:["base",Al,Pl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",El]}],"font-family":[{font:[Fl]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",jl]}],"line-clamp":[{"line-clamp":["none",kl,El]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Sl,jl]}],"list-image":[{"list-image":["none",jl]}],"list-style-type":[{list:["none","disc","decimal",jl]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[t]}],"placeholder-opacity":[{"placeholder-opacity":[y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[t]}],"text-opacity":[{"text-opacity":[y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:["solid","dashed","dotted","double","none","wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Sl,Pl]}],"underline-offset":[{"underline-offset":["auto",Sl,jl]}],"text-decoration-color":[{decoration:[t]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:E()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",jl]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",jl]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",Rl]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Dl]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Nl]}],"bg-color":[{bg:[t]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[r]}],"rounded-s":[{"rounded-s":[r]}],"rounded-e":[{"rounded-e":[r]}],"rounded-t":[{"rounded-t":[r]}],"rounded-r":[{"rounded-r":[r]}],"rounded-b":[{"rounded-b":[r]}],"rounded-l":[{"rounded-l":[r]}],"rounded-ss":[{"rounded-ss":[r]}],"rounded-se":[{"rounded-se":[r]}],"rounded-ee":[{"rounded-ee":[r]}],"rounded-es":[{"rounded-es":[r]}],"rounded-tl":[{"rounded-tl":[r]}],"rounded-tr":[{"rounded-tr":[r]}],"rounded-br":[{"rounded-br":[r]}],"rounded-bl":[{"rounded-bl":[r]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[y]}],"border-style":[{border:["solid","dashed","dotted","double","none","hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[y]}],"divide-style":[{divide:["solid","dashed","dotted","double","none"]}],"border-color":[{border:[i]}],"border-color-x":[{"border-x":[i]}],"border-color-y":[{"border-y":[i]}],"border-color-s":[{"border-s":[i]}],"border-color-e":[{"border-e":[i]}],"border-color-t":[{"border-t":[i]}],"border-color-r":[{"border-r":[i]}],"border-color-b":[{"border-b":[i]}],"border-color-l":[{"border-l":[i]}],"divide-color":[{divide:[i]}],"outline-style":[{outline:["","solid","dashed","dotted","double","none"]}],"outline-offset":[{"outline-offset":[Sl,jl]}],"outline-w":[{outline:[Sl,Pl]}],"outline-color":[{outline:[t]}],"ring-w":[{ring:C()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[t]}],"ring-opacity":[{"ring-opacity":[y]}],"ring-offset-w":[{"ring-offset":[Sl,Pl]}],"ring-offset-color":[{"ring-offset":[t]}],shadow:[{shadow:["","inner","none",Al,Bl]}],"shadow-color":[{shadow:[Fl]}],opacity:[{opacity:[y]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[s]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",Al,jl]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[u]}],invert:[{invert:[h]}],saturate:[{saturate:[x]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[s]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u]}],"backdrop-invert":[{"backdrop-invert":[h]}],"backdrop-opacity":[{"backdrop-opacity":[y]}],"backdrop-saturate":[{"backdrop-saturate":[x]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[o]}],"border-spacing-x":[{"border-spacing-x":[o]}],"border-spacing-y":[{"border-spacing-y":[o]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",jl]}],duration:[{duration:A()}],ease:[{ease:["linear","in","out","in-out",jl]}],delay:[{delay:A()}],animate:[{animate:["none","spin","ping","pulse","bounce",jl]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[b]}],"scale-x":[{"scale-x":[b]}],"scale-y":[{"scale-y":[b]}],rotate:[{rotate:[Cl,jl]}],"translate-x":[{"translate-x":[P]}],"translate-y":[{"translate-y":[P]}],"skew-x":[{"skew-x":[T]}],"skew-y":[{"skew-y":[T]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",jl]}],accent:[{accent:["auto",t]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",jl]}],"caret-color":[{caret:[t]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":E()}],"scroll-mx":[{"scroll-mx":E()}],"scroll-my":[{"scroll-my":E()}],"scroll-ms":[{"scroll-ms":E()}],"scroll-me":[{"scroll-me":E()}],"scroll-mt":[{"scroll-mt":E()}],"scroll-mr":[{"scroll-mr":E()}],"scroll-mb":[{"scroll-mb":E()}],"scroll-ml":[{"scroll-ml":E()}],"scroll-p":[{"scroll-p":E()}],"scroll-px":[{"scroll-px":E()}],"scroll-py":[{"scroll-py":E()}],"scroll-ps":[{"scroll-ps":E()}],"scroll-pe":[{"scroll-pe":E()}],"scroll-pt":[{"scroll-pt":E()}],"scroll-pr":[{"scroll-pr":E()}],"scroll-pb":[{"scroll-pb":E()}],"scroll-pl":[{"scroll-pl":E()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",jl]}],fill:[{fill:[t,"none"]}],"stroke-w":[{stroke:[Sl,Pl,El]}],stroke:[{stroke:[t,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},Hl=pl($l);function Xl(...t){return Hl(n(t))}const Yl=({children:n,className:s="",id:i,fullWidth:r=!1,background:o="default",animate:a=!1,delay:l=0,animationStyle:c="fade",onMouseEnter:u,onMouseLeave:h})=>{const d=t.useRef(null),[p,m]=t.useState(!1),f=t.useRef("undefined"!=typeof window&&window.matchMedia("(prefers-reduced-motion: reduce)").matches);return t.useEffect((()=>{if(!a||f.current)return void m(!0);const t=new IntersectionObserver((([e])=>{e.isIntersecting&&(setTimeout((()=>{m(!0)}),l),t.unobserve(e.target))}),{threshold:.1,rootMargin:"0px 0px -10% 0px"});return d.current&&t.observe(d.current),()=>{d.current&&t.unobserve(d.current)}}),[a,l]),t.useEffect((()=>{if("undefined"==typeof window)return;const t=window.matchMedia("(prefers-reduced-motion: reduce)"),e=t=>{f.current=t.matches,t.matches&&m(!0)};return t.addEventListener("change",e),()=>{t.removeEventListener("change",e)}}),[]),e.jsxs("section",{id:i,ref:d,className:Xl("py-8 xs:py-10 sm:py-12 md:py-16 lg:py-20 relative overflow-hidden",s,a?"transform transition-all duration-700":"",(()=>{if(!a||f.current)return"";if(!p)switch(c){case"fade":return"opacity-0";case"slide-up":return"opacity-0 translate-y-10";case"slide-down":return"opacity-0 -translate-y-10";case"scale":return"opacity-0 scale-95";default:return""}return"opacity-100 translate-y-0 scale-100"})()),style:{transitionDelay:`${l}ms`},onMouseEnter:u,onMouseLeave:h,children:[e.jsx("div",{className:Xl("relative z-10",r?"w-full":"container mx-auto px-3 xs:px-4 sm:px-5 md:px-6 lg:px-8"),children:n}),i&&e.jsx("div",{className:"absolute left-0 top-1/2 w-1 h-16 -translate-y-1/2 transition-all duration-500 \n                      "+(p?"bg-green-bright/50 shadow-[0_0_10px_rgba(0,255,140,0.6)]":"bg-transparent"),"aria-hidden":"true"})]})},Gl=[{text:"NZ Owned & Operated",icon:s,description:"A New Zealand company understanding local SME needs"},{text:"Enterprise-Grade Protection",icon:i,description:"Top-tier security without enterprise complexity"},{text:"Rapid 2-4 Week Implementation",icon:r,description:"Quick security improvements with minimal disruption"},{text:"SME-Focused Solutions",icon:o,description:"Tailored cybersecurity for small and medium enterprises"}],Kl=a.memo((({loaded:n,animDelays:s})=>{const[a,d]=t.useState(null),p={hidden:{opacity:0,x:-20},visible:{opacity:1,x:0,transition:{duration:.5}}};return e.jsxs(xa.div,{initial:"hidden",animate:n?"visible":"hidden",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},className:"space-y-3 xs:space-y-4 sm:space-y-5 md:space-y-6 order-2 md:order-1 md:pr-0 lg:pr-6 xl:pr-12",children:[e.jsx(xa.div,{variants:p,className:"relative",children:e.jsxs("span",{className:"font-mono uppercase tracking-wider text-[10px] xs:text-xs bg-green-dark/60 text-green-200 px-3 py-1.5 rounded-full inline-flex items-center gap-1.5 shadow-sm",children:[e.jsx(i,{className:"w-3 h-3 text-green-200"}),"SME Cybersecurity Specialists"]})}),e.jsx(xa.div,{variants:p,className:"relative",children:e.jsx(l,{to:"/services",className:"group block bg-gradient-to-r from-green-dark/30 to-green-dark/20 border border-green-muted/40 rounded-lg p-3 xs:p-4 hover:border-green-bright/60 transition-all duration-300",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"flex-shrink-0 w-10 h-10 bg-green-dark/50 rounded-lg flex items-center justify-center border border-green-muted/30",children:e.jsx(o,{className:"w-5 h-5 text-green-bright"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("span",{className:"text-[10px] xs:text-xs font-mono uppercase tracking-wider text-green-bright bg-green-dark/40 px-2 py-0.5 rounded",children:"Comprehensive Services"}),e.jsx("span",{className:"text-[10px] xs:text-xs text-white/60",children:"Available Now"})]}),e.jsx("h3",{className:"text-sm xs:text-base font-semibold text-white mb-1 group-hover:text-green-bright transition-colors",children:"Security Assessments, Audits & Emergency Response"}),e.jsx("p",{className:"text-xs xs:text-sm text-white/70 mb-2",children:"From vulnerability assessments to incident response - we've got you covered"}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-green-bright",children:[e.jsx("span",{children:"Explore our services"}),e.jsx(c,{className:"w-3 h-3 group-hover:translate-x-0.5 transition-transform"})]})]})]})})}),e.jsx(xa.div,{variants:p,children:e.jsxs("h1",{className:"text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold leading-tight",children:[e.jsx("span",{className:"block mb-2",children:"SME Cybersecurity"}),e.jsxs("span",{className:"text-green-bright relative inline-block",children:["Done RIGHT",e.jsx("span",{className:"absolute -bottom-1 left-0 w-full h-[3px] bg-gradient-to-r from-green-bright to-transparent"})]})]})}),e.jsxs(xa.p,{variants:p,className:"text-base xs:text-lg sm:text-xl text-white/90 leading-relaxed max-w-xl",children:["Enterprise-grade cybersecurity designed specifically for SMEs. We deliver comprehensive security assessments, rapid incident response, and ongoing protection that ",e.jsx("strong",{className:"text-green-bright",children:"actually works"})," for your business size and budget."]}),e.jsx(xa.div,{variants:p,className:"flex flex-wrap gap-2 xs:gap-3 items-center",children:Gl.slice(0,4).map(((t,n)=>{const s=t.icon,i=a===n;return e.jsxs("div",{className:"flex items-center gap-2 px-3 xs:px-4 py-2 xs:py-2.5 rounded-md text-xs xs:text-sm transition-all duration-300 "+(i?"bg-green-dark/50 shadow-[0_0_10px_rgba(0,255,140,0.2)]":"bg-green-dark/20"),onMouseEnter:()=>d(n),onMouseLeave:()=>d(null),children:[e.jsx(s,{className:`h-3.5 w-3.5 xs:h-4 xs:w-4 ${i?"text-green-bright":"text-green-bright/80"} transition-colors duration-300`}),e.jsx("span",{className:(i?"text-white":"text-white/80")+" transition-colors duration-300",children:t.text})]},n)}))}),e.jsxs(xa.div,{variants:p,className:"flex items-start xs:items-center gap-3 p-3 xs:p-4 bg-gradient-to-r from-blue-900/30 to-green-900/30 border border-green-500/30 rounded-md text-sm xs:text-base max-w-xl shadow-lg",children:[e.jsx(r,{className:"h-5 w-5 xs:h-6 xs:w-6 text-green-400 flex-shrink-0 mt-0.5 xs:mt-0"}),e.jsxs("p",{className:"text-white/90",children:[e.jsx("strong",{children:"Why SMEs Choose Us:"})," We understand that small and medium enterprises need enterprise-level protection without enterprise-level complexity or cost. Get started in just 2-4 weeks."]})]}),e.jsxs(xa.div,{variants:p,className:"pt-4 xs:pt-6 flex flex-col xs:flex-row flex-wrap gap-4",children:[e.jsx(l,{to:"/phishing-assessment",className:"group relative overflow-hidden bg-gradient-to-r from-green-500 to-green-600 text-white font-bold rounded-md inline-flex items-center px-6 py-3 text-base shadow-lg hover:from-green-400 hover:to-green-500 transition-all duration-300",children:e.jsxs("span",{className:"relative z-10 flex items-center gap-2",children:[e.jsx(i,{className:"w-5 h-5"}),"Free Security Assessment"]})}),e.jsx(l,{to:"/services",className:"group relative overflow-hidden bg-green-bright text-black font-medium rounded-md inline-flex items-center px-5 py-3 text-base hover:bg-green-muted transition-colors",children:e.jsxs("span",{className:"relative z-10 flex items-center gap-2",children:[e.jsx(o,{className:"w-5 h-5"}),e.jsx("span",{children:"View All Services"})]})}),e.jsxs("a",{href:"https://calendly.com/blackveil",target:"_blank",rel:"noopener noreferrer",className:"group relative inline-flex items-center bg-transparent border border-green-400/50 text-green-200 hover:text-green-100 hover:border-green-300 px-5 py-3 rounded-md font-medium text-base transition-all duration-300",children:[e.jsx(u,{className:"w-5 h-5 mr-2"}),e.jsx("span",{children:"Book Consultation"}),e.jsx(h,{className:"w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1"})]})]})]})})),ql={hidden:{opacity:0},visible:{opacity:1,transition:{duration:.5,staggerChildren:.1}}},_l={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5}}},Zl={initial:{scale:1,opacity:.8},animate:{scale:[1,1.05,1],opacity:[.8,1,.8],transition:{duration:3,repeat:1/0,ease:"easeInOut"}}},Ql={initial:{y:-5,opacity:.3},animate:{y:100,opacity:[.3,.7,.3],transition:{duration:1.5,repeat:1/0,ease:"linear"}}},Jl=()=>e.jsxs("div",{className:"absolute inset-0 pointer-events-none",children:[e.jsxs(xa.div,{className:"absolute bottom-0 left-0 right-0 p-3 xs:p-4",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:1.2,duration:.5},children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx("div",{className:"w-2 h-2 bg-green-bright rounded-full mr-2 animate-pulse"}),e.jsx("p",{className:"font-mono text-[10px] xs:text-xs text-white/80 tracking-wide",children:"REAL-TIME PROTECTION ACTIVE"})]}),e.jsx(xa.p,{className:"font-mono text-[10px] xs:text-xs text-green-bright/90 tracking-wide mb-2",initial:{opacity:0},animate:{opacity:1},transition:{delay:1.4,duration:.5},children:"EMAIL SECURITY MONITORING"}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"text-[9px] xs:text-[10px] font-mono text-white/60",children:["STATUS: ",e.jsx("span",{className:"text-green-bright/90",children:"SECURE"})]}),e.jsx(xa.div,{className:"flex space-x-1.5",initial:{opacity:0},animate:{opacity:1},transition:{delay:1.6,duration:.5},children:[1,2,3].map((t=>e.jsx(xa.div,{className:"w-5 h-0.5 bg-green-bright/50 rounded-sm",initial:{scaleX:0},animate:{scaleX:1},transition:{delay:1.6+.1*t,duration:.3}},t)))})]})]}),e.jsx("div",{className:"absolute inset-0 bg-cyber-grid opacity-10"})]}),tc=a.memo((({loaded:t,animDelay:n})=>e.jsxs(xa.div,{initial:"hidden",animate:t?"visible":"hidden",variants:ql,className:"relative mt-0 mb-0 order-1 md:order-2",style:{perspective:1e3},children:[e.jsxs(xa.div,{className:"relative rounded-lg overflow-hidden border border-green-muted/30 bg-black-soft/30 backdrop-blur-sm \n                   p-4 xs:p-6 md:p-8 aspect-[4/3]\n                   hover:border-green-muted/50 transition-all duration-500",variants:_l,whileHover:{boxShadow:"0 0 30px rgba(0, 255, 140, 0.15)",translateY:-5,transition:{duration:.3}},children:[e.jsxs("div",{className:"relative flex flex-col items-center justify-center h-full",children:[e.jsx(xa.div,{className:"absolute inset-0 rounded-full bg-green-bright/10 blur-[80px] z-0",variants:Zl,initial:"initial",animate:"animate"}),e.jsxs(xa.div,{className:"relative z-10 mb-4",variants:Zl,initial:"initial",animate:"animate",children:[e.jsx(d,{className:"w-28 h-28 xs:w-36 xs:h-36 md:w-48 md:h-48 text-green-bright/90",strokeWidth:1.25,"aria-hidden":"true"}),e.jsx(xa.div,{className:"absolute -right-10 top-5 bg-black-soft/90 border border-green-muted/40 rounded-full p-2",initial:{scale:.5,opacity:0},animate:{scale:1,opacity:1},transition:{delay:.8,duration:.5},children:e.jsx(p,{className:"w-5 h-5 text-green-bright/80"})}),e.jsx(xa.div,{className:"absolute -left-8 bottom-12 bg-black-soft/90 border border-green-muted/40 rounded-full p-2",initial:{scale:.5,opacity:0},animate:{scale:1,opacity:1},transition:{delay:1,duration:.5},children:e.jsx(m,{className:"w-4 h-4 text-green-bright/80"})}),e.jsx(xa.div,{className:"absolute -left-12 -top-2 opacity-30",initial:{opacity:0},animate:{opacity:.3},transition:{delay:1.2,duration:.8},children:e.jsxs("svg",{width:"24",height:"40",viewBox:"0 0 24 40",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M12,0 C14,5 10,10 12,15 C14,20 10,25 12,30 C14,35 10,40 12,40",stroke:"#00ff8c",strokeWidth:"0.5",fill:"none"}),e.jsx("path",{d:"M12,15 C14,12 16,15 18,12",stroke:"#00ff8c",strokeWidth:"0.3",fill:"none"}),e.jsx("path",{d:"M12,25 C14,22 16,25 18,22",stroke:"#00ff8c",strokeWidth:"0.3",fill:"none"}),e.jsx("path",{d:"M12,15 C10,12 8,15 6,12",stroke:"#00ff8c",strokeWidth:"0.3",fill:"none"}),e.jsx("path",{d:"M12,25 C10,22 8,25 6,22",stroke:"#00ff8c",strokeWidth:"0.3",fill:"none"})]})})]}),e.jsx(xa.div,{className:"absolute left-0 right-0 h-[2px] bg-green-bright/40 z-20",variants:Ql,initial:"initial",animate:"animate"})]}),e.jsx(Jl,{})]}),e.jsx("div",{className:"absolute -top-3 -left-3 w-12 h-12 border-t-2 border-l-2 border-green-bright/20 rounded-tl-xl"}),e.jsx("div",{className:"absolute -bottom-3 -right-3 w-12 h-12 border-b-2 border-r-2 border-green-bright/20 rounded-br-xl"}),e.jsxs("svg",{className:"absolute -z-10 top-0 left-0 w-full h-full opacity-20",viewBox:"0 0 200 150",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx(xa.path,{d:"M0,75 Q50,60 100,75 T200,75",stroke:"#00ff8c",strokeWidth:"0.5",fill:"none",initial:{pathLength:0,opacity:0},animate:{pathLength:1,opacity:.2},transition:{duration:2,delay:1.5}}),e.jsx(xa.path,{d:"M0,40 Q50,55 100,40 T200,40",stroke:"#00ff8c",strokeWidth:"0.5",fill:"none",initial:{pathLength:0,opacity:0},animate:{pathLength:1,opacity:.2},transition:{duration:2,delay:1.8}}),e.jsx(xa.path,{d:"M0,110 Q50,95 100,110 T200,110",stroke:"#00ff8c",strokeWidth:"0.5",fill:"none",initial:{pathLength:0,opacity:0},animate:{pathLength:1,opacity:.2},transition:{duration:2,delay:2.1}})]})]}))),ec=()=>e.jsxs("div",{className:"absolute inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90 transition-all duration-700",children:[e.jsxs("svg",{width:"90%",height:"90%",viewBox:"0 0 800 400",className:"mx-auto",style:{maxWidth:900,maxHeight:400},children:[e.jsx("line",{x1:"100",y1:"50",x2:"100",y2:"350",stroke:"#0f0",strokeWidth:"8"}),e.jsx("line",{x1:"700",y1:"50",x2:"700",y2:"350",stroke:"#0f0",strokeWidth:"8"}),[0,1,2,3,4].map((t=>e.jsxs("g",{children:[e.jsx("line",{x1:"100",y1:90+60*t,x2:"700",y2:90+60*t,stroke:"#0f0",strokeWidth:"5",className:"ladder-rung"}),e.jsx("rect",{x:250+80*t,y:70+60*t,width:"60",height:"40",rx:"10",fill:"#111",stroke:"#0f0",strokeWidth:"3",className:"gate-box"}),e.jsx(xa.rect,{x:250+80*t,y:70+60*t,width:"60",height:"40",rx:"10",fill:"red",initial:{opacity:0},animate:{opacity:[0,0,1,1,.5,.2]},transition:{delay:.5+.7*t,duration:2,repeat:0},style:{mixBlendMode:"screen"}}),e.jsx(xa.text,{x:280+80*t,y:95+60*t,textAnchor:"middle",fontSize:"32",fill:"red",fontWeight:"bold",initial:{opacity:0},animate:{opacity:[0,0,1,1,.5,.2]},transition:{delay:.7+.7*t,duration:1.5,repeat:0},children:"✗"})]},t))),e.jsx("text",{x:"400",y:"40",textAnchor:"middle",fontSize:"32",fill:"#0f0",fontWeight:"bold",children:"PLC Ladder Logic"})]}),e.jsx(xa.div,{className:"absolute inset-0 flex items-center justify-center pointer-events-none",initial:{opacity:0},animate:{opacity:[0,1,1,.8,1]},transition:{duration:2,repeat:1/0,repeatType:"reverse"},children:e.jsx("div",{className:"bg-black bg-opacity-80 rounded-xl px-8 py-6 border-4 border-red-600 shadow-2xl",children:e.jsx("div",{className:"text-2xl md:text-4xl font-mono text-red-500 tracking-widest text-center select-none",style:{textShadow:"0 0 16px #f00"},children:"53 79 73 74 65 6d 20 43 6f 6d 70 72 6f 6d 69 73 65 64"})})})]}),nc=({stuxnetCascadeActive:n=!1})=>{const[s,i]=t.useState(!1);t.useEffect((()=>{const t=setTimeout((()=>{i(!0)}),50);return()=>clearTimeout(t)}),[]);const r={badge:"0.1s",heading:"0.2s",divider:"0.25s",description:"0.3s",features:"0.4s",warning:"0.5s",cta:"0.6s",image:"0.3s"};return e.jsxs(Yl,{className:"pt-10 md:pt-24 pb-10 md:pb-28 overflow-hidden relative",children:[n&&e.jsx(ec,{}),!n&&e.jsxs("div",{className:"absolute inset-0 pointer-events-none",children:[e.jsx(xa.div,{className:"absolute top-0 right-0 w-3/4 h-3/4 bg-green-bright/5 rounded-full blur-[120px] -translate-y-1/2 translate-x-1/2",animate:{opacity:[.3,.5,.3],scale:[1,1.05,1]},transition:{duration:8,repeat:1/0,ease:"easeInOut"}}),e.jsx(xa.div,{className:"absolute bottom-0 left-0 w-1/2 h-1/2 bg-green-bright/3 rounded-full blur-[80px] translate-y-1/4 -translate-x-1/4",animate:{opacity:[.2,.4,.2],scale:[1,1.03,1]},transition:{duration:6,repeat:1/0,ease:"easeInOut",delay:1}}),e.jsx("div",{className:"absolute opacity-5 bottom-10 right-10 hidden md:block",children:e.jsxs("svg",{width:"200",height:"300",viewBox:"0 0 100 150",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M50,10 C60,30 40,40 50,50 C60,60 40,70 50,80 C60,90 40,100 50,110 C60,120 40,130 50,140",stroke:"currentColor",strokeWidth:"1",className:"text-green-bright"}),e.jsx("path",{d:"M50,50 C55,45 60,50 65,45 C70,40 75,45 80,40",stroke:"currentColor",strokeWidth:"0.7",className:"text-green-bright"}),e.jsx("path",{d:"M50,80 C55,75 60,80 65,75 C70,70 75,75 80,70",stroke:"currentColor",strokeWidth:"0.7",className:"text-green-bright"}),e.jsx("path",{d:"M50,110 C55,105 60,110 65,105 C70,100 75,105 80,100",stroke:"currentColor",strokeWidth:"0.7",className:"text-green-bright"}),e.jsx("path",{d:"M50,50 C45,45 40,50 35,45 C30,40 25,45 20,40",stroke:"currentColor",strokeWidth:"0.7",className:"text-green-bright"}),e.jsx("path",{d:"M50,80 C45,75 40,80 35,75 C30,70 25,75 20,70",stroke:"currentColor",strokeWidth:"0.7",className:"text-green-bright"}),e.jsx("path",{d:"M50,110 C45,105 40,110 35,105 C30,100 25,105 20,100",stroke:"currentColor",strokeWidth:"0.7",className:"text-green-bright"})]})})]}),e.jsxs(xa.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.8},className:"grid md:grid-cols-2 gap-8 md:gap-16 items-center",children:[e.jsx(Kl,{loaded:s,animDelays:r}),e.jsx(tc,{loaded:s,animDelay:r.image})]})]})};export{M as A,nc as H,Yl as S,_a as a,Xl as c,xa as m,Ka as u};
