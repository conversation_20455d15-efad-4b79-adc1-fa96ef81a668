import{j as a}from"./vendor-TvU2VNGP.js";import{l as t}from"./index-gHFbPIvn.js";import{c as e}from"./core-BZ2etCil.js";const o=({children:o,animation:r="fade-up",delay:s=0,className:i,threshold:l,rootMargin:n})=>{const{ref:c,isVisible:d}=t({threshold:l,rootMargin:n,delay:s});return a.jsx("div",{ref:c,className:e("transform-gpu transition-all duration-700 ease-out",d?"translate-y-0 translate-x-0 scale-100 opacity-100 rotateX-0":{"fade-up":"translate-y-10 opacity-0","fade-down":"-translate-y-10 opacity-0","fade-left":"translate-x-10 opacity-0","fade-right":"-translate-x-10 opacity-0",zoom:"scale-95 opacity-0",flip:"rotateX-90 opacity-0"}[r],i),style:{transitionDelay:`${s}ms`},children:o})};export{o as A};
