.spectre-glitch{animation:spectre-glitch-anim .4s linear;filter:contrast(1.2) brightness(1.1) blur(1px);position:relative}@keyframes spectre-glitch-anim{0%{transform:none;filter:none}10%{transform:skew(-2deg) scaleY(1.01) translate(-2px);filter:hue-rotate(10deg) blur(1.5px)}20%{transform:skew(2deg) scaleY(.99) translate(2px);filter:hue-rotate(-10deg) blur(1.5px)}30%{transform:skew(-1deg) scaleY(1.02) translate(-1px);filter:hue-rotate(5deg) blur(1px)}40%{transform:skew(1deg) scaleY(.98) translate(1px);filter:hue-rotate(-5deg) blur(1px)}50%{transform:none;filter:none}to{transform:none;filter:none}}.spectre-glitch-overlay{pointer-events:none;position:absolute;inset:0;z-index:30;background:repeating-linear-gradient(120deg,rgba(255,255,255,.07) 0 2px,transparent 2px 6px);mix-blend-mode:lighten;opacity:.7;animation:spectre-overlay-flicker .4s linear}@keyframes spectre-overlay-flicker{0%,to{opacity:.7}20%{opacity:.9}40%{opacity:.5}60%{opacity:.8}80%{opacity:.6}}.spectre-tooltip{position:absolute;left:50%;top:0;transform:translate(-50%,-120%);background:#181c24;color:#fff;border:1.5px solid #4fd1c5;border-radius:8px;padding:.75em 1.25em;font-size:1rem;box-shadow:0 4px 24px #0000002e;z-index:40;white-space:pre-line;animation:spectre-tooltip-fadein .25s cubic-bezier(.4,2,.6,1);min-width:260px;text-align:center}@keyframes spectre-tooltip-fadein{0%{opacity:0;transform:translate(-50%,-140%) scale(.95)}to{opacity:1;transform:translate(-50%,-120%) scale(1)}}
