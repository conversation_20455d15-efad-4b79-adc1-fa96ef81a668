import{r,j as o}from"./vendor-TvU2VNGP.js";import{P as a}from"./ui-qGggI9tr.js";import{c as e}from"./core-BZ2etCil.js";const i={xs:375,sm:640,md:768,lg:1024,xl:1280,"2xl":1536};var t="horizontal",n=["horizontal","vertical"],s=r.forwardRef(((r,e)=>{const{decorative:i,orientation:s=t,...l}=r,d=function(r){return n.includes(r)}(s)?s:t,c=i?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"};return o.jsx(a.div,{"data-orientation":d,...c,...l,ref:e})}));s.displayName="Separator";var l=s;const d=r.forwardRef((({className:r,orientation:a="horizontal",decorative:i=!0,...t},n)=>o.jsx(l,{ref:n,decorative:i,orientation:a,className:e("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",r),...t})));d.displayName=l.displayName;export{d as S,i as b};
