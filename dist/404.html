
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Blackveil - Page Not Found</title>
  <script>
    // Single Page Apps for GitHub Pages
    // MIT License
    // https://github.com/rafgraph/spa-github-pages
    // This script takes the current URL and converts the path and query
    // string into just a query string, and then redirects the browser
    // to the new URL with only a query string and hash fragment.
    
    var pathSegmentsToKeep = 0;

    var l = window.location;
    
    // Rewrite mechanism for client-side navigation
    if (l.pathname.startsWith('/blog/') && !l.pathname.endsWith('/')) {
      // For blog post URLs, add a trailing slash to make sure we're serving the index.html
      l.replace(
        l.protocol + '//' + l.hostname + (l.port ? ':' + l.port : '') +
        l.pathname + '/' + (l.search ? l.search : '') + (l.hash ? l.hash : '')
      );
    } else {
      // Regular SPA redirect behavior
      l.replace(
        l.protocol + '//' + l.hostname + (l.port ? ':' + l.port : '') +
        l.pathname.split('/').slice(0, 1 + pathSegmentsTo<PERSON>eep).join('/') + '/?/' +
        l.pathname.slice(1).split('/').slice(pathSegmentsToKeep).join('/').replace(/&/g, '~and~') +
        (l.search ? '&' + l.search.slice(1).replace(/&/g, '~and~') : '') +
        l.hash
      );
    }
  </script>
</head>
<body>
  <h2>Redirecting...</h2>
</body>
</html>
