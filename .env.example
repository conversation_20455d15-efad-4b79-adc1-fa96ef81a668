# Supabase Configuration
# Copy this file to .env.local and fill in your actual Supabase project credentials

# Your Supabase project URL
VITE_SUPABASE_URL=https://your-project-id.supabase.co

# Your Supabase anonymous/public key (safe for client-side use)
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key-here

# Cloudflare API Configuration (Optional)
# For real threat intelligence data from Cloudflare Radar API
# Get your token from: https://dash.cloudflare.com/profile/api-tokens
# Required permissions: Zone:Zone:Read, Zone:Analytics:Read
# Note: This should be set as a Supabase secret, not in .env files
# Command: supabase secrets set CLOUDFLARE_API_TOKEN=your_token_here
# CLOUDFLARE_API_TOKEN=your-cloudflare-api-token-here

# Note: Never commit actual credentials to version control
# The .env.local file is automatically ignored by git
