# Blackveil Marketing Website

A modern, responsive marketing website for Blackveil, a cybersecurity company specializing in email security and protection services for New Zealand businesses.

## Overview

The Blackveil marketing website is built using React, TypeScript, and Tailwind CSS. It features a sleek, cybersecurity-themed design with interactive elements and comprehensive information about the company's services.

## Features

- **Modern Design**: Cyber-themed UI with animated elements and responsive layout
- **Performance Optimized**: Fast loading times with optimized assets, code splitting, and advanced image optimization
- **Image Optimization**: Automated WebP conversion, compression, and responsive delivery with 85% size reduction
- **SEO Friendly**: Structured data, meta tags, image sitemaps, and semantic HTML for better search engine visibility
- **Accessibility Compliant**: WCAG 2.1 AA compliant with comprehensive alt text and screen reader support
- **Mobile Responsive**: Fully responsive design that works on all device sizes
- **Business Intelligence Dashboard**: Advanced analytics and lead management system for cybersecurity assessments
- **Real-time Threat Intelligence**: Live Cloudflare Radar API integration for current threat landscape data
- **Blog Section**: Informative blog posts about email security with New Zealand focus
- **Contact Form**: Integrated contact form with validation and submission handling
- **Security Assessments**: Interactive cybersecurity and DMARC compliance assessments with lead scoring
- **Business Intelligence**: Admin analytics dashboard with customer journey tracking and A/B testing

## Technology Stack

- **Frontend Framework**: React with TypeScript
- **Build Tool**: Vite for fast development and optimized production builds
- **Styling**: Tailwind CSS with custom theme
- **UI Components**: shadcn-ui for accessible and customizable components
- **Routing**: React Router for client-side navigation
- **Form Handling**: React Hook Form with validation
- **Database**: Supabase (PostgreSQL) with Row Level Security
- **Analytics**: Plausible.io (privacy-focused analytics)

## Project Structure

```text
src/
├── components/       # UI components
│   ├── blog/         # Blog-related components
│   ├── home/         # Homepage components
│   ├── navbar/       # Navigation components
│   └── ui/           # Reusable UI components
├── data/             # Static data files
├── hooks/            # Custom React hooks
├── integrations/     # External service integrations (Supabase)
├── lib/              # Utility libraries
├── models/           # TypeScript interfaces and types
├── pages/            # Page components
├── utils/            # Utility functions
└── main.tsx          # Application entry point

scripts/              # Database and utility scripts
├── populate-assessments-final-fix-corrected.sql  # Assessment questions population
├── populate-assessment-options-final-fix.sql     # Question options population
├── check-database-content.js                     # Database verification
├── image-audit.js                                 # Automated image analysis and reporting
├── compress-images.js                             # High-quality image compression
├── convert-to-webp.js                             # Batch PNG to WebP conversion
└── generate-image-sitemap.js                     # SEO-optimized image sitemap generation
```

## Development Guidelines

### Code Style

- Use TypeScript for type safety
- Follow functional component patterns with hooks
- Use named exports for better code readability
- Maintain consistent naming conventions

### Component Structure

- Components should be modular and reusable
- Props should be well-typed with TypeScript interfaces
- Use composition over inheritance
- Keep components focused on a single responsibility

### Styling

- Use Tailwind CSS utility classes for styling
- Follow mobile-first approach for responsive design
- Maintain consistent spacing and typography
- Use the defined color palette for consistency

### Performance

- Optimize images and assets using automated compression and WebP conversion
- Use lazy loading for non-critical components
- Minimize bundle size with code splitting
- Follow React best practices for rendering optimization
- Leverage image CDN utilities for responsive delivery

## Content Guidelines

### Brand Voice

- Professional but approachable
- Authoritative on security topics
- Clear and jargon-free (avoid technical terms when possible)
- Focus on solutions rather than fear

### Blog Content

- Focus on email security topics
- Include New Zealand-specific context and examples
- Avoid AI-generated content markers (technical jargon, repetitive phrases)
- Provide actionable advice and practical solutions

## Image Optimization

The BlackVeil platform includes a comprehensive image optimization system that automatically improves performance, accessibility, and SEO.

### Automated Optimization Features

- **WebP Conversion**: Automatic conversion of PNG/JPEG files to WebP format (50%+ size reduction)
- **Image Compression**: High-quality compression using Sharp library (up to 96% size reduction)
- **Responsive Delivery**: Multiple image sizes for different screen resolutions
- **Lazy Loading**: Intersection Observer-based lazy loading for better performance
- **Accessibility**: Comprehensive alt text generation for WCAG 2.1 AA compliance
- **SEO Enhancement**: Structured data and image sitemaps for better search indexing

### Image Optimization Scripts

Run these scripts to optimize images:

```bash
# Audit current image usage and identify issues
node scripts/image-audit.js

# Compress oversized images (>2MB)
node scripts/compress-images.js

# Convert PNG files to WebP format
node scripts/convert-to-webp.js

# Generate SEO-optimized image sitemap
node scripts/generate-image-sitemap.js
```

### Performance Results

- **85% total image size reduction** across optimized files
- **96% compression** on oversized images (7.04MB → 0.28MB)
- **50.7% average WebP savings** across 13 converted files
- **100% alt text coverage** for accessibility compliance
- **43 images indexed** in SEO-optimized sitemap

### Best Practices

- All new images should include descriptive alt text
- Compress images before adding to repository
- Generate WebP versions for PNG/JPEG files
- Update image sitemap when adding new content
- Run monthly audits to maintain optimization

## Security Considerations

- No API keys or sensitive information in the codebase
- Content Security Policy implementation
- Secure form handling
- Regular security audits

## Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables:

   ```bash
   cp .env.example .env.local
   # Edit .env.local with your actual Supabase credentials
   ```

4. Start the development server: `npm run dev`
   The site runs at [http://localhost:8080](http://localhost:8080)
5. Build for production: `npm run build`

### Environment Variables

The application requires the following environment variables:

- `VITE_SUPABASE_URL`: Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY`: Your Supabase anonymous/public key

Copy `.env.example` to `.env.local` and fill in your actual credentials. Never commit the `.env.local` file to version control.

## Database Setup

The application uses Supabase (PostgreSQL) for data storage with the following key tables:

- `assessment_types`: Available assessment types (cybersecurity-maturity, dmarc-compliance)
- `assessment_questions`: Questions for each assessment type
- `assessment_question_options`: Multiple choice options for each question
- `assessment_submissions`: User assessment submissions
- `assessment_answers`: Individual question answers
- `lead_scores`: Calculated risk scores and lead priorities

### Populating Assessment Questions

If you need to populate the assessment questions (e.g., for a new database), run these SQL scripts in the Supabase SQL Editor:

1. **First**, run `scripts/populate-assessments-final-fix-corrected.sql` to create the questions
2. **Then**, run `scripts/populate-assessment-options-final-fix.sql` to add the answer options

These scripts include duplicate prevention and will only insert missing data.

### Database Verification

To verify the database content, you can run:

```bash
node scripts/check-database-content.js
```

This will show you the current state of assessment types, questions, and options.

## Business Intelligence Dashboard

The platform includes a comprehensive BI dashboard for analyzing cybersecurity assessment data and managing leads.

### Dashboard Features

- **Lead Management**: View and manage assessment submissions with automated risk scoring
- **Analytics Dashboard**: Key metrics, industry breakdowns, and risk distribution analysis
- **Customer Journey Tracking**: User behavior analytics and conversion funnel analysis
- **A/B Testing Framework**: Test different variations to optimize conversion rates
- **Performance Monitoring**: Database health checks and query optimization

### Advanced BI Setup

For enhanced BI features, execute these additional SQL scripts in Supabase:

#### 1. Create Advanced BI Tables

```sql
-- Execute scripts/create-advanced-bi-schema.sql in Supabase SQL Editor
-- This creates: enhanced_lead_scores, user_journey_events, ab_tests, ab_test_variants, ab_test_participations
```

#### 2. Fix Missing Question Options

```sql
-- Execute scripts/fix-missing-question-options.sql in Supabase SQL Editor
-- This ensures all 61 questions have complete answer options
```

#### 3. Populate Sample Data

For development and testing:

```bash
cd scripts
node populate-existing-data.js
```

### Accessing the Dashboard

Navigate to `/admin` in your application to access the BI dashboard. The dashboard includes:

- **Lead Management Tab**: View all assessment submissions with risk scoring
- **Analytics Tab**: Comprehensive metrics and visualizations
- **A/B Testing Tab**: Manage and monitor conversion tests
- **Settings Tab**: Data population utilities and system health

### Maintenance and Monitoring

#### Automated Database Maintenance

```bash
# Full maintenance (recommended weekly)
node scripts/database-maintenance.js full

# Individual operations
node scripts/database-maintenance.js archive      # Archive old submissions
node scripts/database-maintenance.js performance # Performance monitoring
node scripts/database-maintenance.js report      # Generate maintenance report
```

#### Troubleshooting BI Dashboard

1. **Missing Tables Error**: Ensure advanced BI schema scripts have been executed
2. **No Data in Dashboard**: Run sample data population script
3. **Slow Performance**: Execute performance monitoring script
4. **TypeScript Errors**: Verify types.ts includes all table definitions

## Deployment

The site is currently deployed at [https://blackveil.co.nz](https://blackveil.co.nz).

### GitHub Pages Deployment

The site is configured to deploy to GitHub Pages using GitHub Actions. The workflow is defined in `.github/workflows/deploy.yml`. When changes are pushed to the main branch, the site is automatically built and deployed.

**Important:** For production deployment, ensure that the environment variables are properly configured in your deployment platform:

- **Lovable.dev:** Contact Lovable.dev support to configure environment variables for your project
- **GitHub Actions:** Set `VITE_SUPABASE_URL` and `VITE_SUPABASE_ANON_KEY` as repository secrets
- **Cloudflare Pages:** Configure environment variables in the Pages dashboard
- **Vercel/Netlify:** Set environment variables in the project settings

### Environment Variable Configuration

The application includes robust environment variable handling that will display helpful error messages if configuration is missing. If you see a configuration error page when deploying:

1. **For Lovable.dev deployments:** The platform may not have environment variable support configured. Contact Lovable.dev support or switch to GitHub Pages deployment.

2. **For GitHub Pages:** Add the following repository secrets in your GitHub repository settings:

   ```bash
   VITE_SUPABASE_URL=https://wikngnwwakatokbgvenw.supabase.co
   VITE_SUPABASE_ANON_KEY=[Your Supabase Anonymous Key]
   ```

3. **For other platforms:** Configure the environment variables in your deployment platform's settings.

The application will gracefully handle missing environment variables and provide specific instructions for your deployment scenario.

### SPA Routing

The site uses client-side routing with React Router. To support this on GitHub Pages, we've implemented a custom 404.html page that redirects to the main index.html with the correct path. This ensures that direct links to blog posts and other pages work correctly.

### Troubleshooting

#### Environment Variable Issues

If you see a configuration error page when accessing the deployed application:

1. **Check Environment Variables:** Verify that `VITE_SUPABASE_URL` and `VITE_SUPABASE_ANON_KEY` are properly configured in your deployment platform
2. **Lovable.dev Deployments:** Contact Lovable.dev support for environment variable configuration assistance
3. **GitHub Pages:** Ensure repository secrets are set correctly and the build workflow includes them
4. **Local Development:** Copy `.env.example` to `.env.local` and fill in your credentials

The application will display specific instructions based on your deployment scenario.

#### 404 Errors

If you encounter 404 errors for blog posts or other pages:

1. Make sure the GitHub Pages deployment has completed successfully
2. Check that the prerendered HTML files are being generated correctly in the `dist` directory
3. Verify that the 404.html file is present in the root of the deployed site
4. Clear your browser cache and try again

#### Build Failures

If the build process fails:

1. **Missing Environment Variables:** Check that all required environment variables are available during build
2. **Dependency Issues:** Run `npm ci` to ensure clean dependency installation
3. **Node Version:** Ensure you're using Node.js 18 or later
4. **Memory Issues:** For large builds, increase Node.js memory limit: `NODE_OPTIONS="--max-old-space-size=4096" npm run build`

## Cloudflare Radar API Integration

The platform integrates with Cloudflare Radar API to provide real-time threat intelligence data, replacing static mock data with live cybersecurity statistics.

### Radar Integration Features

- **Real-time Data**: Live threat intelligence from Cloudflare's global network
- **Automatic Fallback**: Graceful degradation to cached/mock data if API unavailable
- **Smart Caching**: 1-hour cache duration to minimize API calls
- **Comprehensive Coverage**: Phishing, spoofing, DMARC adoption, and industry risk data

### Quick Setup

```bash
# Deploy the integration
chmod +x scripts/deploy-cloudflare-radar-integration.sh
./scripts/deploy-cloudflare-radar-integration.sh

# Test the integration
node scripts/test-radar-integration.js
```

### API Token Configuration

1. **Create Cloudflare API Token**:
   - Visit: [Cloudflare API Tokens](https://dash.cloudflare.com/profile/api-tokens)
   - Permissions: `Zone:Zone:Read`, `Zone:Analytics:Read`
   - Resources: Include all accounts and zones

2. **Configure in Supabase**:

   ```bash
   supabase secrets set CLOUDFLARE_API_TOKEN=your_token_here
   ```

3. **Verify Integration**:
   - Check admin dashboard for real threat intelligence
   - Monitor function logs: `supabase functions logs cloudflare-radar-stats`

### Data Sources

The system automatically selects the best available data source:

- **`cloudflare_radar_api`**: Real-time Cloudflare Radar data ✅
- **`fallback_no_token`**: Mock data (no API token configured) ⚠️
- **`fallback_api_error`**: Mock data (API temporarily unavailable) ⚠️
- **`fallback_critical_error`**: Mock data (system error) ❌

For detailed setup instructions, see [docs/CLOUDFLARE_RADAR_API_INTEGRATION.md](docs/CLOUDFLARE_RADAR_API_INTEGRATION.md).

## Contact

For questions or support, contact [<EMAIL>](mailto:<EMAIL>)
