# Cloudflare API Token Configuration Guide

## 🎯 Overview

This guide walks you through configuring the Cloudflare API token to enable real Cloudflare Radar data in your MCP integration, replacing fallback data with live threat intelligence.

## 🔑 API Token Details

**Token**: `****************************************`  
**Purpose**: Enable access to Cloudflare Radar API for real-time threat intelligence  
**Environment Variable**: `CLOUDFLARE_API_TOKEN`

## 📋 Configuration Steps

### **Step 1: Set the Supabase Secret**

Run this command in your terminal where you have Supabase CLI access:

```bash
supabase secrets set CLOUDFLARE_API_TOKEN=****************************************
```

### **Step 2: Verify Secret Configuration**

Check that the secret was set correctly:

```bash
supabase secrets list
```

You should see `CLOUDFLARE_API_TOKEN` in the output.

### **Step 3: Clear Cache (Optional)**

To get immediate results, clear the cached fallback data:

```bash
# Make a few requests to force cache refresh
curl "https://wikngnwwakatokbgvenw.supabase.co/functions/v1/cloudflare-radar-stats"
curl "https://wikngnwwakatokbgvenw.supabase.co/functions/v1/cloudflare-radar-stats"
curl "https://wikngnwwakatokbgvenw.supabase.co/functions/v1/cloudflare-radar-stats"
```

## 🧪 Testing & Verification

### **Quick Test**

Run the verification script to check if real data is active:

```bash
node scripts/verify-real-data-integration.js
```

**Expected Output (Success):**
```
✅ VERIFICATION PASSED: Real Cloudflare Radar data is active!
✅ Data source indicates MCP hybrid mode
✅ MCP metadata present
✅ Phishing data: [real numbers] attacks
✅ Spoofing data: [real numbers] incidents
```

### **Continuous Monitoring**

Monitor the integration status in real-time:

```bash
node scripts/monitor-mcp-integration.js
```

This will check every 30 seconds until real data is detected.

### **Manual Verification**

You can also test manually by checking the API response:

```bash
curl "https://wikngnwwakatokbgvenw.supabase.co/functions/v1/cloudflare-radar-stats" | jq '.dataSource'
```

**Expected Values:**
- ✅ `"cloudflare_radar_mcp_hybrid"` - Real data active
- ⚠️ `"fallback_api_error"` - Token not configured or invalid
- ⚠️ `"fallback_mcp_error"` - MCP server issues

## 📊 What Changes After Configuration

### **Before (Fallback Data)**
```json
{
  "dataSource": "fallback_api_error",
  "phishing": { "total": 1247892, "trend": 12.5 },
  "spoofing": { "total": 856431, "trend": 8.3 },
  "dmarc": { "adoptionRate": 67.2, "compliance": 45.8 }
}
```

### **After (Real Data)**
```json
{
  "dataSource": "cloudflare_radar_mcp_hybrid",
  "mcpMetadata": {
    "toolsUsed": ["get_l7_attack_data", "get_email_security_data", "get_email_routing_data"],
    "dataFreshness": "real-time",
    "fallbackMode": "direct_api"
  },
  "phishing": { "total": [real_numbers], "trend": [real_trend] },
  "spoofing": { "total": [real_numbers], "trend": [real_trend] },
  "dmarc": { "adoptionRate": [real_rate], "compliance": [real_compliance] }
}
```

## 🔍 Troubleshooting

### **Issue: Still Getting Fallback Data**

**Symptoms:**
- `dataSource: "fallback_api_error"`
- Missing `mcpMetadata`
- Static numbers (1,247,892 phishing attacks)

**Solutions:**
1. **Check Secret**: `supabase secrets list`
2. **Verify Token**: Ensure exact token value is set
3. **Clear Cache**: Make multiple requests to refresh cache
4. **Check Logs**: `supabase functions logs cloudflare-radar-stats`

### **Issue: API Token Invalid**

**Symptoms:**
- `dataSource: "fallback_api_error"`
- Error messages in logs about authentication

**Solutions:**
1. **Verify Token**: Double-check the token value
2. **Reset Secret**: Re-run the `supabase secrets set` command
3. **Check Permissions**: Ensure token has Radar API access

### **Issue: Slow Response Times**

**Symptoms:**
- Response times > 5 seconds
- Timeout errors

**Solutions:**
1. **Check Network**: Verify connectivity to Cloudflare
2. **Monitor Logs**: Look for timeout messages
3. **Increase Timeout**: Adjust `REQUEST_TIMEOUT_MS` if needed

## 📈 Performance Expectations

### **Response Times**
- **First Request**: 2-5 seconds (cache miss)
- **Cached Requests**: < 1 second (cache hit)
- **Cache Duration**: 4 hours

### **Data Freshness**
- **Real Data**: Updated every 4 hours
- **Fallback Data**: Static values
- **Cache Behavior**: Automatic refresh when expired

## 🔐 Security Notes

### **Token Security**
- Token is stored securely in Supabase secrets
- Never exposed in frontend code
- Automatically used by edge function

### **Fallback Safety**
- System continues working even if API fails
- Graceful degradation to zero-data fallback
- No service interruption during API issues

## 📞 Support

### **Verification Commands**
```bash
# Check if real data is active
node scripts/verify-real-data-integration.js

# Monitor integration status
node scripts/monitor-mcp-integration.js

# Test basic connectivity
curl "https://wikngnwwakatokbgvenw.supabase.co/functions/v1/cloudflare-radar-stats"
```

### **Log Monitoring**
```bash
# View function logs
supabase functions logs cloudflare-radar-stats --follow

# Check for specific messages
supabase functions logs cloudflare-radar-stats | grep "MCP"
```

### **Expected Log Messages**
- ✅ `"🔌 Initializing MCP connection to Cloudflare Radar..."`
- ✅ `"✅ Using stored OAuth token for MCP authentication"`
- ✅ `"🛠️ Calling MCP tool: get_l7_attack_data"`
- ✅ `"✅ Successfully fetched all MCP data sources"`

Once configured correctly, your BlackVeil Security platform will have access to real-time Cloudflare Radar threat intelligence through the MCP integration!
