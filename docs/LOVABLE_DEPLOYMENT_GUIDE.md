# BlackVeil Security - Lovable.dev Deployment Guide

This guide provides step-by-step instructions for deploying the BlackVeil Security platform on Lovable.dev with proper analytics and environment configuration.

## 🚀 Quick Deployment Checklist

- [ ] Environment variables configured on Lovable.dev
- [ ] Build artifacts committed to repository
- [ ] Analytics services verified
- [ ] Image optimizations deployed
- [ ] Deployment verification passed

## 📋 Environment Variables Configuration

### ✅ RESOLVED: Automatic Fallback Configuration

**Good News!** The BlackVeil Security platform now includes automatic fallback configuration for Lovable.dev deployments. Environment variables are no longer required to be manually configured on the Lovable.dev platform.

### How It Works

The application now includes embedded fallback Supabase credentials that are automatically used when environment variables are not available (such as in Lovable.dev deployments). This ensures:

- **Zero Configuration**: No manual environment variable setup required on Lovable.dev
- **Seamless Deployment**: Application works immediately after deployment
- **Local Development**: Still uses `.env.local` when available for development
- **Security**: Fallback uses the same secure Supabase anonymous key

### Environment Variable Priority

1. **Local Development**: Uses `.env.local` file (highest priority)
2. **Lovable.dev Deployment**: Uses embedded fallback configuration
3. **Other Platforms**: Can still use platform-specific environment variables

### For Advanced Users (Optional)

If you prefer to use custom environment variables on Lovable.dev:

```bash
VITE_SUPABASE_URL=https://wikngnwwakatokbgvenw.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NjY0NTQsImV4cCI6MjA2NDE0MjQ1NH0.CCoK3cPN0Pe-AVUgePrxmWvf9qZho0b-prGDilfKmZ4
```

**Note**: Manual configuration is no longer required thanks to the automatic fallback system.

## 📊 Analytics Configuration

### Configured Analytics Services

The platform includes three analytics services that work together:

#### 1. Plausible.io Analytics ✅
- **Status**: Fully configured and working
- **Configuration**: Script included in `index.html`
- **Domain**: `blackveil.co.nz`
- **Privacy**: GDPR compliant, no cookies

#### 2. Google Tag Manager ✅
- **Status**: Fully configured and working
- **Configuration**: Script included in `index.html`
- **Container ID**: `GTM-PS8J6RLP`
- **Purpose**: Advanced tracking and conversion analytics

#### 3. Cloudflare Analytics ⚠️
- **Status**: Auto-injection dependent on domain
- **Configuration**: Automatic when domain is proxied through Cloudflare
- **Expected Domain**: `blackveil.co.nz`
- **Note**: Will not work on preview domains (expected behavior)

### Analytics Verification

The platform includes automatic analytics testing:

```javascript
// Analytics are tested automatically in development
// Check browser console for test results
```

## 🖼️ Image Optimizations

### Optimizations Applied

- **WebP Conversion**: 21 images converted to WebP format (50.7% average size reduction)
- **Compression**: 3 oversized images compressed (96% size reduction)
- **Backup Files**: Original images preserved as backup files
- **SEO Sitemap**: Image sitemap generated for better search indexing

### Verification

Run the deployment verification script:

```bash
npm run verify-deployment
```

## 🔧 Build Configuration

### Build Process

The platform uses a multi-stage build process:

1. **Client Build**: `npm run build:client`
2. **Server Build**: `npm run build:server`
3. **Pre-rendering**: `npm run build:prerender`

### Build Artifacts

The following files are committed to the repository for Lovable.dev deployment:

- `dist/` directory (complete build output)
- `package-lock.json` (dependency consistency)
- Image optimizations in `dist/lovable-uploads/`

## 🚨 Troubleshooting

### Common Issues

#### 1. Environment Variables Not Working ✅ RESOLVED

**Status**: This issue has been resolved with automatic fallback configuration.

**Previous Symptoms**: Application showed environment error page
**Current Solution**:
- ✅ Automatic fallback configuration now handles missing environment variables
- ✅ No manual configuration required on Lovable.dev
- ✅ Application works immediately after deployment

**If you still experience issues**:
- Check browser console for any other errors
- Verify the build completed successfully
- Contact support if problems persist

#### 2. Analytics Not Tracking

**Symptoms**: No data in analytics dashboards
**Solution**:
- Plausible/GTM: Check browser console for errors
- Cloudflare: Only works on production domain (`blackveil.co.nz`)
- Verify scripts are loading in browser developer tools

#### 3. Images Not Loading

**Symptoms**: Broken image links or slow loading
**Solution**:
- Ensure `dist/lovable-uploads/` is committed
- Check that WebP images are present
- Verify image paths in browser network tab

#### 4. Build Failures

**Symptoms**: Deployment fails during build
**Solution**:
- Run `npm run build` locally to test
- Check for TypeScript errors
- Ensure all dependencies are in `package.json`
- Verify `package-lock.json` is committed

### Verification Commands

```bash
# Verify deployment readiness
npm run verify-deployment

# Test build locally
npm run build

# Check for security vulnerabilities
npm audit

# Test development server
npm run dev
```

## 📈 Performance Monitoring

### Metrics to Monitor

- **Core Web Vitals**: LCP, FID, CLS
- **Analytics Data**: Page views, user engagement
- **Image Performance**: Load times, format adoption
- **Build Performance**: Bundle sizes, build times

### Monitoring Tools

- **Plausible.io**: User behavior and page performance
- **Google Tag Manager**: Conversion tracking and events
- **Cloudflare Analytics**: Server-side performance metrics
- **Browser DevTools**: Core Web Vitals and performance profiling

## 🔒 Security Considerations

### Headers and Policies

- Content Security Policy configured
- CORS headers properly set
- Security headers applied
- Environment variables secured

### Analytics Privacy

- Plausible.io: No cookies, GDPR compliant
- Google Tag Manager: Configured for privacy
- Cloudflare Analytics: Server-side, privacy-focused

## 📞 Support

If you encounter issues not covered in this guide:

1. Check the browser console for error messages
2. Run the verification script: `npm run verify-deployment`
3. Review the deployment logs in Lovable.dev
4. Contact Lovable.dev support for platform-specific issues

---

**Last Updated**: December 2024
**Platform Version**: Lovable.dev
**Analytics Version**: Multi-provider (Plausible + GTM + Cloudflare)
