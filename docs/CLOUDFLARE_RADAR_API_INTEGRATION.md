# Cloudflare Radar API Integration Guide

## 🎯 Overview

This guide covers the integration of real Cloudflare Radar API data into the BlackVeil Security platform, replacing the previous mock data implementation with live threat intelligence.

## 🔄 Migration Summary

### **Before (Mock Data)**
- Static threat intelligence data
- No real-time updates
- Simulated industry risk scores
- Demo-only functionality

### **After (Real API Integration)**
- Live Cloudflare Radar API data
- Real-time threat intelligence
- Actual industry attack statistics
- Production-ready with fallbacks

## 🚀 Quick Deployment

### **Prerequisites**
- Supabase CLI installed (`npm install -g supabase`)
- Cloudflare account with API access
- Access to BlackVeil Supabase project

### **Automated Deployment**
```bash
# Make the script executable
chmod +x scripts/deploy-cloudflare-radar-integration.sh

# Run the deployment script
./scripts/deploy-cloudflare-radar-integration.sh
```

The script will:
1. ✅ Check dependencies and authentication
2. 🔗 Link to the BlackVeil Supabase project
3. 🔑 Guide you through API token setup
4. 🚀 Deploy the updated edge function
5. 🧪 Test the integration
6. 📊 Verify data sources

## 🔑 API Token Setup

### **Creating a Cloudflare API Token**

1. **Go to Cloudflare Dashboard**
   - Visit: https://dash.cloudflare.com/profile/api-tokens
   - Click "Create Token"

2. **Configure Token Permissions**
   ```
   Token Name: BlackVeil Radar API
   Permissions:
   - Zone:Zone:Read
   - Zone:Analytics:Read
   - Account:Account:Read (optional)
   
   Account Resources: Include - All accounts
   Zone Resources: Include - All zones
   Client IP Address Filtering: (optional)
   TTL: (optional, recommended: 1 year)
   ```

3. **Test Token**
   ```bash
   curl -X GET "https://api.cloudflare.com/client/v4/user/tokens/verify" \
        -H "Authorization: Bearer YOUR_TOKEN_HERE" \
        -H "Content-Type: application/json"
   ```

4. **Set in Supabase**
   ```bash
   supabase secrets set CLOUDFLARE_API_TOKEN=your_token_here
   ```

## 📊 Data Mapping

### **API Endpoints Used**

| Data Point | Cloudflare Radar Endpoint | Description |
|------------|---------------------------|-------------|
| **Phishing Attacks** | `/radar/attacks/layer7/summary` | Layer 7 attack statistics |
| **Email Spoofing** | `/radar/email/security/summary/spoof` | Email spoofing incidents |
| **DMARC Adoption** | `/radar/email/routing/summary/dmarc` | DMARC implementation rates |
| **Industry Risks** | `/radar/attacks/layer7/summary/industry` | Industry-specific attack data |

### **Data Transformation**

```typescript
// Example transformation from API to frontend format
{
  phishing: {
    total: calculatePhishingTotal(attacksData),      // From layer7 attacks
    trend: calculateTrend(attacksData),              // Week-over-week change
    topTargets: extractTopTargets(attacksData)       // Most targeted industries
  },
  spoofing: {
    total: calculateSpoofingTotal(emailSecurityData), // From email security
    trend: calculateEmailTrend(emailSecurityData),    // Spoofing trend
    topMethods: ['Email', 'Domain', 'Brand', 'Executive'] // Static for now
  },
  dmarc: {
    adoptionRate: calculateDMARCAdoption(emailRoutingData), // % with DMARC
    compliance: calculateDMARCCompliance(emailRoutingData), // % strict policy
    trend: calculateDMARCTrend(emailRoutingData)            // Improvement rate
  },
  industryRisks: transformIndustryRisks(industryData) // Risk scores by industry
}
```

## 🛡️ Fallback Strategy

### **Fallback Hierarchy**
1. **Primary**: Real Cloudflare Radar API data
2. **Secondary**: Cached data (if API fails temporarily)
3. **Tertiary**: Static fallback data (if all else fails)

### **Fallback Triggers**
- **No API Token**: `fallback_no_token`
- **API Error**: `fallback_api_error` 
- **Critical Error**: `fallback_critical_error`
- **Manual Fallback**: `fallback`

### **Data Source Indicators**
```typescript
interface RadarStats {
  // ... data fields
  dataSource?: 'cloudflare_radar_api' | 'fallback_no_token' | 'fallback_api_error' | 'fallback_critical_error';
  error?: string;
}
```

## ⚡ Performance Optimizations

### **Caching Strategy**
- **Cache Duration**: 1 hour (configurable)
- **Cache Key**: `threat_intelligence`
- **Cache Storage**: Supabase `radar_cache` table
- **Cache Metadata**: Source, timestamp, API token status

### **API Optimizations**
- **Parallel Requests**: Multiple endpoints fetched simultaneously
- **Timeout Handling**: 10-second timeout with retries
- **Retry Logic**: 3 attempts with exponential backoff
- **Rate Limiting**: Respects Cloudflare API limits

### **Error Handling**
```typescript
// Comprehensive error handling with graceful degradation
try {
  radarData = await fetchThreatIntelligence(apiToken);
} catch (apiError) {
  console.error('API failed, using fallback:', apiError);
  radarData = getFallbackData();
  radarData.dataSource = 'fallback_api_error';
}
```

## 🔍 Monitoring & Debugging

### **Function Logs**
```bash
# Monitor real-time logs
supabase functions logs cloudflare-radar-stats --follow

# View recent logs
supabase functions logs cloudflare-radar-stats
```

### **Key Log Messages**
- `🛡️ Fetching Cloudflare Radar threat intelligence...` - Function start
- `🌐 Fetching real Cloudflare Radar data...` - API call initiated
- `✅ Successfully fetched real Radar data` - API success
- `⚠️ Cloudflare Radar API failed, falling back...` - API failure
- `📊 Returning cached Radar data` - Cache hit

### **Data Source Verification**
```javascript
// Check data source in browser console
fetch('/api/cloudflare-radar-stats')
  .then(r => r.json())
  .then(data => console.log('Data source:', data.dataSource));
```

## 🧪 Testing

### **Manual Testing**
```bash
# Test the deployed function
curl -X GET "https://wikngnwwakatokbgvenw.supabase.co/functions/v1/cloudflare-radar-stats" \
     -H "Content-Type: application/json"
```

### **Frontend Testing**
```typescript
// Test in React DevTools or browser console
import { useCloudflareRadar } from '@/hooks/use-cloudflare-radar';

// Check data source and freshness
const { radarData } = useCloudflareRadar();
console.log('Data source:', radarData?.dataSource);
console.log('Last updated:', radarData?.lastUpdated);
```

### **Expected Responses**
- **With API Token**: `dataSource: "cloudflare_radar_api"`
- **Without Token**: `dataSource: "fallback_no_token"`
- **API Error**: `dataSource: "fallback_api_error"`

## 🔧 Configuration

### **Environment Variables**
```bash
# Required for Supabase function
SUPABASE_URL=https://wikngnwwakatokbgvenw.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Optional for real API data
CLOUDFLARE_API_TOKEN=your_cloudflare_token
```

### **Supabase Configuration**
```toml
# supabase/config.toml
[functions.cloudflare-radar-stats]
verify_jwt = false
```

### **Cache Configuration**
```typescript
// Configurable constants in edge function
const CACHE_DURATION_MS = 60 * 60 * 1000; // 1 hour
const API_TIMEOUT_MS = 10000; // 10 seconds
const MAX_RETRIES = 3;
```

## 🚨 Troubleshooting

### **Common Issues**

#### **"No API token found"**
- **Cause**: `CLOUDFLARE_API_TOKEN` not set
- **Solution**: Run `supabase secrets set CLOUDFLARE_API_TOKEN=your_token`
- **Impact**: Uses fallback data (still functional)

#### **"API token is invalid"**
- **Cause**: Incorrect or expired token
- **Solution**: Generate new token with correct permissions
- **Impact**: Uses fallback data

#### **"Radar API error: 429"**
- **Cause**: Rate limit exceeded
- **Solution**: Wait for rate limit reset, check caching
- **Impact**: Temporary fallback to cached data

#### **"Function deployment failed"**
- **Cause**: Supabase CLI issues or permissions
- **Solution**: Check `supabase login` and project linking
- **Impact**: Old function version remains active

### **Debug Commands**
```bash
# Check Supabase status
supabase status

# Verify secrets
supabase secrets list

# Test API token manually
curl -H "Authorization: Bearer $TOKEN" \
     "https://api.cloudflare.com/client/v4/user/tokens/verify"

# Check function deployment
supabase functions list
```

## 📈 Future Enhancements

### **Planned Improvements**
1. **Enhanced Data Mapping**: More sophisticated transformation algorithms
2. **Real-time Updates**: WebSocket integration for live data
3. **Custom Metrics**: BlackVeil-specific threat intelligence
4. **Geographic Data**: Location-based threat analysis
5. **Historical Trends**: Long-term trend analysis and predictions

### **API Expansion**
- Additional Radar endpoints for comprehensive coverage
- Custom aggregation and filtering
- Integration with other threat intelligence sources
- Advanced analytics and machine learning insights

---

**The Cloudflare Radar API integration provides BlackVeil Security with real-time, authoritative threat intelligence while maintaining robust fallback mechanisms for maximum reliability.**
