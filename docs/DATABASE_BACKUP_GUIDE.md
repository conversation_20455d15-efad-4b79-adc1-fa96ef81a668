# BlackVeil Security - Database Backup and Recovery Guide

This guide provides comprehensive instructions for backing up, restoring, and maintaining the BlackVeil Security BI dashboard database.

## Overview

The BlackVeil Security platform uses Supabase (PostgreSQL) for data storage with multiple interconnected tables for assessments, lead scoring, and business intelligence analytics.

## Database Schema Overview

### Core Tables (Required for Basic Functionality)
- `assessment_types` - Assessment type definitions
- `assessment_questions` - Assessment questions and categories  
- `assessment_question_options` - Answer options with risk scoring
- `assessment_submissions` - User assessment submissions
- `assessment_answers` - Individual question answers
- `lead_scores` - Automated lead scoring and risk levels

### Advanced BI Tables (Optional for Enhanced Features)
- `enhanced_lead_scores` - ML-based enhanced lead scoring
- `user_journey_events` - Customer journey tracking
- `ab_tests` - A/B testing configurations
- `ab_test_variants` - Test variant definitions
- `ab_test_participations` - User participation tracking

### Supporting Tables
- `assessment_analytics` - Assessment completion analytics
- `assessment_recommendations` - Automated recommendations
- `lead_follow_ups` - Lead management and follow-up tracking
- `user_roles` - User role management

## Backup Strategies

### 1. Supabase Dashboard Backup

#### Manual Backup via Supabase Dashboard
1. Log into your Supabase dashboard
2. Navigate to Settings → Database
3. Click "Create Backup" 
4. Download the backup file
5. Store securely with date/time stamp

#### Automated Supabase Backups
- Enable Point-in-Time Recovery (PITR) in Supabase Pro plans
- Configure automated daily backups
- Set retention period (recommended: 30 days minimum)

### 2. SQL Dump Backup

#### Full Database Backup
```bash
# Using pg_dump (requires database connection details)
pg_dump --clean --no-owner --no-privileges \
  --host=db.wikngnwwakatokbgvenw.supabase.co \
  --port=5432 \
  --username=postgres \
  --dbname=postgres \
  --file=blackveil_backup_$(date +%Y%m%d_%H%M%S).sql

# You'll be prompted for the database password
```

#### Schema-Only Backup
```bash
# Backup just the schema structure
pg_dump --schema-only --no-owner --no-privileges \
  --host=db.wikngnwwakatokbgvenw.supabase.co \
  --port=5432 \
  --username=postgres \
  --dbname=postgres \
  --file=blackveil_schema_$(date +%Y%m%d_%H%M%S).sql
```

#### Data-Only Backup
```bash
# Backup just the data
pg_dump --data-only --no-owner --no-privileges \
  --host=db.wikngnwwakatokbgvenw.supabase.co \
  --port=5432 \
  --username=postgres \
  --dbname=postgres \
  --file=blackveil_data_$(date +%Y%m%d_%H%M%S).sql
```

### 3. Selective Table Backup

#### Core Assessment Data
```bash
# Backup essential assessment tables
pg_dump --no-owner --no-privileges \
  --table=assessment_types \
  --table=assessment_questions \
  --table=assessment_question_options \
  --table=assessment_submissions \
  --table=lead_scores \
  --host=db.wikngnwwakatokbgvenw.supabase.co \
  --port=5432 \
  --username=postgres \
  --dbname=postgres \
  --file=blackveil_core_backup_$(date +%Y%m%d_%H%M%S).sql
```

#### BI Analytics Data
```bash
# Backup BI and analytics tables
pg_dump --no-owner --no-privileges \
  --table=enhanced_lead_scores \
  --table=user_journey_events \
  --table=ab_tests \
  --table=ab_test_variants \
  --table=ab_test_participations \
  --host=db.wikngnwwakatokbgvenw.supabase.co \
  --port=5432 \
  --username=postgres \
  --dbname=postgres \
  --file=blackveil_bi_backup_$(date +%Y%m%d_%H%M%S).sql
```

## Restoration Procedures

### 1. Full Database Restore

#### From SQL Dump
```bash
# Restore complete database from backup
psql --host=db.wikngnwwakatokbgvenw.supabase.co \
  --port=5432 \
  --username=postgres \
  --dbname=postgres \
  --file=blackveil_backup_YYYYMMDD_HHMMSS.sql
```

#### From Supabase Backup
1. Log into Supabase dashboard
2. Navigate to Settings → Database
3. Click "Restore from backup"
4. Select backup file and confirm restoration

### 2. Selective Table Restore

#### Restore Core Tables Only
```bash
# Restore just the core assessment tables
psql --host=db.wikngnwwakatokbgvenw.supabase.co \
  --port=5432 \
  --username=postgres \
  --dbname=postgres \
  --file=blackveil_core_backup_YYYYMMDD_HHMMSS.sql
```

### 3. Schema Recreation

If you need to recreate the database schema from scratch:

```bash
# 1. Execute schema creation scripts
psql --host=db.wikngnwwakatokbgvenw.supabase.co \
  --port=5432 \
  --username=postgres \
  --dbname=postgres \
  --file=scripts/create-advanced-bi-schema.sql

# 2. Fix missing question options
psql --host=db.wikngnwwakatokbgvenw.supabase.co \
  --port=5432 \
  --username=postgres \
  --dbname=postgres \
  --file=scripts/fix-missing-question-options.sql

# 3. Populate sample data (development only)
cd scripts
node populate-existing-data.js
```

## Automated Backup Scripts

### Daily Backup Script

Create `scripts/daily-backup.sh`:

```bash
#!/bin/bash
# Daily backup script for BlackVeil Security database

BACKUP_DIR="/path/to/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_HOST="db.wikngnwwakatokbgvenw.supabase.co"
DB_PORT="5432"
DB_USER="postgres"
DB_NAME="postgres"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Full database backup
pg_dump --clean --no-owner --no-privileges \
  --host=$DB_HOST \
  --port=$DB_PORT \
  --username=$DB_USER \
  --dbname=$DB_NAME \
  --file=$BACKUP_DIR/blackveil_full_$DATE.sql

# Core tables backup
pg_dump --no-owner --no-privileges \
  --table=assessment_types \
  --table=assessment_questions \
  --table=assessment_question_options \
  --table=assessment_submissions \
  --table=lead_scores \
  --host=$DB_HOST \
  --port=$DB_PORT \
  --username=$DB_USER \
  --dbname=$DB_NAME \
  --file=$BACKUP_DIR/blackveil_core_$DATE.sql

# Compress backups
gzip $BACKUP_DIR/blackveil_full_$DATE.sql
gzip $BACKUP_DIR/blackveil_core_$DATE.sql

# Remove backups older than 30 days
find $BACKUP_DIR -name "blackveil_*.sql.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

### Weekly Maintenance Script

Create `scripts/weekly-maintenance.sh`:

```bash
#!/bin/bash
# Weekly maintenance script

echo "Starting weekly maintenance..."

# Run database maintenance
cd scripts
node database-maintenance.js full

# Create backup
./daily-backup.sh

# Generate maintenance report
node database-maintenance.js report > ../logs/maintenance_$(date +%Y%m%d).log

echo "Weekly maintenance completed"
```

## Disaster Recovery Plan

### 1. Complete Data Loss Scenario

1. **Immediate Actions**
   - Assess scope of data loss
   - Notify stakeholders
   - Activate backup restoration process

2. **Recovery Steps**
   ```bash
   # 1. Restore from most recent full backup
   psql --host=... --file=blackveil_full_LATEST.sql
   
   # 2. Verify data integrity
   cd scripts
   node database-maintenance.js report
   
   # 3. Test BI dashboard functionality
   # Navigate to /admin and verify all features work
   
   # 4. Repopulate any missing recent data
   node populate-existing-data.js  # If needed for testing
   ```

3. **Post-Recovery Verification**
   - Test all assessment flows
   - Verify BI dashboard functionality
   - Check data integrity and completeness
   - Update stakeholders on recovery status

### 2. Partial Data Loss Scenario

1. **Identify affected tables**
2. **Restore specific tables from backup**
   ```bash
   # Extract specific table from backup
   pg_restore --table=table_name backup_file.sql
   ```
3. **Verify data relationships and integrity**
4. **Test affected functionality**

## Monitoring and Alerts

### Database Health Monitoring

```bash
# Run weekly health checks
cd scripts
node database-maintenance.js performance

# Check for data integrity issues
node database-maintenance.js report
```

### Automated Alerts

Set up monitoring for:
- Database connection failures
- Backup failures
- Disk space usage
- Query performance degradation
- Data integrity issues

## Best Practices

### Backup Frequency
- **Full backups**: Daily (automated)
- **Incremental backups**: Every 6 hours (if available)
- **Schema backups**: Before any schema changes
- **Pre-deployment backups**: Before major releases

### Storage and Retention
- Store backups in multiple locations
- Keep daily backups for 30 days
- Keep weekly backups for 6 months
- Keep monthly backups for 2 years
- Test backup restoration monthly

### Security
- Encrypt backup files
- Secure backup storage locations
- Limit access to backup files
- Regular security audits of backup procedures

### Documentation
- Document all backup and restore procedures
- Maintain recovery time objectives (RTO)
- Maintain recovery point objectives (RPO)
- Regular disaster recovery testing

## Emergency Contacts

- **Database Administrator**: [Your DBA contact]
- **Supabase Support**: <EMAIL>
- **Development Team Lead**: [Your team lead contact]
- **System Administrator**: [Your sysadmin contact]

## Recovery Time Objectives

- **Full database restore**: 2-4 hours
- **Partial table restore**: 30-60 minutes
- **Schema recreation**: 1-2 hours
- **Data verification**: 30-60 minutes

Remember to test your backup and recovery procedures regularly to ensure they work when needed!
