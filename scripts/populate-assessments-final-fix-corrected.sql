-- CORRECTED FINAL FIX: Assessment Questions Population Script
-- This script addresses the ID constraint issue: assessment_questions.id is NOT auto-incrementing

-- Step 1: Verify assessment types exist
SELECT 
  'Assessment Types Check' as check_type,
  COUNT(*) as count,
  string_agg(title, ', ') as titles
FROM assessment_types 
WHERE slug IN ('cybersecurity-maturity', 'dmarc-compliance');

-- Step 2: Check current question count and max ID
SELECT 
  'Current Questions Check' as check_type,
  COUNT(*) as total_questions,
  COALESCE(MAX(id), 0) as max_id
FROM assessment_questions;

-- Step 3: Insert Cybersecurity Maturity Assessment Questions with explicit IDs
-- Question 1
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  COALESCE((SELECT MAX(id) FROM assessment_questions), 0) + 1,
  at.id,
  'How often does your organization conduct cybersecurity risk assessments?',
  'Risk Management',
  1,
  true
FROM assessment_types at 
WHERE at.slug = 'cybersecurity-maturity'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 1
);

-- Question 2
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  COALESCE((SELECT MAX(id) FROM assessment_questions), 0) + 1,
  at.id,
  'What type of cybersecurity training do you provide to employees?',
  'Security Awareness',
  2,
  true
FROM assessment_types at 
WHERE at.slug = 'cybersecurity-maturity'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 2
);

-- Question 3
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  COALESCE((SELECT MAX(id) FROM assessment_questions), 0) + 1,
  at.id,
  'How does your organization manage software updates and patches?',
  'Patch Management',
  3,
  true
FROM assessment_types at 
WHERE at.slug = 'cybersecurity-maturity'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 3
);

-- Question 4
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  COALESCE((SELECT MAX(id) FROM assessment_questions), 0) + 1,
  at.id,
  'What backup and recovery procedures do you have in place?',
  'Business Continuity',
  4,
  true
FROM assessment_types at 
WHERE at.slug = 'cybersecurity-maturity'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 4
);

-- Question 5
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  COALESCE((SELECT MAX(id) FROM assessment_questions), 0) + 1,
  at.id,
  'How do you control access to sensitive systems and data?',
  'Access Control',
  5,
  true
FROM assessment_types at 
WHERE at.slug = 'cybersecurity-maturity'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 5
);

-- Question 6
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  COALESCE((SELECT MAX(id) FROM assessment_questions), 0) + 1,
  at.id,
  'Do you have an incident response plan for cybersecurity breaches?',
  'Incident Response',
  6,
  true
FROM assessment_types at 
WHERE at.slug = 'cybersecurity-maturity'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 6
);

-- Question 7
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  COALESCE((SELECT MAX(id) FROM assessment_questions), 0) + 1,
  at.id,
  'How do you monitor your network for security threats?',
  'Threat Detection',
  7,
  true
FROM assessment_types at 
WHERE at.slug = 'cybersecurity-maturity'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 7
);

-- Question 8
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  COALESCE((SELECT MAX(id) FROM assessment_questions), 0) + 1,
  at.id,
  'What is your approach to vendor and third-party security?',
  'Third-Party Risk',
  8,
  true
FROM assessment_types at 
WHERE at.slug = 'cybersecurity-maturity'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 8
);

-- Step 4: Insert DMARC Compliance Assessment Questions
-- DMARC Question 1
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  COALESCE((SELECT MAX(id) FROM assessment_questions), 0) + 1,
  at.id,
  'Have you implemented SPF (Sender Policy Framework) for your domain?',
  'Email Authentication',
  1,
  true
FROM assessment_types at 
WHERE at.slug = 'dmarc-compliance'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 1
);

-- DMARC Question 2
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  COALESCE((SELECT MAX(id) FROM assessment_questions), 0) + 1,
  at.id,
  'Do you have DKIM (DomainKeys Identified Mail) configured?',
  'Email Authentication',
  2,
  true
FROM assessment_types at 
WHERE at.slug = 'dmarc-compliance'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 2
);

-- DMARC Question 3
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  COALESCE((SELECT MAX(id) FROM assessment_questions), 0) + 1,
  at.id,
  'What is your current DMARC policy setting?',
  'DMARC Policy',
  3,
  true
FROM assessment_types at 
WHERE at.slug = 'dmarc-compliance'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 3
);

-- DMARC Question 4
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  COALESCE((SELECT MAX(id) FROM assessment_questions), 0) + 1,
  at.id,
  'How do you monitor DMARC reports and failures?',
  'Monitoring',
  4,
  true
FROM assessment_types at 
WHERE at.slug = 'dmarc-compliance'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 4
);

-- DMARC Question 5
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  COALESCE((SELECT MAX(id) FROM assessment_questions), 0) + 1,
  at.id,
  'Do you have email security awareness training for employees?',
  'Security Awareness',
  5,
  true
FROM assessment_types at 
WHERE at.slug = 'dmarc-compliance'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 5
);

-- DMARC Question 6
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  COALESCE((SELECT MAX(id) FROM assessment_questions), 0) + 1,
  at.id,
  'How do you handle email from external domains that fail authentication?',
  'Email Filtering',
  6,
  true
FROM assessment_types at 
WHERE at.slug = 'dmarc-compliance'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 6
);

-- Step 5: Verify questions were inserted
SELECT 
  'Questions Inserted' as status,
  at.title,
  COUNT(aq.id) as question_count
FROM assessment_types at
LEFT JOIN assessment_questions aq ON at.id = aq.assessment_type_id
WHERE at.slug IN ('cybersecurity-maturity', 'dmarc-compliance')
GROUP BY at.id, at.title
ORDER BY at.title;
