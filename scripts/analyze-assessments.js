#!/usr/bin/env node

/**
 * BlackVeil Assessment Database Analysis Script
 * 
 * This script analyzes the assessment system to identify:
 * 1. Placeholder vs real content
 * 2. Database structure completeness
 * 3. Assessment data population status
 * 4. Hardcoded fallback content
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Configuration
const CONFIG = {
  // These would need to be set as environment variables or passed as arguments
  supabaseUrl: process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL,
  supabaseKey: process.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY,
  projectId: 'wikngnwwakatokbgvenw' // From config.toml
};

class AssessmentAnalyzer {
  constructor() {
    if (!CONFIG.supabaseUrl || !CONFIG.supabaseKey) {
      console.warn('⚠️  Supabase credentials not found in environment variables.');
      console.log('Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY environment variables.');
      console.log('Proceeding with code-based analysis only...\n');
      this.supabase = null;
    } else {
      this.supabase = createClient(CONFIG.supabaseUrl, CONFIG.supabaseKey);
    }
    
    this.analysis = {
      placeholders: [],
      hardcodedContent: [],
      databaseTables: {},
      assessmentTypes: [],
      missingData: [],
      recommendations: []
    };
  }

  async run() {
    console.log('🔍 Starting BlackVeil Assessment Analysis\n');
    
    // Analyze code-based placeholders first
    await this.analyzeCodePlaceholders();
    
    // Analyze database if connection is available
    if (this.supabase) {
      await this.analyzeDatabaseContent();
    }
    
    // Generate report
    this.generateReport();
  }

  async analyzeCodePlaceholders() {
    console.log('📝 Analyzing code-based placeholders...\n');
    
    // Check email template hardcoded content
    this.analyzeEmailTemplate();
    
    // Check static FAQ content
    this.analyzeStaticContent();
    
    // Check fallback scores
    this.analyzeFallbackLogic();
    
    // Check assessment question structure
    this.analyzeAssessmentStructure();
  }

  analyzeEmailTemplate() {
    console.log('📧 Email Template Analysis:');
    
    const hardcodedItems = [
      {
        file: 'supabase/functions/send-assessment-results/index.ts',
        type: 'Hardcoded Risk Reduction',
        content: '82% Potential Risk Reduction',
        line: 'Static value in email generation',
        severity: 'HIGH'
      },
      {
        file: 'supabase/functions/send-assessment-results/index.ts',
        type: 'Fallback Recommendations',
        content: 'Hardcoded recommendation list',
        details: [
          'Implement regular phishing awareness training',
          'Configure email authentication (SPF, DKIM, DMARC)',
          'Establish formal suspicious email reporting process',
          'Start regular phishing simulation testing'
        ],
        severity: 'MEDIUM'
      },
      {
        file: 'supabase/functions/send-assessment-results/index.ts',
        type: 'Fallback Lead Score',
        content: 'Default risk scores when database fails',
        details: {
          total_risk_score: 20,
          max_possible_score: 40,
          risk_percentage: 50,
          risk_level: 'MEDIUM'
        },
        severity: 'HIGH'
      }
    ];

    hardcodedItems.forEach(item => {
      console.log(`  ❌ ${item.type}: ${item.content} (${item.severity})`);
      this.analysis.hardcodedContent.push(item);
    });
    console.log('');
  }

  analyzeStaticContent() {
    console.log('📄 Static Content Analysis:');
    
    const staticContent = [
      {
        file: 'src/data/faq.ts',
        type: 'FAQ Content',
        content: 'Static FAQ questions and answers',
        severity: 'LOW'
      },
      {
        file: 'src/data/blog/*.js',
        type: 'Blog Content',
        content: 'Static blog posts with placeholder content',
        severity: 'LOW'
      }
    ];

    staticContent.forEach(item => {
      console.log(`  ℹ️  ${item.type}: ${item.content} (${item.severity})`);
      this.analysis.placeholders.push(item);
    });
    console.log('');
  }

  analyzeFallbackLogic() {
    console.log('🔄 Fallback Logic Analysis:');
    
    const fallbacks = [
      {
        location: 'send-assessment-results function',
        type: 'Lead Score Fallback',
        triggers: 'When lead_scores table query fails',
        impact: 'Uses hardcoded risk assessment values',
        severity: 'HIGH'
      },
      {
        location: 'Industry insights',
        type: 'Default Industry Data',
        triggers: 'For unrecognized industries',
        impact: 'Generic security recommendations',
        severity: 'MEDIUM'
      }
    ];

    fallbacks.forEach(fallback => {
      console.log(`  🔄 ${fallback.type}: ${fallback.impact} (${fallback.severity})`);
      this.analysis.placeholders.push(fallback);
    });
    console.log('');
  }

  analyzeAssessmentStructure() {
    console.log('🏗️  Assessment Structure Analysis:');
    
    const structure = {
      tables: [
        'assessment_types',
        'assessment_questions', 
        'assessment_question_options',
        'assessment_submissions',
        'assessment_answers',
        'lead_scores',
        'assessment_analytics'
      ],
      expectedAssessmentTypes: [
        'phishing-risk',
        'cybersecurity-maturity', 
        'dmarc-compliance'
      ],
      categories: [
        'Email Security',
        'Overall Security',
        'Threat Protection'
      ]
    };

    console.log(`  ✅ Database tables defined: ${structure.tables.length}`);
    console.log(`  📋 Expected assessment types: ${structure.expectedAssessmentTypes.length}`);
    console.log(`  📊 Question categories: ${structure.categories.length}`);
    console.log('');
  }

  async analyzeDatabaseContent() {
    console.log('🗄️  Database Content Analysis:\n');
    
    try {
      // Check assessment types
      await this.checkAssessmentTypes();
      
      // Check assessment questions
      await this.checkAssessmentQuestions();
      
      // Check question options
      await this.checkQuestionOptions();
      
      // Check submissions and scores
      await this.checkSubmissionsAndScores();
      
    } catch (error) {
      console.error('❌ Database analysis failed:', error.message);
      this.analysis.missingData.push({
        type: 'Database Connection',
        error: error.message
      });
    }
  }

  async checkAssessmentTypes() {
    console.log('📋 Checking assessment types...');
    
    const { data: types, error } = await this.supabase
      .from('assessment_types')
      .select('*')
      .eq('is_active', true);
      
    if (error) {
      console.log(`  ❌ Error fetching assessment types: ${error.message}`);
      return;
    }
    
    console.log(`  ✅ Found ${types?.length || 0} active assessment types`);
    types?.forEach(type => {
      console.log(`    - ${type.name} (${type.slug}): ${type.description}`);
    });
    
    this.analysis.assessmentTypes = types || [];
    console.log('');
  }

  async checkAssessmentQuestions() {
    console.log('❓ Checking assessment questions...');
    
    for (const type of this.analysis.assessmentTypes) {
      const { data: questions, error } = await this.supabase
        .from('assessment_questions')
        .select('*')
        .eq('assessment_type_id', type.id)
        .eq('is_active', true);
        
      if (error) {
        console.log(`  ❌ Error fetching questions for ${type.name}: ${error.message}`);
        continue;
      }
      
      console.log(`  📝 ${type.name}: ${questions?.length || 0} questions`);
      
      if (!questions || questions.length === 0) {
        this.analysis.missingData.push({
          type: 'Assessment Questions',
          assessment: type.name,
          issue: 'No questions found'
        });
      }
      
      // Group by category
      const categories = questions?.reduce((acc, q) => {
        acc[q.category] = (acc[q.category] || 0) + 1;
        return acc;
      }, {}) || {};
      
      Object.entries(categories).forEach(([category, count]) => {
        console.log(`    - ${category}: ${count} questions`);
      });
    }
    console.log('');
  }

  async checkQuestionOptions() {
    console.log('🎯 Checking question options...');
    
    const { data: options, error } = await this.supabase
      .from('assessment_question_options')
      .select('question_id')
      .limit(1);
      
    if (error) {
      console.log(`  ❌ Error checking options: ${error.message}`);
      return;
    }
    
    if (!options || options.length === 0) {
      console.log('  ❌ No question options found - questions may not be answerable');
      this.analysis.missingData.push({
        type: 'Question Options',
        issue: 'No options available for questions'
      });
    } else {
      console.log('  ✅ Question options are populated');
    }
    console.log('');
  }

  async checkSubmissionsAndScores() {
    console.log('📊 Checking submissions and lead scores...');
    
    const { data: submissions, error } = await this.supabase
      .from('assessment_submissions')
      .select('id, status, created_at')
      .limit(10);
      
    if (error) {
      console.log(`  ❌ Error fetching submissions: ${error.message}`);
      return;
    }
    
    console.log(`  📝 Recent submissions: ${submissions?.length || 0}`);
    
    if (submissions && submissions.length > 0) {
      const statusCounts = submissions.reduce((acc, s) => {
        acc[s.status] = (acc[s.status] || 0) + 1;
        return acc;
      }, {});
      
      Object.entries(statusCounts).forEach(([status, count]) => {
        console.log(`    - ${status}: ${count}`);
      });
      
      // Check lead scores
      const { data: scores } = await this.supabase
        .from('lead_scores')
        .select('submission_id, risk_level, risk_percentage')
        .limit(5);
        
      console.log(`  🎯 Lead scores generated: ${scores?.length || 0}`);
    }
    console.log('');
  }

  generateReport() {
    console.log('📋 ASSESSMENT ANALYSIS SUMMARY');
    console.log('='.repeat(50));
    console.log('');
    
    // High-priority placeholders
    const highPriorityIssues = this.analysis.hardcodedContent
      .filter(item => item.severity === 'HIGH');
      
    if (highPriorityIssues.length > 0) {
      console.log('🚨 HIGH PRIORITY PLACEHOLDERS:');
      highPriorityIssues.forEach(issue => {
        console.log(`  ❌ ${issue.type} in ${issue.file}`);
      });
      console.log('');
    }
    
    // Medium-priority placeholders  
    const mediumPriorityIssues = this.analysis.hardcodedContent
      .filter(item => item.severity === 'MEDIUM');
      
    if (mediumPriorityIssues.length > 0) {
      console.log('⚠️  MEDIUM PRIORITY PLACEHOLDERS:');
      mediumPriorityIssues.forEach(issue => {
        console.log(`  ⚠️  ${issue.type} in ${issue.file}`);
      });
      console.log('');
    }
    
    // Missing data
    if (this.analysis.missingData.length > 0) {
      console.log('📊 DATA COMPLETENESS ISSUES:');
      this.analysis.missingData.forEach(issue => {
        console.log(`  ❌ ${issue.type}: ${issue.issue || issue.error}`);
      });
      console.log('');
    }
    
    // Recommendations
    console.log('💡 RECOMMENDATIONS:');
    const recommendations = [
      'Replace hardcoded risk reduction percentage with dynamic calculation',
      'Implement dynamic recommendation generation based on actual assessment results',
      'Remove fallback lead scores - ensure proper error handling instead',
      'Populate assessment questions for all assessment types',
      'Add question options for all questions to make them answerable',
      'Implement industry-specific recommendations',
      'Add email template customization based on assessment type'
    ];
    
    recommendations.forEach((rec, i) => {
      console.log(`  ${i + 1}. ${rec}`);
    });
    
    console.log('');
    console.log('Analysis complete! 🎉');
  }
}

// Run the analysis
const analyzer = new AssessmentAnalyzer();
analyzer.run().catch(console.error);
