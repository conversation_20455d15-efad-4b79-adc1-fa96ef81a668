// Service Role Advanced BI Schema Creation Script
// This script uses the Supabase service role key to execute SQL schema creation

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env.local file
let supabaseUrl;
try {
  const envPath = join(__dirname, '..', '.env.local');
  const envContent = readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  envLines.forEach(line => {
    const [key, value] = line.split('=');
    if (key === 'VITE_SUPABASE_URL') supabaseUrl = value;
  });
} catch (error) {
  console.error('❌ Could not load .env.local file:', error.message);
}

if (!supabaseUrl) {
  console.error('❌ Missing Supabase URL');
  process.exit(1);
}

// Service role key (bypasses RLS)
const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODU2NjQ1NCwiZXhwIjoyMDY0MTQyNDU0fQ.j1sjkEHL2LR78ChzOL6MdieomYgSk1CNXXR0n5sq9eo';

// Create Supabase client with service role
const supabase = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Function to execute SQL statements using service role
async function executeSQL(sql) {
  try {
    console.log('🔄 Executing SQL with service role...');
    
    // Use the rpc function to execute raw SQL
    const { data, error } = await supabase.rpc('exec_sql', { 
      sql_query: sql 
    });
    
    if (error) {
      // If exec_sql doesn't exist, try alternative approach
      if (error.message.includes('function exec_sql') || error.code === '42883') {
        console.log('⚠️ exec_sql function not available, trying alternative approach...');
        return await executeAlternativeSQL(sql);
      }
      throw error;
    }
    
    return { success: true, data };
  } catch (err) {
    console.error('❌ SQL execution error:', err.message);
    return { success: false, error: err.message };
  }
}

// Alternative SQL execution method
async function executeAlternativeSQL(sql) {
  console.log('🔄 Using alternative SQL execution method...');
  
  // Split SQL into individual statements
  const statements = sql
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

  console.log(`📝 Executing ${statements.length} SQL statements...`);
  
  const results = [];
  let successCount = 0;
  let errorCount = 0;

  for (let i = 0; i < statements.length; i++) {
    const statement = statements[i];
    
    if (statement.length === 0) continue;
    
    console.log(`\n🔄 Statement ${i + 1}/${statements.length}:`);
    console.log(`   ${statement.substring(0, 80)}${statement.length > 80 ? '...' : ''}`);

    try {
      // For CREATE TABLE statements, try using the REST API
      if (statement.toUpperCase().includes('CREATE TABLE')) {
        console.log('   📋 Creating table via service role...');
        
        // Extract table name
        const tableMatch = statement.match(/CREATE TABLE (?:IF NOT EXISTS )?(\w+)/i);
        const tableName = tableMatch ? tableMatch[1] : 'unknown';
        
        // Try to execute via direct SQL
        const { error } = await supabase.rpc('exec_ddl', { 
          ddl_statement: statement 
        });
        
        if (error) {
          console.log(`   ❌ DDL execution failed: ${error.message}`);
          errorCount++;
          results.push({ statement: i + 1, success: false, error: error.message });
        } else {
          console.log(`   ✅ Table ${tableName} created successfully`);
          successCount++;
          results.push({ statement: i + 1, success: true, table: tableName });
        }
      } else {
        // For other statements (indexes, policies, etc.)
        console.log('   🔧 Executing statement...');
        
        const { error } = await supabase.rpc('exec_statement', { 
          sql_statement: statement 
        });
        
        if (error) {
          console.log(`   ❌ Statement failed: ${error.message}`);
          errorCount++;
          results.push({ statement: i + 1, success: false, error: error.message });
        } else {
          console.log(`   ✅ Statement executed successfully`);
          successCount++;
          results.push({ statement: i + 1, success: true });
        }
      }
    } catch (err) {
      console.log(`   ❌ Exception: ${err.message}`);
      errorCount++;
      results.push({ statement: i + 1, success: false, error: err.message });
    }

    // Small delay between statements
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log(`\n📊 Execution Summary:`);
  console.log(`   ✅ Successful: ${successCount}`);
  console.log(`   ❌ Failed: ${errorCount}`);
  console.log(`   📝 Total: ${statements.length}`);

  return { 
    success: errorCount === 0, 
    results, 
    successCount, 
    errorCount 
  };
}

// Function to create tables using direct table creation
async function createTablesDirectly() {
  console.log('🔄 Creating tables using direct service role access...');
  
  const tables = [
    {
      name: 'enhanced_lead_scores',
      sql: `
        CREATE TABLE IF NOT EXISTS enhanced_lead_scores (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          submission_id UUID NOT NULL REFERENCES assessment_submissions(id) ON DELETE CASCADE,
          completion_score NUMERIC NOT NULL CHECK (completion_score >= 0 AND completion_score <= 100),
          industry_score NUMERIC NOT NULL CHECK (industry_score >= 0 AND industry_score <= 100),
          size_score NUMERIC NOT NULL CHECK (size_score >= 0 AND size_score <= 100),
          engagement_score NUMERIC NOT NULL CHECK (engagement_score >= 0 AND engagement_score <= 100),
          urgency_score NUMERIC NOT NULL CHECK (urgency_score >= 0 AND urgency_score <= 100),
          total_score NUMERIC NOT NULL CHECK (total_score >= 0 AND total_score <= 500),
          conversion_probability NUMERIC NOT NULL CHECK (conversion_probability >= 0 AND conversion_probability <= 100),
          priority_level TEXT NOT NULL CHECK (priority_level IN ('LOW', 'MEDIUM', 'HIGH', 'URGENT')),
          recommended_actions JSONB,
          calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(submission_id)
        );
      `
    },
    {
      name: 'user_journey_events',
      sql: `
        CREATE TABLE IF NOT EXISTS user_journey_events (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          event_type TEXT NOT NULL CHECK (event_type IN ('page_view', 'assessment_start', 'assessment_progress', 'assessment_complete', 'result_view', 'contact_form', 'download', 'email_click')),
          page_url TEXT,
          session_id TEXT NOT NULL,
          user_id UUID,
          submission_id UUID REFERENCES assessment_submissions(id) ON DELETE SET NULL,
          event_data JSONB,
          user_agent TEXT,
          ip_address INET,
          referrer TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'ab_tests',
      sql: `
        CREATE TABLE IF NOT EXISTS ab_tests (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT,
          test_type TEXT NOT NULL CHECK (test_type IN ('assessment_question', 'email_template', 'cta_button', 'landing_page', 'form_layout', 'pricing_display')),
          hypothesis TEXT,
          success_metric TEXT NOT NULL,
          start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          end_date TIMESTAMP WITH TIME ZONE,
          is_active BOOLEAN DEFAULT true,
          winner_variant UUID,
          confidence_level NUMERIC CHECK (confidence_level >= 0 AND confidence_level <= 100),
          statistical_significance BOOLEAN DEFAULT false,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_by UUID
        );
      `
    },
    {
      name: 'ab_test_variants',
      sql: `
        CREATE TABLE IF NOT EXISTS ab_test_variants (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          test_id UUID NOT NULL REFERENCES ab_tests(id) ON DELETE CASCADE,
          name TEXT NOT NULL,
          description TEXT,
          config JSONB NOT NULL,
          traffic_percentage NUMERIC NOT NULL CHECK (traffic_percentage >= 0 AND traffic_percentage <= 100),
          participants INTEGER DEFAULT 0,
          conversions INTEGER DEFAULT 0,
          conversion_rate NUMERIC GENERATED ALWAYS AS (
            CASE 
              WHEN participants > 0 THEN (conversions::NUMERIC / participants::NUMERIC * 100)
              ELSE 0
            END
          ) STORED,
          is_control BOOLEAN DEFAULT false,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'ab_test_participations',
      sql: `
        CREATE TABLE IF NOT EXISTS ab_test_participations (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          test_id UUID NOT NULL REFERENCES ab_tests(id) ON DELETE CASCADE,
          variant_id UUID NOT NULL REFERENCES ab_test_variants(id) ON DELETE CASCADE,
          session_id TEXT NOT NULL,
          user_id UUID,
          submission_id UUID REFERENCES assessment_submissions(id) ON DELETE SET NULL,
          converted BOOLEAN DEFAULT false,
          conversion_value NUMERIC,
          conversion_timestamp TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(test_id, session_id)
        );
      `
    }
  ];

  let successCount = 0;
  let errorCount = 0;

  for (const table of tables) {
    console.log(`\n🔄 Creating table: ${table.name}`);
    
    try {
      const result = await executeSQL(table.sql);
      
      if (result.success) {
        console.log(`✅ ${table.name} created successfully`);
        successCount++;
      } else {
        console.log(`❌ ${table.name} creation failed: ${result.error}`);
        errorCount++;
      }
    } catch (err) {
      console.log(`❌ ${table.name} creation error: ${err.message}`);
      errorCount++;
    }
  }

  return { successCount, errorCount, totalTables: tables.length };
}

// Function to verify table creation
async function verifyTablesCreation() {
  console.log('\n🔍 Verifying Advanced BI Tables Creation...');
  
  const tables = [
    'enhanced_lead_scores',
    'user_journey_events', 
    'ab_tests',
    'ab_test_variants',
    'ab_test_participations'
  ];

  const results = {};
  let allExist = true;
  
  for (const table of tables) {
    try {
      const { count, error } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.log(`❌ ${table}: ${error.message}`);
        results[table] = false;
        allExist = false;
      } else {
        console.log(`✅ ${table}: Table exists (${count || 0} records)`);
        results[table] = true;
      }
    } catch (err) {
      console.log(`❌ ${table}: ${err.message}`);
      results[table] = false;
      allExist = false;
    }
  }

  return { allExist, results };
}

// Main execution function
async function createAdvancedBISchema() {
  console.log('🚀 BlackVeil Security - Service Role Schema Creation');
  console.log('🔐 Using service role key to bypass RLS');
  console.log('=' .repeat(60));

  try {
    // First check current status
    console.log('\n📊 Checking current table status...');
    const initialStatus = await verifyTablesCreation();
    
    if (initialStatus.allExist) {
      console.log('\n🎉 All advanced BI tables already exist!');
      return true;
    }

    console.log('\n⚠️ Some tables are missing. Creating them now...');

    // Try to read and execute the full SQL file first
    try {
      const sqlFilePath = join(__dirname, 'create-advanced-bi-schema.sql');
      console.log(`\n📖 Reading SQL file: ${sqlFilePath}`);
      
      const sqlContent = readFileSync(sqlFilePath, 'utf8');
      console.log(`✅ SQL file loaded (${sqlContent.length} characters)`);

      const result = await executeSQL(sqlContent);
      
      if (result.success) {
        console.log('✅ Full SQL script executed successfully');
      } else {
        console.log('⚠️ Full SQL script failed, trying individual table creation...');
        await createTablesDirectly();
      }
    } catch (fileError) {
      console.log('⚠️ Could not read SQL file, trying direct table creation...');
      await createTablesDirectly();
    }

    // Verify creation
    console.log('\n🔍 Final verification...');
    const finalStatus = await verifyTablesCreation();
    
    if (finalStatus.allExist) {
      console.log('\n🎉 All advanced BI tables created successfully!');
      return true;
    } else {
      console.log('\n⚠️ Some tables may not have been created properly');
      return false;
    }

  } catch (error) {
    console.error('❌ Service role schema creation failed:', error.message);
    return false;
  }
}

// Run the schema creation
createAdvancedBISchema().then((success) => {
  if (success) {
    console.log('\n✅ Advanced BI schema creation completed successfully!');
    console.log('📊 All tables are ready for use');
    console.log('\n📋 Next Steps:');
    console.log('   1. Run: node scripts/database-inspection.js');
    console.log('   2. Test the BI dashboard at /admin');
    console.log('   3. Verify enhanced lead scoring features');
    process.exit(0);
  } else {
    console.log('\n❌ Schema creation incomplete');
    console.log('📋 Some tables may need manual creation');
    process.exit(1);
  }
}).catch(error => {
  console.error('❌ Script execution failed:', error);
  process.exit(1);
});
