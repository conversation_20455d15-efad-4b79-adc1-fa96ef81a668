#!/usr/bin/env node

/**
 * Copy question structure from working assessments to create templates
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://wikngnwwakatokbgvenw.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NjY0NTQsImV4cCI6MjA2NDE0MjQ1NH0.CCoK3cPN0Pe-AVUgePrxmWvf9qZho0b-prGDilfKmZ4';

const supabase = createClient(supabaseUrl, supabaseKey);

async function analyzeQuestionStructure() {
  console.log('🔍 Analyzing Question Structure from Working Assessments\n');

  try {
    // Get a working assessment (phishing-risk)
    const { data: phishingType } = await supabase
      .from('assessment_types')
      .select('*')
      .eq('slug', 'phishing-risk')
      .single();

    // Get all questions and options for this assessment
    const { data: questions } = await supabase
      .from('assessment_questions')
      .select(`
        *,
        assessment_question_options (*)
      `)
      .eq('assessment_type_id', phishingType.id)
      .order('order_index');

    console.log('📝 Question Structure Analysis:');
    console.log('=====================================');
    
    questions.forEach((question, index) => {
      console.log(`\nQuestion ${index + 1}:`);
      console.log(`  ID: ${question.id}`);
      console.log(`  Text: ${question.question_text}`);
      console.log(`  Category: ${question.category}`);
      console.log(`  Order: ${question.order_index}`);
      console.log(`  Active: ${question.is_active}`);
      console.log(`  Assessment Type ID: ${question.assessment_type_id}`);
      console.log(`  Created: ${question.created_at}`);
      
      console.log(`  Options (${question.assessment_question_options.length}):`);
      question.assessment_question_options.forEach((option, optIndex) => {
        console.log(`    ${optIndex + 1}. ${option.option_text}`);
        console.log(`       Risk Score: ${option.risk_score}`);
        console.log(`       Order: ${option.order_index}`);
        console.log(`       Question ID: ${option.question_id}`);
      });
    });

    // Now let's try to understand why we can't insert
    console.log('\n\n🔒 Testing Insert Permissions...');
    
    // Try to get the missing assessment types
    const { data: missingTypes } = await supabase
      .from('assessment_types')
      .select('*')
      .in('slug', ['cybersecurity-maturity', 'dmarc-compliance']);

    console.log(`Found ${missingTypes.length} assessment types that need questions:`);
    missingTypes.forEach(type => {
      console.log(`  - ${type.title} (ID: ${type.id})`);
    });

    // Try a simple test insert (this will likely fail but show us the exact error)
    console.log('\n🧪 Testing simple insert...');
    const testQuestion = {
      assessment_type_id: missingTypes[0].id,
      question_text: 'Test question - please ignore',
      category: 'Test',
      order_index: 999,
      is_active: false // Make it inactive so it doesn't interfere
    };

    const { data: insertResult, error: insertError } = await supabase
      .from('assessment_questions')
      .insert(testQuestion)
      .select();

    if (insertError) {
      console.log('❌ Insert failed as expected:', insertError.message);
      console.log('   Error details:', insertError);
      
      // Check if it's an RLS issue
      if (insertError.message.includes('row-level security')) {
        console.log('\n💡 This is a Row-Level Security (RLS) policy issue.');
        console.log('   We need to either:');
        console.log('   1. Use a service role key (not anon key)');
        console.log('   2. Disable RLS temporarily');
        console.log('   3. Add questions through the Supabase dashboard');
        console.log('   4. Create a Supabase function with elevated permissions');
      }
    } else {
      console.log('✅ Insert succeeded:', insertResult);
      
      // Clean up the test question
      await supabase
        .from('assessment_questions')
        .delete()
        .eq('id', insertResult[0].id);
      console.log('🧹 Test question cleaned up');
    }

  } catch (error) {
    console.error('💥 Error:', error.message);
  }
}

analyzeQuestionStructure();
