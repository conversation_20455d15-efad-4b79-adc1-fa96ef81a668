#!/usr/bin/env node

/**
 * BlackVeil Security - Cloudflare Radar MCP Integration Test Script
 *
 * This script tests the new MCP-based Cloudflare Radar integration
 * to ensure it's working correctly with real MCP server data.
 */

import https from 'https';
import { performance } from 'perf_hooks';

// Configuration
const MCP_FUNCTION_URL = 'https://wikngnwwakatokbgvenw.supabase.co/functions/v1/cloudflare-radar-stats';
const TIMEOUT_MS = 60000; // 60 seconds for MCP calls

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(color, prefix, message) {
  console.log(`${color}[${prefix}]${colors.reset} ${message}`);
}

function logSuccess(message) {
  log(colors.green, '✅ SUCCESS', message);
}

function logError(message) {
  log(colors.red, '❌ ERROR', message);
}

function logWarning(message) {
  log(colors.yellow, '⚠️  WARNING', message);
}

function logInfo(message) {
  log(colors.blue, 'ℹ️  INFO', message);
}

function logTest(message) {
  log(colors.cyan, '🧪 TEST', message);
}

/**
 * Make HTTP request with timeout
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error(`Request timeout after ${TIMEOUT_MS}ms`));
    }, TIMEOUT_MS);

    const req = https.request(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    }, (res) => {
      clearTimeout(timeout);
      
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({ status: res.statusCode, data: parsed, headers: res.headers });
        } catch (e) {
          resolve({ status: res.statusCode, data, headers: res.headers });
        }
      });
    });

    req.on('error', (error) => {
      clearTimeout(timeout);
      reject(error);
    });

    req.end();
  });
}

/**
 * Test MCP function response structure
 */
function validateMCPResponse(data) {
  const errors = [];

  // Check required top-level fields
  const requiredFields = ['phishing', 'spoofing', 'dmarc', 'industryRisks', 'lastUpdated', 'dataSource'];
  for (const field of requiredFields) {
    if (!(field in data)) {
      errors.push(`Missing required field: ${field}`);
    }
  }

  // Check phishing structure
  if (data.phishing) {
    const phishingFields = ['total', 'trend', 'topTargets'];
    for (const field of phishingFields) {
      if (!(field in data.phishing)) {
        errors.push(`Missing phishing.${field}`);
      }
    }
  }

  // Check spoofing structure
  if (data.spoofing) {
    const spoofingFields = ['total', 'trend', 'topMethods'];
    for (const field of spoofingFields) {
      if (!(field in data.spoofing)) {
        errors.push(`Missing spoofing.${field}`);
      }
    }
  }

  // Check DMARC structure
  if (data.dmarc) {
    const dmarcFields = ['adoptionRate', 'compliance', 'trend'];
    for (const field of dmarcFields) {
      if (!(field in data.dmarc)) {
        errors.push(`Missing dmarc.${field}`);
      }
    }
  }

  // Check data source
  const validDataSources = [
    'cloudflare_radar_mcp_hybrid',
    'fallback_mcp_error',
    'fallback_mcp_unavailable',
    'fallback_critical_error',
    'fallback_api_error', // Legacy fallback
    'fallback_no_token'   // Legacy fallback
  ];
  
  if (data.dataSource && !validDataSources.includes(data.dataSource)) {
    errors.push(`Invalid data source: ${data.dataSource}`);
  }

  return errors;
}

/**
 * Test MCP data quality
 */
function validateMCPDataQuality(data) {
  const warnings = [];

  // Check if we're getting real MCP data
  if (data.dataSource === 'cloudflare_radar_mcp_hybrid') {
    logSuccess('Receiving real MCP data from Cloudflare Radar server');
    
    // Check for MCP-specific enhancements
    if (data.mcpMetadata) {
      logInfo(`MCP tools used: ${data.mcpMetadata.toolsUsed?.join(', ')}`);
      logInfo(`Data freshness: ${data.mcpMetadata.dataFreshness}`);
    }
    
    // Check for enhanced data fields
    if (data.domainIntelligence) {
      logInfo('Enhanced domain intelligence data available');
    }
    
    if (data.phishing?.recentAnomalies) {
      logInfo(`Recent anomalies detected: ${data.phishing.recentAnomalies.length}`);
    }
    
  } else {
    logWarning(`Using fallback data source: ${data.dataSource}`);
    if (data.error) {
      logWarning(`Error details: ${data.error}`);
    }
  }

  // Check data freshness
  const lastUpdated = new Date(data.lastUpdated);
  const now = new Date();
  const ageMinutes = (now - lastUpdated) / (1000 * 60);
  
  if (ageMinutes > 60) {
    warnings.push(`Data is ${Math.round(ageMinutes)} minutes old`);
  } else {
    logSuccess(`Data is fresh (${Math.round(ageMinutes)} minutes old)`);
  }

  return warnings;
}

/**
 * Main test function
 */
async function runMCPTests() {
  console.log('\n' + '='.repeat(60));
  console.log('🛡️  BlackVeil Security - MCP Integration Test');
  console.log('='.repeat(60));

  logInfo('Testing Cloudflare Radar MCP integration...');
  logInfo(`Function URL: ${MCP_FUNCTION_URL}`);
  logInfo(`Timeout: ${TIMEOUT_MS}ms`);

  try {
    // Test 1: Basic connectivity
    logTest('Testing MCP function connectivity...');
    const startTime = performance.now();
    
    const response = await makeRequest(MCP_FUNCTION_URL);
    const endTime = performance.now();
    const responseTime = Math.round(endTime - startTime);

    logInfo(`Response time: ${responseTime}ms`);
    logInfo(`HTTP status: ${response.status}`);

    if (response.status !== 200) {
      logError(`HTTP error: ${response.status}`);
      console.log('Response:', response.data);
      return;
    }

    logSuccess('MCP function is accessible');

    // Test 2: Response structure validation
    logTest('Validating response structure...');
    const structureErrors = validateMCPResponse(response.data);
    
    if (structureErrors.length > 0) {
      logError('Response structure validation failed:');
      structureErrors.forEach(error => logError(`  - ${error}`));
      return;
    }

    logSuccess('Response structure is valid');

    // Test 3: Data quality validation
    logTest('Validating data quality...');
    const qualityWarnings = validateMCPDataQuality(response.data);
    
    if (qualityWarnings.length > 0) {
      qualityWarnings.forEach(warning => logWarning(warning));
    }

    // Test 4: Performance check
    logTest('Checking performance metrics...');
    if (responseTime < 10000) {
      logSuccess(`Good response time: ${responseTime}ms`);
    } else if (responseTime < 30000) {
      logWarning(`Acceptable response time: ${responseTime}ms`);
    } else {
      logError(`Slow response time: ${responseTime}ms`);
    }

    // Test 5: Cache behavior
    logTest('Testing cache behavior...');
    const cacheStartTime = performance.now();
    const cacheResponse = await makeRequest(MCP_FUNCTION_URL);
    const cacheEndTime = performance.now();
    const cacheResponseTime = Math.round(cacheEndTime - cacheStartTime);

    if (cacheResponseTime < responseTime * 0.5) {
      logSuccess(`Cache is working (${cacheResponseTime}ms vs ${responseTime}ms)`);
    } else {
      logWarning('Cache may not be working effectively');
    }

    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('📊 Test Summary');
    console.log('='.repeat(60));
    
    logInfo(`Data Source: ${response.data.dataSource}`);
    logInfo(`Last Updated: ${response.data.lastUpdated}`);
    logInfo(`Response Time: ${responseTime}ms`);
    logInfo(`Cache Response Time: ${cacheResponseTime}ms`);
    
    if (response.data.dataSource === 'cloudflare_radar_mcp_hybrid') {
      logSuccess('✅ MCP integration is working with real Cloudflare Radar data');
    } else {
      logWarning('⚠️  MCP integration is using fallback data');
    }

    console.log('\n🎉 MCP integration test completed successfully!');

  } catch (error) {
    logError(`Test failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run tests
runMCPTests().catch(console.error);
