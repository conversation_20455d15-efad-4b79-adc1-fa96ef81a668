// BlackVeil Security - Direct Email Queue Script
// This script directly queues emails for existing submissions using the service role

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://wikngnwwakatokbgvenw.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODU2NjQ1NCwiZXhwIjoyMDY0MTQyNDU0fQ.j1sjkEHL2LR78ChzOL6MdieomYgSk1CNXXR0n5sq9eo';

const supabase = createClient(supabaseUrl, serviceKey);

// Function to calculate email delay
function calculateEmailDelay(riskLevel) {
  switch (riskLevel) {
    case 'HIGH': return 0; // Immediate
    case 'MEDIUM': return 24; // 24 hours
    case 'LOW': return 72; // 72 hours
    default: return 24; // Default
  }
}

// Function to generate email content
function generateEmailContent(submission, leadScore) {
  const riskLevel = leadScore.risk_level;
  const riskPercentage = leadScore.risk_percentage;
  const recommendations = leadScore.top_recommendations || [];
  const contactName = submission.contact_name || 'there';
  const companyName = submission.company_name;
  const industry = submission.industry || 'business';
  const assessmentType = submission.assessment_types?.title || 'security assessment';

  let subject = '';
  let content = '';

  if (riskLevel === 'HIGH') {
    subject = 'Critical Security Gaps Identified - Immediate Action Recommended';
    content = `Hi ${contactName},

Thank you for completing the ${assessmentType} assessment for ${companyName}.

CRITICAL SECURITY FINDINGS:
Our analysis has identified several critical security vulnerabilities that require immediate attention. Your organization scored ${riskPercentage}% risk level, indicating significant exposure to cyber threats.

KEY SECURITY GAPS:
${recommendations.map((rec, i) => `${i + 1}. ${rec}`).join('\n')}

IMMEDIATE ACTIONS RECOMMENDED:
1. Implement Multi-Factor Authentication across all critical systems
2. Conduct Security Awareness Training for all employees
3. Establish Incident Response Procedures for rapid threat containment
4. Review Access Controls and implement principle of least privilege

COMPLIMENTARY SECURITY CONSULTATION:
I'd like to schedule a brief 15-minute call to discuss these findings and provide specific recommendations tailored to ${companyName}'s infrastructure.

This consultation is complimentary and focused on helping you understand and prioritize these critical security gaps.

Would you be available for a quick security consultation this week?

Best regards,
BlackVeil Security Team
Protecting New Zealand businesses from cyber threats

P.S. We've prepared a detailed security report with actionable steps. Reply to this email to receive your complimentary copy.

---
BlackVeil Security | Cybersecurity Experts | New Zealand
This email was sent because you completed a security assessment.`;

  } else if (riskLevel === 'MEDIUM') {
    subject = `Security Assessment Results & Recommendations for ${companyName}`;
    content = `Hi ${contactName},

Thank you for taking the time to complete our ${assessmentType} assessment.

YOUR SECURITY SCORE: ${riskPercentage}% Risk Level

Your results show ${companyName} has a moderate security posture with some areas for improvement. While not critical, addressing these gaps will significantly strengthen your defenses.

KEY AREAS FOR IMPROVEMENT:
${recommendations.map((rec, i) => `${i + 1}. ${rec}`).join('\n')}

INDUSTRY INSIGHTS:
Based on our analysis of ${industry} organizations, companies that address these areas typically see:
- 60% reduction in security incidents
- Improved compliance posture
- Enhanced customer trust and reputation
- Reduced cyber insurance premiums

INDUSTRY SECURITY GUIDE:
I've prepared some ${industry}-specific security best practices that might be valuable for ${companyName}. This guide includes practical steps you can implement immediately to strengthen your security posture.

Would you like me to send over these industry-specific recommendations?

Best regards,
BlackVeil Security Team
Helping ${industry} organizations strengthen their security

---
BlackVeil Security | Cybersecurity Experts | New Zealand
This email was sent because you completed a security assessment.`;

  } else if (riskLevel === 'LOW') {
    subject = `Great Security Foundation - Enhancement Opportunities for ${companyName}`;
    content = `Hi ${contactName},

CONGRATULATIONS!
${companyName} demonstrates a strong security foundation with a ${riskPercentage}% risk level.

Your proactive approach to cybersecurity puts you ahead of many organizations in the ${industry} sector.

YOUR SECURITY STRENGTHS:
- Strong foundational security practices in place
- Good awareness of security principles
- Proactive approach to risk assessment
- Commitment to continuous improvement

ENHANCEMENT OPPORTUNITIES:
While your current security posture is solid, there are always opportunities to enhance your defenses and stay ahead of evolving threats:

${recommendations.map((rec, i) => `${i + 1}. ${rec}`).join('\n')}

STAYING AHEAD OF THREATS:
The cybersecurity landscape evolves rapidly. I'll be sharing monthly security insights and emerging threat updates that might be valuable for ${companyName}.

These updates include:
- Latest threat intelligence and attack trends
- New security technologies and best practices
- Industry-specific security recommendations
- Compliance updates and regulatory changes

Would you like to receive our monthly security newsletter with the latest threat intelligence and best practices?

Best regards,
BlackVeil Security Team
Supporting security-conscious ${industry} organizations

---
BlackVeil Security | Cybersecurity Experts | New Zealand
This email was sent because you completed a security assessment.`;
  }

  return { subject, content };
}

async function queueEmailsDirectly() {
  console.log('📧 BlackVeil Security - Direct Email Queue');
  console.log('🎯 Queuing personalized emails for existing submissions');
  console.log('=' .repeat(60));

  try {
    // Get existing submissions with lead scores
    const { data: submissions, error: submissionsError } = await supabase
      .from('assessment_submissions')
      .select(`
        id,
        company_name,
        contact_name,
        email,
        industry,
        status,
        created_at,
        assessment_types (
          title
        ),
        lead_scores (
          risk_level,
          risk_percentage,
          lead_priority,
          total_risk_score,
          top_recommendations
        )
      `)
      .eq('status', 'completed');

    if (submissionsError) {
      throw new Error(`Failed to fetch submissions: ${submissionsError.message}`);
    }

    console.log(`📊 Found ${submissions?.length || 0} completed submissions`);

    if (!submissions || submissions.length === 0) {
      console.log('❌ No completed submissions found');
      return false;
    }

    let queuedCount = 0;
    let skippedCount = 0;

    for (const submission of submissions) {
      if (!submission.lead_scores) {
        console.log(`⚠️ No lead score for ${submission.company_name} - skipping`);
        skippedCount++;
        continue;
      }

      console.log(`\n📧 Processing: ${submission.company_name}`);
      console.log(`   Contact: ${submission.contact_name} (${submission.email})`);
      console.log(`   Risk: ${submission.lead_scores.risk_level} (${submission.lead_scores.risk_percentage}%)`);
      console.log(`   Industry: ${submission.industry}`);

      // Check if email already queued
      const { data: existingEmail } = await supabase
        .from('email_queue')
        .select('id, status, email_subject')
        .eq('submission_id', submission.id)
        .single();

      if (existingEmail) {
        console.log(`   ✅ Email already queued (Status: ${existingEmail.status})`);
        console.log(`   📧 Subject: "${existingEmail.email_subject}"`);
        skippedCount++;
        continue;
      }

      // Generate email content
      const emailContent = generateEmailContent(submission, submission.lead_scores);
      const delayHours = calculateEmailDelay(submission.lead_scores.risk_level);
      const scheduledAt = new Date(Date.now() + (delayHours * 60 * 60 * 1000));

      console.log(`   📝 Subject: "${emailContent.subject}"`);
      console.log(`   ⏰ Scheduled: ${scheduledAt.toLocaleString()} (${delayHours}h delay)`);

      // Insert into email queue
      const { data: queueResult, error: queueError } = await supabase
        .from('email_queue')
        .insert({
          submission_id: submission.id,
          email_type: submission.lead_scores.risk_level,
          recipient_email: submission.email,
          recipient_name: submission.contact_name,
          company_name: submission.company_name,
          industry: submission.industry,
          assessment_type: submission.assessment_types?.title,
          risk_percentage: submission.lead_scores.risk_percentage,
          top_recommendations: submission.lead_scores.top_recommendations,
          scheduled_at: scheduledAt.toISOString(),
          email_subject: emailContent.subject,
          email_content: emailContent.content,
          status: 'pending'
        })
        .select();

      if (queueError) {
        console.log(`   ❌ Failed to queue email: ${queueError.message}`);
        continue;
      }

      console.log(`   ✅ Email queued successfully`);
      queuedCount++;

      // Log the email queue event
      const { error: journeyError } = await supabase
        .from('user_journey_events')
        .insert({
          event_type: 'email_queued',
          submission_id: submission.id,
          session_id: `direct_queue_${Date.now()}`,
          page_url: '/system/email-automation',
          event_data: {
            email_type: submission.lead_scores.risk_level,
            recipient_email: submission.email,
            scheduled_at: scheduledAt.toISOString(),
            delay_hours: delayHours,
            risk_percentage: submission.lead_scores.risk_percentage,
            subject: emailContent.subject,
            company_name: submission.company_name,
            industry: submission.industry
          }
        });

      if (journeyError) {
        console.log(`   ⚠️ Failed to log journey event: ${journeyError.message}`);
      } else {
        console.log(`   📊 Analytics event logged`);
      }

      // Show email content preview
      console.log(`\n   📝 Email Content Preview:`);
      console.log(`   ${'-'.repeat(40)}`);
      const preview = emailContent.content.substring(0, 200) + '...';
      console.log(`   ${preview}`);
      console.log(`   ${'-'.repeat(40)}`);
    }

    console.log(`\n📊 Email Queue Summary:`);
    console.log(`   ✅ Emails queued: ${queuedCount}`);
    console.log(`   ⚠️ Emails skipped: ${skippedCount}`);
    console.log(`   📧 Total submissions: ${submissions.length}`);

    // Check final queue status
    const { data: queueStatus, error: statusError } = await supabase
      .rpc('get_email_queue_status');

    if (!statusError && queueStatus && queueStatus.length > 0) {
      const status = queueStatus[0];
      console.log(`\n📊 Final Email Queue Status:`);
      console.log(`   📧 Total queued: ${status.total_queued}`);
      console.log(`   ⏳ Pending: ${status.pending}`);
      console.log(`   ✅ Sent: ${status.sent}`);
      console.log(`   ❌ Failed: ${status.failed}`);
      console.log(`   ⏰ Next scheduled: ${status.next_scheduled || 'None'}`);
    }

    // Show recent emails in queue
    const { data: recentEmails, error: emailsError } = await supabase
      .from('email_queue')
      .select('company_name, email_type, email_subject, scheduled_at, status')
      .order('created_at', { ascending: false })
      .limit(5);

    if (!emailsError && recentEmails && recentEmails.length > 0) {
      console.log(`\n📧 Recent Emails in Queue:`);
      recentEmails.forEach((email, index) => {
        const scheduledTime = new Date(email.scheduled_at).toLocaleString();
        console.log(`   ${index + 1}. ${email.company_name} - ${email.email_type} Priority`);
        console.log(`      Subject: "${email.email_subject}"`);
        console.log(`      Scheduled: ${scheduledTime} | Status: ${email.status}`);
      });
    }

    console.log(`\n🎉 Email Queue Setup Complete!`);
    console.log(`✅ Automated lead nurturing emails are ready`);
    console.log(`📧 Professional, consultative messaging configured`);
    console.log(`🎯 Risk-based delays implemented (HIGH: 0h, MEDIUM: 24h, LOW: 72h)`);

    return queuedCount > 0;

  } catch (error) {
    console.error('❌ Direct email queuing failed:', error.message);
    return false;
  }
}

// Run the direct queuing
queueEmailsDirectly().then((success) => {
  if (success) {
    console.log('\n🚀 Email automation system is now operational!');
    console.log('📧 Emails will be sent based on lead scores and risk levels');
    console.log('🎯 Professional, trust-building approach maintained');
  } else {
    console.log('\n❌ Email queuing failed or no new emails to queue');
  }
}).catch(error => {
  console.error('❌ Script execution failed:', error);
});
