-- BlackVeil Security - Advanced BI Schema Creation
-- Execute this script in Supabase SQL Editor to create missing advanced BI tables
-- This script is idempotent and safe to run multiple times

-- 1. Create enhanced_lead_scores table
CREATE TABLE IF NOT EXISTS enhanced_lead_scores (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    submission_id UUID NOT NULL REFERENCES assessment_submissions(id) ON DELETE CASCADE,
    completion_score NUMERIC NOT NULL CHECK (completion_score >= 0 AND completion_score <= 100),
    industry_score NUMERIC NOT NULL CHECK (industry_score >= 0 AND industry_score <= 100),
    size_score NUMERIC NOT NULL CHECK (size_score >= 0 AND size_score <= 100),
    engagement_score NUMERIC NOT NULL CHECK (engagement_score >= 0 AND engagement_score <= 100),
    urgency_score NUMERIC NOT NULL CHECK (urgency_score >= 0 AND urgency_score <= 100),
    total_score NUMERIC NOT NULL CHECK (total_score >= 0 AND total_score <= 500),
    conversion_probability NUMERIC NOT NULL CHECK (conversion_probability >= 0 AND conversion_probability <= 100),
    priority_level TEXT NOT NULL CHECK (priority_level IN ('LOW', 'MEDIUM', 'HIGH', 'URGENT')),
    recommended_actions JSONB,
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(submission_id)
);

-- 2. Create user_journey_events table
CREATE TABLE IF NOT EXISTS user_journey_events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event_type TEXT NOT NULL CHECK (event_type IN ('page_view', 'assessment_start', 'assessment_progress', 'assessment_complete', 'result_view', 'contact_form', 'download', 'email_click')),
    page_url TEXT,
    session_id TEXT NOT NULL,
    user_id UUID,
    submission_id UUID REFERENCES assessment_submissions(id) ON DELETE SET NULL,
    event_data JSONB,
    user_agent TEXT,
    ip_address INET,
    referrer TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create ab_tests table
CREATE TABLE IF NOT EXISTS ab_tests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    test_type TEXT NOT NULL CHECK (test_type IN ('assessment_question', 'email_template', 'cta_button', 'landing_page', 'form_layout', 'pricing_display')),
    hypothesis TEXT,
    success_metric TEXT NOT NULL,
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    winner_variant UUID,
    confidence_level NUMERIC CHECK (confidence_level >= 0 AND confidence_level <= 100),
    statistical_significance BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID
);

-- 4. Create ab_test_variants table
CREATE TABLE IF NOT EXISTS ab_test_variants (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    test_id UUID NOT NULL REFERENCES ab_tests(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    config JSONB NOT NULL,
    traffic_percentage NUMERIC NOT NULL CHECK (traffic_percentage >= 0 AND traffic_percentage <= 100),
    participants INTEGER DEFAULT 0,
    conversions INTEGER DEFAULT 0,
    conversion_rate NUMERIC GENERATED ALWAYS AS (
        CASE 
            WHEN participants > 0 THEN (conversions::NUMERIC / participants::NUMERIC * 100)
            ELSE 0
        END
    ) STORED,
    is_control BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create ab_test_participations table
CREATE TABLE IF NOT EXISTS ab_test_participations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    test_id UUID NOT NULL REFERENCES ab_tests(id) ON DELETE CASCADE,
    variant_id UUID NOT NULL REFERENCES ab_test_variants(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL,
    user_id UUID,
    submission_id UUID REFERENCES assessment_submissions(id) ON DELETE SET NULL,
    converted BOOLEAN DEFAULT false,
    conversion_value NUMERIC,
    conversion_timestamp TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(test_id, session_id)
);

-- 6. Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_enhanced_lead_scores_submission_id ON enhanced_lead_scores(submission_id);
CREATE INDEX IF NOT EXISTS idx_enhanced_lead_scores_priority_level ON enhanced_lead_scores(priority_level);
CREATE INDEX IF NOT EXISTS idx_enhanced_lead_scores_conversion_probability ON enhanced_lead_scores(conversion_probability DESC);

CREATE INDEX IF NOT EXISTS idx_user_journey_events_session_id ON user_journey_events(session_id);
CREATE INDEX IF NOT EXISTS idx_user_journey_events_event_type ON user_journey_events(event_type);
CREATE INDEX IF NOT EXISTS idx_user_journey_events_created_at ON user_journey_events(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_journey_events_submission_id ON user_journey_events(submission_id);

CREATE INDEX IF NOT EXISTS idx_ab_tests_is_active ON ab_tests(is_active);
CREATE INDEX IF NOT EXISTS idx_ab_tests_test_type ON ab_tests(test_type);
CREATE INDEX IF NOT EXISTS idx_ab_tests_start_date ON ab_tests(start_date DESC);

CREATE INDEX IF NOT EXISTS idx_ab_test_variants_test_id ON ab_test_variants(test_id);
CREATE INDEX IF NOT EXISTS idx_ab_test_variants_conversion_rate ON ab_test_variants(conversion_rate DESC);

CREATE INDEX IF NOT EXISTS idx_ab_test_participations_test_id ON ab_test_participations(test_id);
CREATE INDEX IF NOT EXISTS idx_ab_test_participations_session_id ON ab_test_participations(session_id);
CREATE INDEX IF NOT EXISTS idx_ab_test_participations_converted ON ab_test_participations(converted);

-- 7. Enable Row Level Security (RLS) on new tables
ALTER TABLE enhanced_lead_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_journey_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE ab_tests ENABLE ROW LEVEL SECURITY;
ALTER TABLE ab_test_variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE ab_test_participations ENABLE ROW LEVEL SECURITY;

-- 8. Create RLS policies for authenticated users (admin access)
CREATE POLICY "Authenticated users can access enhanced_lead_scores" ON enhanced_lead_scores
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can access user_journey_events" ON user_journey_events
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can access ab_tests" ON ab_tests
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can access ab_test_variants" ON ab_test_variants
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can access ab_test_participations" ON ab_test_participations
    FOR ALL USING (auth.role() = 'authenticated');

-- 9. Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_enhanced_lead_scores_updated_at 
    BEFORE UPDATE ON enhanced_lead_scores 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ab_tests_updated_at 
    BEFORE UPDATE ON ab_tests 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 10. Insert sample A/B tests for demonstration
INSERT INTO ab_tests (id, name, description, test_type, hypothesis, success_metric, is_active) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', 'Assessment CTA Button Test', 'Testing different call-to-action button colors and text', 'cta_button', 'Green buttons will have higher conversion than blue buttons', 'assessment_completion_rate', true),
    ('550e8400-e29b-41d4-a716-446655440002', 'Landing Page Layout Test', 'Testing hero section layouts for better engagement', 'landing_page', 'Centered layout will perform better than left-aligned', 'time_on_page', true),
    ('550e8400-e29b-41d4-a716-446655440003', 'Email Template Test', 'Testing follow-up email templates for lead nurturing', 'email_template', 'Personalized emails will have higher open rates', 'email_open_rate', false)
ON CONFLICT (id) DO NOTHING;

-- Insert corresponding variants
INSERT INTO ab_test_variants (test_id, name, description, config, traffic_percentage, is_control) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', 'Control - Blue Button', 'Original blue CTA button', '{"button_color": "blue", "button_text": "Start Assessment"}', 50, true),
    ('550e8400-e29b-41d4-a716-446655440001', 'Variant - Green Button', 'Green CTA button with action text', '{"button_color": "green", "button_text": "Get My Security Score"}', 50, false),
    ('550e8400-e29b-41d4-a716-446655440002', 'Control - Left Layout', 'Original left-aligned hero section', '{"layout": "left", "hero_position": "left"}', 50, true),
    ('550e8400-e29b-41d4-a716-446655440002', 'Variant - Center Layout', 'Centered hero section layout', '{"layout": "center", "hero_position": "center"}', 50, false)
ON CONFLICT DO NOTHING;

-- 11. Verification query (commented out for programmatic execution)
-- SELECT
--     'Advanced BI Schema Creation Complete' as status,
--     (SELECT COUNT(*) FROM enhanced_lead_scores) as enhanced_scores_count,
--     (SELECT COUNT(*) FROM user_journey_events) as journey_events_count,
--     (SELECT COUNT(*) FROM ab_tests) as ab_tests_count,
--     (SELECT COUNT(*) FROM ab_test_variants) as ab_variants_count,
--     (SELECT COUNT(*) FROM ab_test_participations) as ab_participations_count;

-- Success message (commented out for programmatic execution)
-- DO $$
-- BEGIN
--     RAISE NOTICE 'Advanced BI schema creation completed successfully!';
--     RAISE NOTICE 'Created tables: enhanced_lead_scores, user_journey_events, ab_tests, ab_test_variants, ab_test_participations';
--     RAISE NOTICE 'Added indexes, RLS policies, and sample A/B test data';
--     RAISE NOTICE 'Ready for enhanced BI dashboard features';
-- END $$;
