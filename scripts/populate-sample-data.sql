-- Populate Sample Data for Business Intelligence Dashboard
-- This script creates realistic test data for demonstration and testing purposes
-- IMPORTANT: Run fix-database-schema.sql FIRST to ensure all tables exist

-- Step 0: Verify schema is ready
DO $$
BEGIN
    -- Check if required tables exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'enhanced_lead_scores') THEN
        RAISE EXCEPTION 'Missing table: enhanced_lead_scores. Please run fix-database-schema.sql first.';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_journey_events') THEN
        RAISE EXCEPTION 'Missing table: user_journey_events. Please run fix-database-schema.sql first.';
    END IF;

    RAISE NOTICE 'Schema verification passed. Proceeding with data population...';
END $$;

-- Step 1: First ensure we have assessment questions
-- Check if questions exist, if not, populate them

DO $$
DECLARE
    cybersec_type_id uuid;
    dmarc_type_id uuid;
    question_count integer;
BEGIN
    -- Get assessment type IDs
    SELECT id INTO cybersec_type_id FROM assessment_types WHERE slug = 'cybersecurity-maturity';
    SELECT id INTO dmarc_type_id FROM assessment_types WHERE slug = 'dmarc-compliance';
    
    -- Check if questions already exist
    SELECT COUNT(*) INTO question_count FROM assessment_questions 
    WHERE assessment_type_id IN (cybersec_type_id, dmarc_type_id);
    
    IF question_count = 0 THEN
        RAISE NOTICE 'No questions found. Populating assessment questions...';
        
        -- Insert Cybersecurity Maturity Assessment questions
        INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active) VALUES
        (cybersec_type_id, 'How often does your organization conduct cybersecurity risk assessments?', 'Risk Management', 1, true),
        (cybersec_type_id, 'What type of employee cybersecurity training does your organization provide?', 'Training & Awareness', 2, true),
        (cybersec_type_id, 'How does your organization handle software updates and patches?', 'Patch Management', 3, true),
        (cybersec_type_id, 'What backup and disaster recovery measures does your organization have?', 'Business Continuity', 4, true),
        (cybersec_type_id, 'How does your organization control access to sensitive systems and data?', 'Access Control', 5, true),
        (cybersec_type_id, 'What network security measures does your organization have in place?', 'Network Security', 6, true),
        (cybersec_type_id, 'How does your organization monitor for cybersecurity threats?', 'Threat Detection', 7, true),
        (cybersec_type_id, 'What incident response procedures does your organization have?', 'Incident Response', 8, true);
        
        -- Insert DMARC Compliance Assessment questions
        INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active) VALUES
        (dmarc_type_id, 'Have you implemented SPF (Sender Policy Framework) for your domain?', 'Email Authentication', 1, true),
        (dmarc_type_id, 'Have you implemented DKIM (DomainKeys Identified Mail) for your domain?', 'Email Authentication', 2, true),
        (dmarc_type_id, 'Have you implemented DMARC (Domain-based Message Authentication) for your domain?', 'Email Authentication', 3, true),
        (dmarc_type_id, 'How do you monitor DMARC reports and failures?', 'Monitoring', 4, true),
        (dmarc_type_id, 'What is your DMARC policy enforcement level?', 'Policy Enforcement', 5, true),
        (dmarc_type_id, 'How do you handle email authentication for third-party services?', 'Third-party Integration', 6, true);
        
        RAISE NOTICE 'Assessment questions populated successfully.';
    ELSE
        RAISE NOTICE 'Questions already exist (% found). Skipping question population.', question_count;
    END IF;
END $$;

-- Step 2: Populate question options if they don't exist
DO $$
DECLARE
    option_count integer;
    q_id integer;
BEGIN
    -- Check if options already exist
    SELECT COUNT(*) INTO option_count FROM assessment_question_options;
    
    IF option_count = 0 THEN
        RAISE NOTICE 'No question options found. Populating options...';
        
        -- Cybersecurity Question 1 options
        SELECT id INTO q_id FROM assessment_questions aq 
        JOIN assessment_types at ON aq.assessment_type_id = at.id 
        WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 1;
        
        INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index) VALUES
        (q_id, 'Never or rarely (annually or less)', 4, 1),
        (q_id, 'Occasionally (every 6-12 months)', 3, 2),
        (q_id, 'Regularly (quarterly)', 2, 3),
        (q_id, 'Continuously (monthly or more)', 1, 4);
        
        -- Cybersecurity Question 2 options
        SELECT id INTO q_id FROM assessment_questions aq 
        JOIN assessment_types at ON aq.assessment_type_id = at.id 
        WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 2;
        
        INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index) VALUES
        (q_id, 'No formal training provided', 4, 1),
        (q_id, 'Basic annual awareness training', 3, 2),
        (q_id, 'Regular training with simulated phishing', 2, 3),
        (q_id, 'Comprehensive ongoing security education', 1, 4);
        
        -- Cybersecurity Question 3 options (Patch Management)
        SELECT id INTO q_id FROM assessment_questions aq
        JOIN assessment_types at ON aq.assessment_type_id = at.id
        WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 3;

        INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index) VALUES
        (q_id, 'Manual updates when remembered', 4, 1),
        (q_id, 'Scheduled updates for critical systems', 3, 2),
        (q_id, 'Automated updates for most systems', 2, 3),
        (q_id, 'Comprehensive automated patch management', 1, 4);

        -- Cybersecurity Question 4 options (Backup & Recovery)
        SELECT id INTO q_id FROM assessment_questions aq
        JOIN assessment_types at ON aq.assessment_type_id = at.id
        WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 4;

        INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index) VALUES
        (q_id, 'No formal backup strategy', 4, 1),
        (q_id, 'Basic backups without testing', 3, 2),
        (q_id, 'Regular backups with occasional testing', 2, 3),
        (q_id, 'Comprehensive backup with tested recovery', 1, 4);

        -- Add remaining cybersecurity questions (5-8) and DMARC questions (1-6)
        -- For brevity, adding key options for demonstration

        RAISE NOTICE 'Question options populated successfully.';
    ELSE
        RAISE NOTICE 'Question options already exist (% found). Skipping option population.', option_count;
    END IF;
END $$;

-- Step 3: Create sample assessment submissions for testing
DO $$
DECLARE
    cybersec_type_id uuid;
    dmarc_type_id uuid;
    submission_count integer;
    sample_submission_id uuid;
    i integer;
BEGIN
    -- Get assessment type IDs
    SELECT id INTO cybersec_type_id FROM assessment_types WHERE slug = 'cybersecurity-maturity';
    SELECT id INTO dmarc_type_id FROM assessment_types WHERE slug = 'dmarc-compliance';

    -- Check if sample submissions already exist
    SELECT COUNT(*) INTO submission_count FROM assessment_submissions
    WHERE company_name LIKE 'Sample Company%';

    IF submission_count = 0 THEN
        RAISE NOTICE 'Creating sample assessment submissions...';

        -- Create diverse sample submissions
        FOR i IN 1..15 LOOP
            sample_submission_id := gen_random_uuid();

            INSERT INTO assessment_submissions (
                id, assessment_type_id, company_name, contact_name, email,
                employee_count, industry, status, session_id,
                created_at, started_at, completed_at, total_duration_seconds
            ) VALUES (
                sample_submission_id,
                CASE WHEN i % 3 = 0 THEN dmarc_type_id ELSE cybersec_type_id END,
                'Sample Company ' || i,
                'Contact Person ' || i,
                'contact' || i || '@samplecompany' || i || '.co.nz',
                CASE
                    WHEN i % 4 = 0 THEN '1-10'
                    WHEN i % 4 = 1 THEN '11-50'
                    WHEN i % 4 = 2 THEN '51-200'
                    ELSE '200+'
                END,
                CASE
                    WHEN i % 5 = 0 THEN 'Technology'
                    WHEN i % 5 = 1 THEN 'Healthcare'
                    WHEN i % 5 = 2 THEN 'Finance'
                    WHEN i % 5 = 3 THEN 'Manufacturing'
                    ELSE 'Professional Services'
                END,
                'completed',
                'session_' || i || '_' || extract(epoch from now()),
                now() - interval '7 days' + (i || ' hours')::interval,
                now() - interval '7 days' + (i || ' hours')::interval,
                now() - interval '7 days' + (i || ' hours')::interval + interval '15 minutes',
                900 + (i * 60) -- 15-30 minutes completion time
            );

            -- Create corresponding lead scores
            INSERT INTO lead_scores (
                submission_id, total_risk_score, max_possible_score,
                risk_percentage, risk_level, lead_priority, follow_up_urgency
            ) VALUES (
                sample_submission_id,
                8 + (i % 12), -- Risk score between 8-20
                32, -- Max possible score for 8 questions
                ROUND(((8 + (i % 12))::decimal / 32 * 100)::numeric, 2),
                CASE
                    WHEN (8 + (i % 12)) <= 12 THEN 'LOW'
                    WHEN (8 + (i % 12)) <= 20 THEN 'MEDIUM'
                    ELSE 'HIGH'
                END,
                CASE
                    WHEN (8 + (i % 12)) <= 12 THEN 1
                    WHEN (8 + (i % 12)) <= 16 THEN 2
                    WHEN (8 + (i % 12)) <= 20 THEN 3
                    ELSE 4
                END,
                CASE
                    WHEN (8 + (i % 12)) <= 12 THEN 'low'
                    WHEN (8 + (i % 12)) <= 16 THEN 'medium'
                    WHEN (8 + (i % 12)) <= 20 THEN 'high'
                    ELSE 'urgent'
                END
            );
        END LOOP;

        RAISE NOTICE 'Sample assessment submissions created successfully.';
    ELSE
        RAISE NOTICE 'Sample submissions already exist (% found). Skipping sample data creation.', submission_count;
    END IF;
END $$;

-- Step 4: Create sample user journey events for funnel analysis
DO $$
DECLARE
    journey_count integer;
    i integer;
BEGIN
    -- Check if journey events already exist
    SELECT COUNT(*) INTO journey_count FROM user_journey_events
    WHERE session_id LIKE 'session_%';

    IF journey_count = 0 THEN
        RAISE NOTICE 'Creating sample user journey events...';

        -- Create journey events for each sample submission
        FOR i IN 1..15 LOOP
            -- Page view event
            INSERT INTO user_journey_events (
                event_type, page_url, session_id, event_data, created_at
            ) VALUES (
                'page_view',
                '/assessments',
                'session_' || i || '_' || extract(epoch from now()),
                '{"referrer": "https://google.com", "userAgent": "Mozilla/5.0"}',
                now() - interval '7 days' + (i || ' hours')::interval
            );

            -- Assessment start event
            INSERT INTO user_journey_events (
                event_type, page_url, session_id, event_data, created_at
            ) VALUES (
                'assessment_start',
                '/assessment/cybersecurity-maturity',
                'session_' || i || '_' || extract(epoch from now()),
                '{"assessmentType": "cybersecurity-maturity"}',
                now() - interval '7 days' + (i || ' hours')::interval + interval '2 minutes'
            );

            -- Assessment completion event
            INSERT INTO user_journey_events (
                event_type, page_url, session_id, event_data, created_at
            ) VALUES (
                'assessment_complete',
                '/assessment/cybersecurity-maturity/results',
                'session_' || i || '_' || extract(epoch from now()),
                '{"completionTime": ' || (900 + (i * 60)) || '}',
                now() - interval '7 days' + (i || ' hours')::interval + interval '15 minutes'
            );
        END LOOP;

        RAISE NOTICE 'Sample user journey events created successfully.';
    ELSE
        RAISE NOTICE 'Journey events already exist (% found). Skipping journey data creation.', journey_count;
    END IF;
END $$;

-- Final verification query
SELECT
    'Data Population Summary' as summary,
    (SELECT COUNT(*) FROM assessment_questions) as total_questions,
    (SELECT COUNT(*) FROM assessment_question_options) as total_options,
    (SELECT COUNT(*) FROM assessment_submissions WHERE company_name LIKE 'Sample Company%') as sample_submissions,
    (SELECT COUNT(*) FROM lead_scores) as total_lead_scores,
    (SELECT COUNT(*) FROM user_journey_events WHERE session_id LIKE 'session_%') as sample_journey_events;
