-- Populate Missing Assessment Questions
-- Run this script in the Supabase SQL Editor to add questions for assessments that are missing them

-- First, let's get the assessment type IDs we need
-- Cybersecurity Maturity Assessment: d47d75ab-bb7d-4433-a2cc-d07d07a7ac92
-- DMARC Compliance Assessment: 7cda7ab2-0d59-429f-b29e-9279a929d0a4

-- Insert questions for Cybersecurity Maturity Assessment
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active) VALUES
(gen_random_uuid(), 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92', 'How often does your organization conduct cybersecurity risk assessments?', 'Risk Management', 1, true),
(gen_random_uuid(), 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92', 'What type of cybersecurity training do you provide to employees?', 'Security Awareness', 2, true),
(gen_random_uuid(), 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92', 'How does your organization manage software updates and patches?', 'Patch Management', 3, true),
(gen_random_uuid(), 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92', 'What backup and recovery procedures do you have in place?', 'Business Continuity', 4, true),
(gen_random_uuid(), 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92', 'How do you control access to sensitive systems and data?', 'Access Control', 5, true),
(gen_random_uuid(), 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92', 'Do you have an incident response plan for cybersecurity breaches?', 'Incident Response', 6, true),
(gen_random_uuid(), 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92', 'How do you monitor your network for security threats?', 'Threat Detection', 7, true),
(gen_random_uuid(), 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92', 'What is your approach to vendor and third-party security?', 'Third-Party Risk', 8, true);

-- Insert questions for DMARC Compliance Assessment
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active) VALUES
(gen_random_uuid(), '7cda7ab2-0d59-429f-b29e-9279a929d0a4', 'Have you implemented SPF (Sender Policy Framework) for your domain?', 'Email Authentication', 1, true),
(gen_random_uuid(), '7cda7ab2-0d59-429f-b29e-9279a929d0a4', 'Do you have DKIM (DomainKeys Identified Mail) configured?', 'Email Authentication', 2, true),
(gen_random_uuid(), '7cda7ab2-0d59-429f-b29e-9279a929d0a4', 'What is your current DMARC policy setting?', 'DMARC Policy', 3, true),
(gen_random_uuid(), '7cda7ab2-0d59-429f-b29e-9279a929d0a4', 'How do you monitor DMARC reports and failures?', 'Monitoring', 4, true),
(gen_random_uuid(), '7cda7ab2-0d59-429f-b29e-9279a929d0a4', 'Do you have email security awareness training for employees?', 'Security Awareness', 5, true),
(gen_random_uuid(), '7cda7ab2-0d59-429f-b29e-9279a929d0a4', 'How do you handle email from external domains that fail authentication?', 'Email Filtering', 6, true);

-- Now insert the options for Cybersecurity Maturity Assessment questions
-- We need to get the question IDs first, so this will be done in parts

-- For question 1: Risk assessments frequency
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Never or rarely (annually or less)', 4, 1 FROM assessment_questions 
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 1;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Occasionally (every 6-12 months)', 3, 2 FROM assessment_questions 
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 1;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Regularly (quarterly)', 2, 3 FROM assessment_questions 
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 1;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Continuously (monthly or more)', 1, 4 FROM assessment_questions 
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 1;

-- For question 2: Security training
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'No formal training provided', 4, 1 FROM assessment_questions 
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 2;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Basic annual training only', 3, 2 FROM assessment_questions 
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 2;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Regular training with updates', 2, 3 FROM assessment_questions 
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 2;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Comprehensive ongoing training with simulations', 1, 4 FROM assessment_questions 
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 2;

-- For question 3: Patch management
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Manual updates when remembered', 4, 1 FROM assessment_questions 
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 3;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Scheduled monthly updates', 3, 2 FROM assessment_questions 
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 3;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Automated updates for most systems', 2, 3 FROM assessment_questions 
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 3;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Comprehensive automated patch management', 1, 4 FROM assessment_questions 
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 3;

-- For question 4: Backup and recovery
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'No formal backup procedures', 4, 1 FROM assessment_questions 
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 4;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Basic local backups', 3, 2 FROM assessment_questions 
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 4;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Regular offsite backups', 2, 3 FROM assessment_questions 
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 4;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Comprehensive backup with tested recovery', 1, 4 FROM assessment_questions
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 4;

-- For question 5: Access control
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Basic password protection only', 4, 1 FROM assessment_questions
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 5;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Role-based access with passwords', 3, 2 FROM assessment_questions
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 5;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Multi-factor authentication for some systems', 2, 3 FROM assessment_questions
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 5;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Comprehensive MFA and zero-trust approach', 1, 4 FROM assessment_questions
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 5;

-- For question 6: Incident response
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'No formal incident response plan', 4, 1 FROM assessment_questions
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 6;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Basic plan but not tested', 3, 2 FROM assessment_questions
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 6;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Documented plan with some testing', 2, 3 FROM assessment_questions
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 6;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Comprehensive tested plan with regular drills', 1, 4 FROM assessment_questions
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 6;

-- For question 7: Threat monitoring
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'No active monitoring', 4, 1 FROM assessment_questions
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 7;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Basic antivirus software only', 3, 2 FROM assessment_questions
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 7;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Network monitoring with some alerting', 2, 3 FROM assessment_questions
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 7;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Advanced threat detection and response', 1, 4 FROM assessment_questions
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 7;

-- For question 8: Third-party security
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'No vendor security assessments', 4, 1 FROM assessment_questions
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 8;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Basic vendor questionnaires', 3, 2 FROM assessment_questions
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 8;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Regular vendor security reviews', 2, 3 FROM assessment_questions
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 8;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Comprehensive third-party risk management', 1, 4 FROM assessment_questions
WHERE assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92' AND order_index = 8;

-- Now insert options for DMARC Compliance Assessment questions

-- For DMARC question 1: SPF implementation
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'No SPF record configured', 4, 1 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 1;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Basic SPF record with some gaps', 3, 2 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 1;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Comprehensive SPF record configured', 2, 3 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 1;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'SPF with strict policy and monitoring', 1, 4 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 1;

-- For DMARC question 2: DKIM configuration
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'No DKIM signing configured', 4, 1 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 2;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'DKIM for some email services only', 3, 2 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 2;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'DKIM configured for all outbound email', 2, 3 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 2;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'DKIM with key rotation and monitoring', 1, 4 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 2;

-- For DMARC question 3: DMARC policy
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'No DMARC record published', 4, 1 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 3;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'DMARC with ''none'' policy (monitoring only)', 3, 2 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 3;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'DMARC with ''quarantine'' policy', 2, 3 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 3;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'DMARC with ''reject'' policy', 1, 4 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 3;

-- For DMARC question 4: DMARC monitoring
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'No DMARC report monitoring', 4, 1 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 4;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Occasional manual review of reports', 3, 2 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 4;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Regular monitoring with basic analysis', 2, 3 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 4;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Automated monitoring with alerting', 1, 4 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 4;

-- For DMARC question 5: Email security training
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'No email security training', 4, 1 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 5;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Basic annual training', 3, 2 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 5;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Regular training with phishing simulations', 2, 3 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 5;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Comprehensive ongoing training program', 1, 4 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 5;

-- For DMARC question 6: Email filtering
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'No special handling for failed authentication', 4, 1 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 6;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Basic spam filtering only', 3, 2 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 6;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Enhanced filtering with some authentication checks', 2, 3 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 6;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT id, 'Strict authentication-based filtering', 1, 4 FROM assessment_questions
WHERE assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4' AND order_index = 6;

-- Verification query to check what we've added
SELECT
  at.title as assessment_title,
  COUNT(aq.id) as question_count,
  COUNT(aqo.id) as option_count
FROM assessment_types at
LEFT JOIN assessment_questions aq ON at.id = aq.assessment_type_id
LEFT JOIN assessment_question_options aqo ON aq.id = aqo.question_id
WHERE at.slug IN ('cybersecurity-maturity', 'dmarc-compliance')
GROUP BY at.id, at.title
ORDER BY at.title;
