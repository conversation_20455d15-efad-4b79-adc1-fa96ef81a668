#!/usr/bin/env node

/**
 * BlackVeil Security Platform - Image Audit & Optimization Script
 * 
 * This script analyzes the current image usage across the platform and provides
 * recommendations for optimization, accessibility, and performance improvements.
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const CONFIG = {
  imageExtensions: ['.jpg', '.jpeg', '.png', '.webp', '.avif', '.svg'],
  maxFileSize: 2 * 1024 * 1024, // 2MB
  recommendedFormats: ['webp', 'avif'],
  targetQuality: 85,
  responsiveBreakpoints: [320, 640, 768, 1024, 1280, 1920]
};

// Audit results
const auditResults = {
  totalImages: 0,
  oversizedImages: [],
  missingAltText: [],
  unoptimizedFormats: [],
  missingResponsive: [],
  securityIssues: [],
  recommendations: []
};

/**
 * Scans directory for image files
 */
function scanForImages(dir, images = []) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      scanForImages(filePath, images);
    } else if (CONFIG.imageExtensions.some(ext => file.toLowerCase().endsWith(ext))) {
      images.push({
        path: filePath,
        name: file,
        size: stat.size,
        extension: path.extname(file).toLowerCase()
      });
    }
  }
  
  return images;
}

/**
 * Analyzes image usage in source code
 */
function analyzeImageUsage() {
  const sourceFiles = [];
  
  // Scan TypeScript/JavaScript files
  function scanSourceFiles(dir) {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
        scanSourceFiles(filePath);
      } else if (file.endsWith('.tsx') || file.endsWith('.ts') || file.endsWith('.jsx') || file.endsWith('.js')) {
        sourceFiles.push(filePath);
      }
    }
  }
  
  scanSourceFiles('./src');
  
  const imageUsage = [];
  
  for (const sourceFile of sourceFiles) {
    const content = fs.readFileSync(sourceFile, 'utf8');
    
    // Find image references
    const imageRegex = /(?:src|href|url)\s*=\s*["']([^"']*\.(?:jpg|jpeg|png|webp|avif|svg))["']/gi;
    const altRegex = /alt\s*=\s*["']([^"']*)["']/gi;
    
    let match;
    while ((match = imageRegex.exec(content)) !== null) {
      imageUsage.push({
        file: sourceFile,
        imagePath: match[1],
        hasAltText: content.includes('alt=')
      });
    }
  }
  
  return imageUsage;
}

/**
 * Checks for oversized images
 */
function checkImageSizes(images) {
  for (const image of images) {
    if (image.size > CONFIG.maxFileSize) {
      auditResults.oversizedImages.push({
        ...image,
        recommendedAction: `Compress to under ${CONFIG.maxFileSize / 1024 / 1024}MB`
      });
    }
  }
}

/**
 * Checks for unoptimized formats
 */
function checkImageFormats(images) {
  for (const image of images) {
    if (image.extension === '.jpg' || image.extension === '.jpeg' || image.extension === '.png') {
      // Check if WebP version exists
      const webpPath = image.path.replace(image.extension, '.webp');
      if (!fs.existsSync(webpPath)) {
        auditResults.unoptimizedFormats.push({
          ...image,
          recommendedAction: 'Convert to WebP format for better compression'
        });
      }
    }
  }
}

/**
 * Checks for missing alt text
 */
function checkAltText(imageUsage) {
  for (const usage of imageUsage) {
    if (!usage.hasAltText) {
      auditResults.missingAltText.push({
        file: usage.file,
        imagePath: usage.imagePath,
        recommendedAction: 'Add descriptive alt text for accessibility'
      });
    }
  }
}

/**
 * Generates optimization recommendations
 */
function generateRecommendations() {
  const recommendations = [];
  
  // File size recommendations
  if (auditResults.oversizedImages.length > 0) {
    recommendations.push({
      priority: 'HIGH',
      category: 'Performance',
      title: 'Compress oversized images',
      description: `${auditResults.oversizedImages.length} images exceed the recommended 2MB limit`,
      action: 'Use image compression tools or CDN optimization'
    });
  }
  
  // Format optimization
  if (auditResults.unoptimizedFormats.length > 0) {
    recommendations.push({
      priority: 'HIGH',
      category: 'Performance',
      title: 'Convert to modern formats',
      description: `${auditResults.unoptimizedFormats.length} images could benefit from WebP/AVIF conversion`,
      action: 'Implement automatic format conversion in build process'
    });
  }
  
  // Accessibility
  if (auditResults.missingAltText.length > 0) {
    recommendations.push({
      priority: 'HIGH',
      category: 'Accessibility',
      title: 'Add missing alt text',
      description: `${auditResults.missingAltText.length} images lack proper alt text`,
      action: 'Review and add descriptive alt text for all images'
    });
  }
  
  // General recommendations
  recommendations.push({
    priority: 'MEDIUM',
    category: 'SEO',
    title: 'Implement structured data for images',
    description: 'Add schema markup for better search engine understanding',
    action: 'Use ImageObject schema for blog and service images'
  });
  
  recommendations.push({
    priority: 'MEDIUM',
    category: 'Performance',
    title: 'Implement responsive images',
    description: 'Use srcset and sizes attributes for optimal loading',
    action: 'Generate multiple image sizes for different screen resolutions'
  });
  
  recommendations.push({
    priority: 'LOW',
    category: 'Brand',
    title: 'Add professional photography',
    description: 'Replace placeholder images with professional content',
    action: 'Schedule photography session for team and office images'
  });
  
  auditResults.recommendations = recommendations;
}

/**
 * Generates audit report
 */
function generateReport() {
  const report = `
# BlackVeil Security Platform - Image Audit Report
Generated: ${new Date().toISOString()}

## Summary
- **Total Images Found**: ${auditResults.totalImages}
- **Oversized Images**: ${auditResults.oversizedImages.length}
- **Missing Alt Text**: ${auditResults.missingAltText.length}
- **Unoptimized Formats**: ${auditResults.unoptimizedFormats.length}

## Detailed Findings

### Oversized Images (>${CONFIG.maxFileSize / 1024 / 1024}MB)
${auditResults.oversizedImages.map(img => 
  `- ${img.path} (${(img.size / 1024 / 1024).toFixed(2)}MB)`
).join('\n') || 'None found ✅'}

### Missing Alt Text
${auditResults.missingAltText.map(item => 
  `- ${item.file}: ${item.imagePath}`
).join('\n') || 'None found ✅'}

### Unoptimized Formats
${auditResults.unoptimizedFormats.map(img => 
  `- ${img.path} (${img.extension} → WebP recommended)`
).join('\n') || 'None found ✅'}

## Recommendations

${auditResults.recommendations.map(rec => `
### ${rec.priority} PRIORITY: ${rec.title}
**Category**: ${rec.category}
**Description**: ${rec.description}
**Action**: ${rec.action}
`).join('\n')}

## Next Steps

1. **Immediate**: Address HIGH priority issues
2. **This Week**: Implement image optimization pipeline
3. **This Month**: Complete professional photography
4. **Ongoing**: Monitor image performance metrics

---
Generated by BlackVeil Image Audit Script
`;

  return report;
}

/**
 * Main audit function
 */
function runAudit() {
  console.log('🔍 Starting BlackVeil Security Platform Image Audit...\n');
  
  // Scan for images
  console.log('📁 Scanning for images...');
  const images = scanForImages('./public');
  auditResults.totalImages = images.length;
  console.log(`Found ${images.length} images\n`);
  
  // Analyze usage
  console.log('🔎 Analyzing image usage in source code...');
  const imageUsage = analyzeImageUsage();
  console.log(`Found ${imageUsage.length} image references\n`);
  
  // Run checks
  console.log('⚡ Running optimization checks...');
  checkImageSizes(images);
  checkImageFormats(images);
  checkAltText(imageUsage);
  
  // Generate recommendations
  console.log('💡 Generating recommendations...');
  generateRecommendations();
  
  // Generate report
  const report = generateReport();
  
  // Save report
  fs.writeFileSync('./docs/IMAGE_AUDIT_REPORT.md', report);
  
  console.log('✅ Audit complete! Report saved to ./docs/IMAGE_AUDIT_REPORT.md\n');
  
  // Display summary
  console.log('📊 AUDIT SUMMARY:');
  console.log(`Total Images: ${auditResults.totalImages}`);
  console.log(`Issues Found: ${auditResults.oversizedImages.length + auditResults.missingAltText.length + auditResults.unoptimizedFormats.length}`);
  console.log(`Recommendations: ${auditResults.recommendations.length}`);
  
  if (auditResults.recommendations.filter(r => r.priority === 'HIGH').length > 0) {
    console.log('\n⚠️  HIGH PRIORITY ISSUES FOUND - Review the report for details');
  } else {
    console.log('\n✅ No critical issues found!');
  }
}

// Run the audit
if (import.meta.url === `file://${process.argv[1]}`) {
  runAudit();
}

export { runAudit, auditResults };
