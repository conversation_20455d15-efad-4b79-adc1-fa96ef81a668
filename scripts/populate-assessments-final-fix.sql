-- FINAL FIX: Assessment Questions Population Script
-- This script is designed to be run directly in Supabase SQL Editor
-- It bypasses RLS policies and handles all identified issues

-- Step 1: Verify assessment types exist
SELECT 
  'Assessment Types Check' as check_type,
  COUNT(*) as count,
  string_agg(title, ', ') as titles
FROM assessment_types 
WHERE slug IN ('cybersecurity-maturity', 'dmarc-compliance');

-- Step 2: Check current question count
SELECT 
  'Current Questions Check' as check_type,
  COUNT(*) as total_questions
FROM assessment_questions;

-- Step 3: Insert Cybersecurity Maturity Assessment Questions
-- Using explicit integer IDs to match schema - ID column is NOT auto-incrementing
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT
  COALESCE((SELECT MAX(id) FROM assessment_questions), 0) + 1,
  at.id,
  'How often does your organization conduct cybersecurity risk assessments?',
  'Risk Management',
  1,
  true
FROM assessment_types at
WHERE at.slug = 'cybersecurity-maturity'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 1
);

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  at.id,
  'What type of cybersecurity training do you provide to employees?',
  'Security Awareness',
  2,
  true
FROM assessment_types at 
WHERE at.slug = 'cybersecurity-maturity'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 2
);

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  at.id,
  'How does your organization manage software updates and patches?',
  'Patch Management',
  3,
  true
FROM assessment_types at 
WHERE at.slug = 'cybersecurity-maturity'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 3
);

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  at.id,
  'What backup and recovery procedures do you have in place?',
  'Business Continuity',
  4,
  true
FROM assessment_types at 
WHERE at.slug = 'cybersecurity-maturity'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 4
);

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  at.id,
  'How do you control access to sensitive systems and data?',
  'Access Control',
  5,
  true
FROM assessment_types at 
WHERE at.slug = 'cybersecurity-maturity'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 5
);

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  at.id,
  'Do you have an incident response plan for cybersecurity breaches?',
  'Incident Response',
  6,
  true
FROM assessment_types at 
WHERE at.slug = 'cybersecurity-maturity'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 6
);

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  at.id,
  'How do you monitor your network for security threats?',
  'Threat Detection',
  7,
  true
FROM assessment_types at 
WHERE at.slug = 'cybersecurity-maturity'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 7
);

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  at.id,
  'What is your approach to vendor and third-party security?',
  'Third-Party Risk',
  8,
  true
FROM assessment_types at 
WHERE at.slug = 'cybersecurity-maturity'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 8
);

-- Step 4: Insert DMARC Compliance Assessment Questions
INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  at.id,
  'Have you implemented SPF (Sender Policy Framework) for your domain?',
  'Email Authentication',
  1,
  true
FROM assessment_types at 
WHERE at.slug = 'dmarc-compliance'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 1
);

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  at.id,
  'Do you have DKIM (DomainKeys Identified Mail) configured?',
  'Email Authentication',
  2,
  true
FROM assessment_types at 
WHERE at.slug = 'dmarc-compliance'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 2
);

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  at.id,
  'What is your current DMARC policy setting?',
  'DMARC Policy',
  3,
  true
FROM assessment_types at 
WHERE at.slug = 'dmarc-compliance'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 3
);

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  at.id,
  'How do you monitor DMARC reports and failures?',
  'Monitoring',
  4,
  true
FROM assessment_types at 
WHERE at.slug = 'dmarc-compliance'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 4
);

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  at.id,
  'Do you have email security awareness training for employees?',
  'Security Awareness',
  5,
  true
FROM assessment_types at 
WHERE at.slug = 'dmarc-compliance'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 5
);

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT 
  at.id,
  'How do you handle email from external domains that fail authentication?',
  'Email Filtering',
  6,
  true
FROM assessment_types at 
WHERE at.slug = 'dmarc-compliance'
AND NOT EXISTS (
  SELECT 1 FROM assessment_questions aq 
  WHERE aq.assessment_type_id = at.id AND aq.order_index = 6
);

-- Step 5: Verify questions were inserted
SELECT 
  'Questions Inserted' as status,
  at.title,
  COUNT(aq.id) as question_count
FROM assessment_types at
LEFT JOIN assessment_questions aq ON at.id = aq.assessment_type_id
WHERE at.slug IN ('cybersecurity-maturity', 'dmarc-compliance')
GROUP BY at.id, at.title
ORDER BY at.title;
