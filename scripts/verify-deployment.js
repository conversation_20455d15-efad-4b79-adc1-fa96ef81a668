#!/usr/bin/env node

/**
 * BlackVeil Security Platform - Deployment Verification Script
 * 
 * Verifies that the deployment is ready and all systems are working correctly.
 * Checks build artifacts, environment variables, analytics, and image optimizations.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

console.log('🔍 BlackVeil Security - Deployment Verification');
console.log('================================================\n');

let hasErrors = false;
let hasWarnings = false;

const logError = (message) => {
  console.error(`❌ ERROR: ${message}`);
  hasErrors = true;
};

const logWarning = (message) => {
  console.warn(`⚠️  WARNING: ${message}`);
  hasWarnings = true;
};

const logSuccess = (message) => {
  console.log(`✅ ${message}`);
};

const logInfo = (message) => {
  console.log(`ℹ️  ${message}`);
};

// Check 1: Build artifacts
console.log('📦 Checking build artifacts...');
const distPath = path.join(projectRoot, 'dist');
const indexPath = path.join(distPath, 'index.html');
const assetsPath = path.join(distPath, 'assets');

if (!fs.existsSync(distPath)) {
  logError('dist/ directory not found. Run "npm run build" first.');
} else {
  logSuccess('dist/ directory exists');
  
  if (!fs.existsSync(indexPath)) {
    logError('dist/index.html not found');
  } else {
    logSuccess('dist/index.html exists');
    
    // Check index.html content
    const indexContent = fs.readFileSync(indexPath, 'utf8');
    
    // Check for analytics scripts
    if (indexContent.includes('plausible.io')) {
      logSuccess('Plausible analytics script found in index.html');
    } else {
      logWarning('Plausible analytics script not found in index.html');
    }
    
    if (indexContent.includes('googletagmanager.com')) {
      logSuccess('Google Tag Manager script found in index.html');
    } else {
      logWarning('Google Tag Manager script not found in index.html');
    }
    
    // Check for environment variable placeholders
    if (indexContent.includes('VITE_SUPABASE_URL') || indexContent.includes('VITE_SUPABASE_ANON_KEY')) {
      logWarning('Environment variable placeholders found in built HTML - may indicate build issue');
    }
  }
  
  if (!fs.existsSync(assetsPath)) {
    logError('dist/assets/ directory not found');
  } else {
    const assets = fs.readdirSync(assetsPath);
    logSuccess(`dist/assets/ contains ${assets.length} files`);
    
    // Check for main JS and CSS files
    const hasJS = assets.some(file => file.endsWith('.js'));
    const hasCSS = assets.some(file => file.endsWith('.css'));
    
    if (hasJS) {
      logSuccess('JavaScript assets found');
    } else {
      logError('No JavaScript assets found');
    }
    
    if (hasCSS) {
      logSuccess('CSS assets found');
    } else {
      logError('No CSS assets found');
    }
  }
}

// Check 2: Image optimizations
console.log('\n🖼️  Checking image optimizations...');
const uploadsPath = path.join(distPath, 'lovable-uploads');

if (!fs.existsSync(uploadsPath)) {
  logWarning('lovable-uploads directory not found in dist/');
} else {
  const images = fs.readdirSync(uploadsPath);
  const webpImages = images.filter(img => img.endsWith('.webp'));
  const backupImages = images.filter(img => img.startsWith('backup-'));
  const compressedImages = ['4deb7e82-aa37-4ab5-94f4-876c1d8787d4.png', '7982392e-5308-407f-a054-9dcc9a21824f.png', 'dc1e2db4-ccd0-4882-a461-4093ccda5191.png'];
  
  logSuccess(`Found ${images.length} total images`);
  logSuccess(`Found ${webpImages.length} WebP optimized images`);
  logSuccess(`Found ${backupImages.length} backup images`);
  
  // Check if compressed images exist
  let compressedCount = 0;
  compressedImages.forEach(img => {
    if (images.includes(img)) {
      compressedCount++;
    }
  });
  
  if (compressedCount === compressedImages.length) {
    logSuccess('All expected compressed images found');
  } else {
    logWarning(`Only ${compressedCount}/${compressedImages.length} compressed images found`);
  }
  
  // Check for image sitemap
  const imageSitemapPath = path.join(distPath, 'image-sitemap.xml');
  if (fs.existsSync(imageSitemapPath)) {
    logSuccess('Image sitemap found');
  } else {
    logWarning('Image sitemap not found');
  }
}

// Check 3: Environment variables (local)
console.log('\n🔧 Checking environment configuration...');
const envLocalPath = path.join(projectRoot, '.env.local');

if (fs.existsSync(envLocalPath)) {
  logSuccess('.env.local file exists');
  
  const envContent = fs.readFileSync(envLocalPath, 'utf8');
  if (envContent.includes('VITE_SUPABASE_URL=') && envContent.includes('VITE_SUPABASE_ANON_KEY=')) {
    logSuccess('Required environment variables found in .env.local');
  } else {
    logWarning('Required environment variables missing from .env.local');
  }
} else {
  logWarning('.env.local file not found - deployment may need environment variables configured');
}

// Check 4: Package dependencies
console.log('\n📋 Checking package dependencies...');
const packageJsonPath = path.join(projectRoot, 'package.json');
const packageLockPath = path.join(projectRoot, 'package-lock.json');

if (fs.existsSync(packageJsonPath)) {
  logSuccess('package.json exists');
  
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Check for Sharp dependency
  if (packageJson.dependencies?.sharp) {
    logSuccess(`Sharp library found (v${packageJson.dependencies.sharp})`);
  } else {
    logWarning('Sharp library not found in dependencies');
  }
  
  // Check for analytics dependencies
  const analyticsPackages = ['@supabase/supabase-js'];
  analyticsPackages.forEach(pkg => {
    if (packageJson.dependencies?.[pkg]) {
      logSuccess(`${pkg} found`);
    } else {
      logWarning(`${pkg} not found`);
    }
  });
} else {
  logError('package.json not found');
}

if (fs.existsSync(packageLockPath)) {
  logSuccess('package-lock.json exists (good for deployment consistency)');
} else {
  logWarning('package-lock.json not found - may cause deployment inconsistencies');
}

// Check 5: Git status
console.log('\n📝 Checking git status...');
try {
  const { execSync } = await import('child_process');
  
  // Check if dist is tracked
  const gitStatus = execSync('git status --porcelain dist/', { encoding: 'utf8', cwd: projectRoot });
  
  if (gitStatus.trim()) {
    logInfo('dist/ directory has changes (expected for Lovable.dev deployment)');
  } else {
    logSuccess('dist/ directory is clean');
  }
  
  // Check if we're on main branch
  const currentBranch = execSync('git branch --show-current', { encoding: 'utf8', cwd: projectRoot }).trim();
  if (currentBranch === 'main') {
    logSuccess('On main branch');
  } else {
    logInfo(`On branch: ${currentBranch}`);
  }
} catch (error) {
  logWarning('Could not check git status');
}

// Summary
console.log('\n📊 Deployment Verification Summary');
console.log('==================================');

if (hasErrors) {
  console.log('❌ DEPLOYMENT NOT READY - Errors found that must be fixed');
  process.exit(1);
} else if (hasWarnings) {
  console.log('⚠️  DEPLOYMENT READY WITH WARNINGS - Review warnings above');
  console.log('   Most warnings are expected for different deployment environments');
  process.exit(0);
} else {
  console.log('✅ DEPLOYMENT READY - All checks passed');
  process.exit(0);
}
