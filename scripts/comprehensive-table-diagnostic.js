// Comprehensive Table Diagnostic Script
// This script thoroughly investigates the table existence issue

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env.local file
let supabaseUrl;
try {
  const envPath = join(__dirname, '..', '.env.local');
  const envContent = readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  envLines.forEach(line => {
    const [key, value] = line.split('=');
    if (key === 'VITE_SUPABASE_URL') supabaseUrl = value;
  });
} catch (error) {
  console.error('❌ Could not load .env.local file:', error.message);
}

if (!supabaseUrl) {
  console.error('❌ Missing Supabase URL');
  process.exit(1);
}

// Service role key and anon key
const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODU2NjQ1NCwiZXhwIjoyMDY0MTQyNDU0fQ.j1sjkEHL2LR78ChzOL6MdieomYgSk1CNXXR0n5sq9eo';
const anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NjY0NTQsImV4cCI6MjA2NDE0MjQ1NH0.CCoK3cPN0Pe-AVUgePrxmWvf9qZho0b-prGDilfKmZ4';

// Create Supabase clients
const supabaseService = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

const supabaseAnon = createClient(supabaseUrl, anonKey);

async function comprehensiveDiagnostic() {
  console.log('🔍 BlackVeil Security - Comprehensive Table Diagnostic');
  console.log('=' .repeat(60));
  
  const tables = [
    'enhanced_lead_scores',
    'user_journey_events', 
    'ab_tests',
    'ab_test_variants',
    'ab_test_participations'
  ];

  for (const tableName of tables) {
    console.log(`\n🔍 Diagnosing table: ${tableName}`);
    console.log('-' .repeat(40));
    
    // Test 1: Service role access
    console.log('🔐 Test 1: Service role access...');
    try {
      const { count: serviceCount, error: serviceError } = await supabaseService
        .from(tableName)
        .select('*', { count: 'exact', head: true });
      
      if (serviceError) {
        console.log(`   ❌ Service role error: ${serviceError.message}`);
        console.log(`   Code: ${serviceError.code}, Details: ${serviceError.details}`);
      } else {
        console.log(`   ✅ Service role success: ${serviceCount} records`);
      }
    } catch (err) {
      console.log(`   ❌ Service role exception: ${err.message}`);
    }

    // Test 2: Anon role access
    console.log('👤 Test 2: Anon role access...');
    try {
      const { count: anonCount, error: anonError } = await supabaseAnon
        .from(tableName)
        .select('*', { count: 'exact', head: true });
      
      if (anonError) {
        console.log(`   ❌ Anon role error: ${anonError.message}`);
        console.log(`   Code: ${anonError.code}, Details: ${anonError.details}`);
      } else {
        console.log(`   ✅ Anon role success: ${anonCount} records`);
      }
    } catch (err) {
      console.log(`   ❌ Anon role exception: ${err.message}`);
    }

    // Test 3: Direct select with service role
    console.log('📋 Test 3: Direct select with service role...');
    try {
      const { data: serviceData, error: serviceSelectError } = await supabaseService
        .from(tableName)
        .select('*')
        .limit(1);
      
      if (serviceSelectError) {
        console.log(`   ❌ Service select error: ${serviceSelectError.message}`);
      } else {
        console.log(`   ✅ Service select success: ${serviceData?.length || 0} records`);
      }
    } catch (err) {
      console.log(`   ❌ Service select exception: ${err.message}`);
    }

    // Test 4: Direct select with anon role
    console.log('📋 Test 4: Direct select with anon role...');
    try {
      const { data: anonData, error: anonSelectError } = await supabaseAnon
        .from(tableName)
        .select('*')
        .limit(1);
      
      if (anonSelectError) {
        console.log(`   ❌ Anon select error: ${anonSelectError.message}`);
      } else {
        console.log(`   ✅ Anon select success: ${anonData?.length || 0} records`);
      }
    } catch (err) {
      console.log(`   ❌ Anon select exception: ${err.message}`);
    }

    // Test 5: Try to insert a test record (service role)
    console.log('➕ Test 5: Insert test with service role...');
    try {
      let testRecord;
      
      if (tableName === 'enhanced_lead_scores') {
        // Get a submission ID to reference
        const { data: submissions } = await supabaseService
          .from('assessment_submissions')
          .select('id')
          .limit(1);
        
        if (submissions && submissions.length > 0) {
          testRecord = {
            submission_id: submissions[0].id,
            completion_score: 85,
            industry_score: 75,
            size_score: 65,
            engagement_score: 90,
            urgency_score: 70,
            total_score: 385,
            conversion_probability: 77,
            priority_level: 'HIGH'
          };
        }
      } else if (tableName === 'user_journey_events') {
        testRecord = {
          event_type: 'page_view',
          session_id: 'test-session-' + Date.now(),
          page_url: '/test'
        };
      } else if (tableName === 'ab_tests') {
        testRecord = {
          name: 'Test A/B Test',
          test_type: 'cta_button',
          success_metric: 'click_rate'
        };
      } else if (tableName === 'ab_test_variants') {
        // Skip this test for variants as it requires a test_id
        console.log(`   ⏭️ Skipping insert test for ${tableName} (requires foreign key)`);
        continue;
      } else if (tableName === 'ab_test_participations') {
        // Skip this test for participations as it requires foreign keys
        console.log(`   ⏭️ Skipping insert test for ${tableName} (requires foreign keys)`);
        continue;
      }

      if (testRecord) {
        const { data: insertData, error: insertError } = await supabaseService
          .from(tableName)
          .insert(testRecord)
          .select();
        
        if (insertError) {
          console.log(`   ❌ Insert error: ${insertError.message}`);
        } else {
          console.log(`   ✅ Insert success: Record created`);
          
          // Clean up the test record
          if (insertData && insertData.length > 0) {
            await supabaseService
              .from(tableName)
              .delete()
              .eq('id', insertData[0].id);
            console.log(`   🧹 Test record cleaned up`);
          }
        }
      }
    } catch (err) {
      console.log(`   ❌ Insert exception: ${err.message}`);
    }
  }

  // Test 6: Check what tables actually exist
  console.log('\n📊 Test 6: List all tables in public schema...');
  try {
    // Try to get table information using service role
    const { data: tableInfo, error: tableError } = await supabaseService
      .rpc('get_table_info');
    
    if (tableError) {
      console.log(`❌ Table info error: ${tableError.message}`);
    } else {
      console.log(`✅ Table info retrieved: ${JSON.stringify(tableInfo)}`);
    }
  } catch (err) {
    console.log(`❌ Table info exception: ${err.message}`);
  }

  // Test 7: Check working tables for comparison
  console.log('\n✅ Test 7: Control test with known working tables...');
  const workingTables = ['assessment_submissions', 'lead_scores', 'assessment_types'];
  
  for (const workingTable of workingTables) {
    try {
      const { count, error } = await supabaseAnon
        .from(workingTable)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.log(`❌ ${workingTable}: ${error.message}`);
      } else {
        console.log(`✅ ${workingTable}: ${count} records (working)`);
      }
    } catch (err) {
      console.log(`❌ ${workingTable}: ${err.message}`);
    }
  }

  // Test 8: Summary and recommendations
  console.log('\n📋 Summary and Recommendations:');
  console.log('=' .repeat(40));
  
  console.log('🔍 Based on the diagnostic results:');
  console.log('   1. If service role can access but anon cannot: RLS policy issue');
  console.log('   2. If neither can access: Tables do not exist');
  console.log('   3. If both can access: Tables exist and are properly configured');
  console.log('   4. If insert works: Tables are fully functional');
  
  console.log('\n📋 Next steps based on results:');
  console.log('   • If tables missing: Execute SQL schema in Supabase SQL Editor');
  console.log('   • If RLS issues: Update policies to allow anon access');
  console.log('   • If working: Proceed with BI dashboard testing');
}

// Run the comprehensive diagnostic
comprehensiveDiagnostic().then(() => {
  console.log('\n✅ Comprehensive diagnostic completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Diagnostic failed:', error);
  process.exit(1);
});
