-- Safe Assessment Population Script
-- This script dynamically calculates safe ID ranges to avoid conflicts

-- First, let's get the next available ID ranges
DO $$
DECLARE
    next_id INTEGER;
    cybersec_start INTEGER;
    dmarc_start INTEGER;
BEGIN
    -- Get the next available ID (max + 1, or 1 if no questions exist)
    SELECT COALESCE(MAX(id), 0) + 1 INTO next_id FROM assessment_questions;
    
    -- Set starting IDs with safe gaps
    cybersec_start := next_id;
    dmarc_start := next_id + 20; -- Leave gap for cybersecurity questions
    
    -- Insert Cybersecurity Maturity Assessment questions
    INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
    SELECT cybersec_start + 0, id, 'How often does your organization conduct cybersecurity risk assessments?', 'Risk Management', 1, true
    FROM assessment_types WHERE slug = 'cybersecurity-maturity';

    INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
    SELECT cybersec_start + 1, id, 'What type of cybersecurity training do you provide to employees?', 'Security Awareness', 2, true
    FROM assessment_types WHERE slug = 'cybersecurity-maturity';

    INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
    SELECT cybersec_start + 2, id, 'How does your organization manage software updates and patches?', 'Patch Management', 3, true
    FROM assessment_types WHERE slug = 'cybersecurity-maturity';

    INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
    SELECT cybersec_start + 3, id, 'What backup and recovery procedures do you have in place?', 'Business Continuity', 4, true
    FROM assessment_types WHERE slug = 'cybersecurity-maturity';

    INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
    SELECT cybersec_start + 4, id, 'How do you control access to sensitive systems and data?', 'Access Control', 5, true
    FROM assessment_types WHERE slug = 'cybersecurity-maturity';

    INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
    SELECT cybersec_start + 5, id, 'Do you have an incident response plan for cybersecurity breaches?', 'Incident Response', 6, true
    FROM assessment_types WHERE slug = 'cybersecurity-maturity';

    INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
    SELECT cybersec_start + 6, id, 'How do you monitor your network for security threats?', 'Threat Detection', 7, true
    FROM assessment_types WHERE slug = 'cybersecurity-maturity';

    INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
    SELECT cybersec_start + 7, id, 'What is your approach to vendor and third-party security?', 'Third-Party Risk', 8, true
    FROM assessment_types WHERE slug = 'cybersecurity-maturity';

    -- Insert DMARC Compliance Assessment questions
    INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
    SELECT dmarc_start + 0, id, 'Have you implemented SPF (Sender Policy Framework) for your domain?', 'Email Authentication', 1, true
    FROM assessment_types WHERE slug = 'dmarc-compliance';

    INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
    SELECT dmarc_start + 1, id, 'Do you have DKIM (DomainKeys Identified Mail) configured?', 'Email Authentication', 2, true
    FROM assessment_types WHERE slug = 'dmarc-compliance';

    INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
    SELECT dmarc_start + 2, id, 'What is your current DMARC policy setting?', 'DMARC Policy', 3, true
    FROM assessment_types WHERE slug = 'dmarc-compliance';

    INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
    SELECT dmarc_start + 3, id, 'How do you monitor DMARC reports and failures?', 'Monitoring', 4, true
    FROM assessment_types WHERE slug = 'dmarc-compliance';

    INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
    SELECT dmarc_start + 4, id, 'Do you have email security awareness training for employees?', 'Security Awareness', 5, true
    FROM assessment_types WHERE slug = 'dmarc-compliance';

    INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
    SELECT dmarc_start + 5, id, 'How do you handle email from external domains that fail authentication?', 'Email Filtering', 6, true
    FROM assessment_types WHERE slug = 'dmarc-compliance';

    -- Now insert options for Cybersecurity Maturity Assessment
    -- Question 1 options
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 0, 'Never or rarely (annually or less)', 4, 1);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 0, 'Occasionally (every 6-12 months)', 3, 2);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 0, 'Regularly (quarterly)', 2, 3);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 0, 'Continuously (monthly or more)', 1, 4);

    -- Question 2 options
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 1, 'No formal training provided', 4, 1);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 1, 'Basic annual training only', 3, 2);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 1, 'Regular training with updates', 2, 3);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 1, 'Comprehensive ongoing training with simulations', 1, 4);

    -- Question 3 options
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 2, 'Manual updates when remembered', 4, 1);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 2, 'Scheduled monthly updates', 3, 2);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 2, 'Automated updates for most systems', 2, 3);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 2, 'Comprehensive automated patch management', 1, 4);

    -- Question 4 options
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 3, 'No formal backup procedures', 4, 1);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 3, 'Basic local backups', 3, 2);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 3, 'Regular offsite backups', 2, 3);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 3, 'Comprehensive backup with tested recovery', 1, 4);

    -- Question 5 options
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 4, 'Basic password protection only', 4, 1);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 4, 'Role-based access with passwords', 3, 2);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 4, 'Multi-factor authentication for some systems', 2, 3);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 4, 'Comprehensive MFA and zero-trust approach', 1, 4);

    -- Question 6 options
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 5, 'No formal incident response plan', 4, 1);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 5, 'Basic plan but not tested', 3, 2);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 5, 'Documented plan with some testing', 2, 3);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 5, 'Comprehensive tested plan with regular drills', 1, 4);

    -- Question 7 options
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 6, 'No active monitoring', 4, 1);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 6, 'Basic antivirus software only', 3, 2);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 6, 'Network monitoring with some alerting', 2, 3);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 6, 'Advanced threat detection and response', 1, 4);

    -- Question 8 options
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 7, 'No vendor security assessments', 4, 1);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 7, 'Basic vendor questionnaires', 3, 2);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 7, 'Regular vendor security reviews', 2, 3);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (cybersec_start + 7, 'Comprehensive third-party risk management', 1, 4);

    -- Now insert options for DMARC Compliance Assessment
    -- Question 1 options
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 0, 'No SPF record configured', 4, 1);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 0, 'Basic SPF record with some gaps', 3, 2);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 0, 'Comprehensive SPF record configured', 2, 3);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 0, 'SPF with strict policy and monitoring', 1, 4);

    -- Question 2 options
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 1, 'No DKIM signing configured', 4, 1);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 1, 'DKIM for some email services only', 3, 2);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 1, 'DKIM configured for all outbound email', 2, 3);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 1, 'DKIM with key rotation and monitoring', 1, 4);

    -- Question 3 options
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 2, 'No DMARC record published', 4, 1);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 2, 'DMARC with none policy (monitoring only)', 3, 2);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 2, 'DMARC with quarantine policy', 2, 3);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 2, 'DMARC with reject policy', 1, 4);

    -- Question 4 options
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 3, 'No DMARC report monitoring', 4, 1);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 3, 'Occasional manual review of reports', 3, 2);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 3, 'Regular monitoring with basic analysis', 2, 3);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 3, 'Automated monitoring with alerting', 1, 4);

    -- Question 5 options
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 4, 'No email security training', 4, 1);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 4, 'Basic annual training', 3, 2);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 4, 'Regular training with phishing simulations', 2, 3);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 4, 'Comprehensive ongoing training program', 1, 4);

    -- Question 6 options
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 5, 'No special handling for failed authentication', 4, 1);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 5, 'Basic spam filtering only', 3, 2);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 5, 'Enhanced filtering with some authentication checks', 2, 3);
    INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
    VALUES (dmarc_start + 5, 'Strict authentication-based filtering', 1, 4);

    -- Output success message
    RAISE NOTICE 'Successfully inserted questions starting at ID % for cybersecurity-maturity and ID % for dmarc-compliance', cybersec_start, dmarc_start;
END $$;

-- Verification query to check what we have added
SELECT 
  at.title as assessment_title,
  at.slug,
  COUNT(DISTINCT aq.id) as question_count,
  COUNT(aqo.id) as option_count
FROM assessment_types at
LEFT JOIN assessment_questions aq ON at.id = aq.assessment_type_id
LEFT JOIN assessment_question_options aqo ON aq.id = aqo.question_id
WHERE at.slug IN ('cybersecurity-maturity', 'dmarc-compliance')
GROUP BY at.id, at.title, at.slug
ORDER BY at.title;
