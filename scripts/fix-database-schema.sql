-- BlackVeil Security - Database Schema Fix Script
-- This script creates missing tables and fixes data integrity issues

-- 1. <PERSON>reate missing enhanced_lead_scores table
CREATE TABLE IF NOT EXISTS enhanced_lead_scores (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    submission_id UUID NOT NULL REFERENCES assessment_submissions(id) ON DELETE CASCADE,
    completion_score NUMERIC NOT NULL,
    industry_score NUMERIC NOT NULL,
    size_score NUMERIC NOT NULL,
    engagement_score NUMERIC NOT NULL,
    urgency_score NUMERIC NOT NULL,
    total_score NUMERIC NOT NULL,
    conversion_probability NUMERIC NOT NULL,
    priority_level TEXT NOT NULL CHECK (priority_level IN ('LOW', 'MEDIUM', 'HIGH', 'URGENT')),
    recommended_actions JSONB,
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(submission_id)
);

-- 2. <PERSON>reate missing user_journey_events table
CREATE TABLE IF NOT EXISTS user_journey_events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event_type TEXT NOT NULL,
    page_url TEXT,
    session_id TEXT NOT NULL,
    user_id UUID,
    submission_id UUID REFERENCES assessment_submissions(id) ON DELETE SET NULL,
    event_data JSONB,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create missing ab_tests table
CREATE TABLE IF NOT EXISTS ab_tests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    test_type TEXT NOT NULL CHECK (test_type IN ('assessment_question', 'email_template', 'cta_button', 'landing_page')),
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    winner_variant UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create missing ab_test_variants table
CREATE TABLE IF NOT EXISTS ab_test_variants (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    test_id UUID NOT NULL REFERENCES ab_tests(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    config JSONB NOT NULL,
    traffic_percentage NUMERIC NOT NULL CHECK (traffic_percentage >= 0 AND traffic_percentage <= 100),
    participants INTEGER DEFAULT 0,
    conversion_rate NUMERIC
);

-- 5. Create missing ab_test_participations table
CREATE TABLE IF NOT EXISTS ab_test_participations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    test_id UUID NOT NULL REFERENCES ab_tests(id) ON DELETE CASCADE,
    variant_id UUID NOT NULL REFERENCES ab_test_variants(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL,
    user_id UUID,
    submission_id UUID REFERENCES assessment_submissions(id) ON DELETE SET NULL,
    converted BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Fix assessment_types data (insert missing types)
INSERT INTO assessment_types (id, name, title, slug, description, category, estimated_time_minutes, is_active, order_index)
VALUES 
    ('d47d75ab-bb7d-4433-a2cc-d07d07a7ac92', 'cybersecurity-maturity', 'Cybersecurity Maturity Assessment', 'cybersecurity-maturity', 'Comprehensive assessment of your organization''s cybersecurity posture and maturity level', 'Security', 15, true, 1),
    ('7cda7ab2-0d59-429f-b29e-9279a929d0a4', 'dmarc-compliance', 'DMARC Compliance Assessment', 'dmarc-compliance', 'Evaluate your email authentication and DMARC implementation', 'Email Security', 10, true, 2)
ON CONFLICT (id) DO NOTHING;

-- 7. Update existing questions to link to correct assessment types
UPDATE assessment_questions 
SET assessment_type_id = 'd47d75ab-bb7d-4433-a2cc-d07d07a7ac92'
WHERE category IN ('Risk Management', 'Training & Awareness', 'Patch Management', 'Business Continuity', 'Access Control', 'Network Security', 'Threat Detection', 'Incident Response');

UPDATE assessment_questions 
SET assessment_type_id = '7cda7ab2-0d59-429f-b29e-9279a929d0a4'
WHERE category IN ('Email Authentication', 'Monitoring', 'Policy Enforcement', 'Third-party Integration');

-- 8. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_enhanced_lead_scores_submission_id ON enhanced_lead_scores(submission_id);
CREATE INDEX IF NOT EXISTS idx_user_journey_events_session_id ON user_journey_events(session_id);
CREATE INDEX IF NOT EXISTS idx_user_journey_events_event_type ON user_journey_events(event_type);
CREATE INDEX IF NOT EXISTS idx_ab_test_participations_test_id ON ab_test_participations(test_id);
CREATE INDEX IF NOT EXISTS idx_ab_test_participations_session_id ON ab_test_participations(session_id);

-- 9. Enable Row Level Security (RLS) on new tables
ALTER TABLE enhanced_lead_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_journey_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE ab_tests ENABLE ROW LEVEL SECURITY;
ALTER TABLE ab_test_variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE ab_test_participations ENABLE ROW LEVEL SECURITY;

-- 10. Create RLS policies for admin access
CREATE POLICY "Admin full access to enhanced_lead_scores" ON enhanced_lead_scores
    FOR ALL USING (auth.jwt() ->> 'role' = 'authenticated');

CREATE POLICY "Admin full access to user_journey_events" ON user_journey_events
    FOR ALL USING (auth.jwt() ->> 'role' = 'authenticated');

CREATE POLICY "Admin full access to ab_tests" ON ab_tests
    FOR ALL USING (auth.jwt() ->> 'role' = 'authenticated');

CREATE POLICY "Admin full access to ab_test_variants" ON ab_test_variants
    FOR ALL USING (auth.jwt() ->> 'role' = 'authenticated');

CREATE POLICY "Admin full access to ab_test_participations" ON ab_test_participations
    FOR ALL USING (auth.jwt() ->> 'role' = 'authenticated');

-- 11. Verification query
SELECT 
    'Schema Fix Summary' as summary,
    (SELECT COUNT(*) FROM assessment_types) as assessment_types_count,
    (SELECT COUNT(*) FROM assessment_questions) as questions_count,
    (SELECT COUNT(*) FROM assessment_question_options) as options_count,
    (SELECT COUNT(*) FROM assessment_submissions) as submissions_count,
    (SELECT COUNT(*) FROM lead_scores) as lead_scores_count,
    (SELECT COUNT(*) FROM enhanced_lead_scores) as enhanced_scores_count,
    (SELECT COUNT(*) FROM user_journey_events) as journey_events_count,
    (SELECT COUNT(*) FROM ab_tests) as ab_tests_count;
