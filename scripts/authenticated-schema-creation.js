// Authenticated Advanced BI Schema Creation Script
// This script uses database credentials to execute SQL schema creation directly

import { createClient } from '@supabase/supabase-js';
import { readFileSync, writeFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { spawn } from 'child_process';
import { createReadStream } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env.local file
let supabaseUrl, supabaseKey;
try {
  const envPath = join(__dirname, '..', '.env.local');
  const envContent = readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  envLines.forEach(line => {
    const [key, value] = line.split('=');
    if (key === 'VITE_SUPABASE_URL') supabaseUrl = value;
    if (key === 'VITE_SUPABASE_ANON_KEY') supabaseKey = value;
  });
} catch (error) {
  console.error('❌ Could not load .env.local file:', error.message);
}

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

// Extract database connection details from Supabase URL
function parseSupabaseUrl(url) {
  try {
    const urlObj = new URL(url);
    const projectRef = urlObj.hostname.split('.')[0];
    
    return {
      host: `db.${projectRef}.supabase.co`,
      port: 5432,
      database: 'postgres',
      username: 'postgres',
      projectRef: projectRef
    };
  } catch (err) {
    console.error('❌ Could not parse Supabase URL:', err.message);
    return null;
  }
}

// Function to execute SQL using psql with password
async function executeSQLWithCredentials(sqlFilePath, connectionDetails, password) {
  return new Promise((resolve, reject) => {
    console.log('🔄 Executing SQL using authenticated database connection...');
    
    // Create a temporary .pgpass file for password authentication
    const pgpassPath = join(__dirname, '.pgpass_temp');
    const pgpassContent = `${connectionDetails.host}:${connectionDetails.port}:${connectionDetails.database}:${connectionDetails.username}:${password}`;
    
    try {
      writeFileSync(pgpassPath, pgpassContent, { mode: 0o600 });
      console.log('🔐 Created temporary password file');
    } catch (err) {
      console.error('❌ Could not create password file:', err.message);
      reject(err);
      return;
    }

    const psqlArgs = [
      '-h', connectionDetails.host,
      '-p', connectionDetails.port.toString(),
      '-d', connectionDetails.database,
      '-U', connectionDetails.username,
      '-f', sqlFilePath,
      '--single-transaction',
      '--set', 'ON_ERROR_STOP=1'
    ];

    console.log(`📝 Executing: psql ${psqlArgs.filter(arg => !arg.includes('password')).join(' ')}`);

    const psql = spawn('psql', psqlArgs, {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: {
        ...process.env,
        PGPASSFILE: pgpassPath
      }
    });

    let stdout = '';
    let stderr = '';

    psql.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log('📋', output.trim());
    });

    psql.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      // Only show actual errors, not notices
      if (output.includes('ERROR') || output.includes('FATAL')) {
        console.error('❌', output.trim());
      } else {
        console.log('ℹ️', output.trim());
      }
    });

    psql.on('close', (code) => {
      // Clean up temporary password file
      try {
        const fs = require('fs');
        fs.unlinkSync(pgpassPath);
        console.log('🧹 Cleaned up temporary password file');
      } catch (err) {
        console.warn('⚠️ Could not clean up password file:', err.message);
      }

      if (code === 0) {
        console.log('✅ SQL execution completed successfully');
        resolve({ success: true, stdout, stderr });
      } else {
        console.error(`❌ psql exited with code ${code}`);
        reject(new Error(`SQL execution failed with code ${code}\nSTDOUT: ${stdout}\nSTDERR: ${stderr}`));
      }
    });

    psql.on('error', (err) => {
      // Clean up password file on error
      try {
        const fs = require('fs');
        fs.unlinkSync(pgpassPath);
      } catch (cleanupErr) {
        // Ignore cleanup errors
      }

      if (err.code === 'ENOENT') {
        console.error('❌ psql command not found');
        console.log('📋 Please install PostgreSQL client tools:');
        console.log('   macOS: brew install postgresql');
        console.log('   Ubuntu: sudo apt-get install postgresql-client');
        console.log('   Windows: Download from https://www.postgresql.org/download/');
        reject(new Error('psql not available'));
      } else {
        reject(err);
      }
    });
  });
}

// Alternative: Use node-postgres for direct connection
async function executeSQLWithNodePostgres(sqlContent, connectionDetails, password) {
  try {
    console.log('🔄 Attempting to use node-postgres for direct connection...');
    
    // Try to import pg (PostgreSQL client for Node.js)
    let pg;
    try {
      pg = await import('pg');
    } catch (importErr) {
      console.log('📦 Installing pg (PostgreSQL client)...');
      
      // Install pg package
      const { spawn } = require('child_process');
      await new Promise((resolve, reject) => {
        const npm = spawn('npm', ['install', 'pg'], { stdio: 'inherit' });
        npm.on('close', (code) => {
          if (code === 0) resolve();
          else reject(new Error(`npm install failed with code ${code}`));
        });
      });
      
      // Try import again
      pg = await import('pg');
    }

    const { Client } = pg.default;
    
    const client = new Client({
      host: connectionDetails.host,
      port: connectionDetails.port,
      database: connectionDetails.database,
      user: connectionDetails.username,
      password: password,
      ssl: { rejectUnauthorized: false }
    });

    console.log('🔗 Connecting to database...');
    await client.connect();
    console.log('✅ Connected to database');

    console.log('📝 Executing SQL schema...');
    const result = await client.query(sqlContent);
    
    console.log('✅ SQL executed successfully');
    console.log(`📊 Result: ${JSON.stringify(result.rows?.slice(0, 3) || [])}`);

    await client.end();
    console.log('🔌 Database connection closed');

    return { success: true, result };

  } catch (err) {
    console.error('❌ Node-postgres execution failed:', err.message);
    throw err;
  }
}

// Function to verify table creation
async function verifyTablesCreation() {
  console.log('\n🔍 Verifying Advanced BI Tables Creation...');
  
  const supabase = createClient(supabaseUrl, supabaseKey);
  const tables = [
    'enhanced_lead_scores',
    'user_journey_events', 
    'ab_tests',
    'ab_test_variants',
    'ab_test_participations'
  ];

  const results = {};
  let allExist = true;
  
  for (const table of tables) {
    try {
      const { count, error } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.log(`❌ ${table}: ${error.message}`);
        results[table] = false;
        allExist = false;
      } else {
        console.log(`✅ ${table}: Table exists (${count || 0} records)`);
        results[table] = true;
      }
    } catch (err) {
      console.log(`❌ ${table}: ${err.message}`);
      results[table] = false;
      allExist = false;
    }
  }

  return { allExist, results };
}

// Main execution function
async function executeAuthenticatedSchemaCreation(password) {
  console.log('🚀 BlackVeil Security - Authenticated Schema Creation');
  console.log('=' .repeat(60));

  try {
    // Parse connection details
    const connectionDetails = parseSupabaseUrl(supabaseUrl);
    if (!connectionDetails) {
      throw new Error('Could not parse Supabase connection details');
    }

    console.log(`\n🔗 Database Connection Details:`);
    console.log(`   Host: ${connectionDetails.host}`);
    console.log(`   Port: ${connectionDetails.port}`);
    console.log(`   Database: ${connectionDetails.database}`);
    console.log(`   Username: ${connectionDetails.username}`);

    // Read SQL schema file
    const sqlFilePath = join(__dirname, 'create-advanced-bi-schema.sql');
    console.log(`\n📖 Reading SQL file: ${sqlFilePath}`);
    
    const sqlContent = readFileSync(sqlFilePath, 'utf8');
    console.log(`✅ SQL file loaded (${sqlContent.length} characters)`);

    // Try node-postgres first (more reliable)
    try {
      console.log('\n🔄 Method 1: Using node-postgres direct connection...');
      await executeSQLWithNodePostgres(sqlContent, connectionDetails, password);
      
    } catch (nodePostgresError) {
      console.log('\n⚠️ Node-postgres failed, trying psql...');
      console.log(`   Error: ${nodePostgresError.message}`);
      
      // Fallback to psql
      console.log('\n🔄 Method 2: Using psql command line...');
      await executeSQLWithCredentials(sqlFilePath, connectionDetails, password);
    }

    // Verify creation
    console.log('\n🔍 Verifying table creation...');
    const verification = await verifyTablesCreation();
    
    if (verification.allExist) {
      console.log('\n🎉 All advanced BI tables created successfully!');
      console.log('📊 Schema creation completed');
      return true;
    } else {
      console.log('\n⚠️ Some tables may not have been created properly');
      console.log('📋 Please check the errors above');
      return false;
    }

  } catch (error) {
    console.error('❌ Authenticated schema creation failed:', error.message);
    return false;
  }
}

// Export the function for use with password
export { executeAuthenticatedSchemaCreation };

// If run directly, prompt for password
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('🔐 BlackVeil Security - Authenticated Database Schema Creation');
  console.log('=' .repeat(60));
  console.log('📋 This script requires your Supabase database password');
  console.log('⚠️ The password will be used securely and not stored');
  console.log('\n🔍 You can find your database password in:');
  console.log('   Supabase Dashboard → Settings → Database → Connection string');
  console.log('   Or use the password you set when creating the project');
  
  // Get password from command line argument or prompt
  const password = process.argv[2];
  
  if (!password) {
    console.log('\n📋 Usage: node scripts/authenticated-schema-creation.js <database_password>');
    console.log('   Example: node scripts/authenticated-schema-creation.js your_db_password');
    process.exit(1);
  }

  executeAuthenticatedSchemaCreation(password).then((success) => {
    if (success) {
      console.log('\n✅ Schema creation completed successfully!');
      console.log('📋 Next steps:');
      console.log('   1. Run: node scripts/database-inspection.js');
      console.log('   2. Test the BI dashboard at /admin');
      process.exit(0);
    } else {
      console.log('\n❌ Schema creation failed');
      process.exit(1);
    }
  }).catch(error => {
    console.error('❌ Script execution failed:', error);
    process.exit(1);
  });
}
