-- CORRECTED Assessment Questions Population Script
-- This script addresses the identified issues:
-- 1. Uses integer IDs (not UUIDs) to match schema
-- 2. Designed to be run directly in Supabase SQL Editor (bypasses RLS)
-- 3. Handles data type consistency
-- 4. Includes proper error handling

-- First, check if we already have questions to avoid duplicates
DO $$
DECLARE
    cybersec_count INTEGER;
    dmarc_count INTEGER;
    cybersec_type_id UUID;
    dmarc_type_id UUID;
    next_question_id INTEGER;
BEGIN
    -- Get assessment type IDs
    SELECT id INTO cybersec_type_id FROM assessment_types WHERE slug = 'cybersecurity-maturity';
    SELECT id INTO dmarc_type_id FROM assessment_types WHERE slug = 'dmarc-compliance';
    
    IF cybersec_type_id IS NULL THEN
        RAISE EXCEPTION 'Assessment type "cybersecurity-maturity" not found. Please ensure assessment types are created first.';
    END IF;
    
    IF dmarc_type_id IS NULL THEN
        RAISE EXCEPTION 'Assessment type "dmarc-compliance" not found. Please ensure assessment types are created first.';
    END IF;
    
    -- Check existing questions
    SELECT COUNT(*) INTO cybersec_count FROM assessment_questions WHERE assessment_type_id = cybersec_type_id;
    SELECT COUNT(*) INTO dmarc_count FROM assessment_questions WHERE assessment_type_id = dmarc_type_id;
    
    RAISE NOTICE 'Current state: Cybersecurity questions: %, DMARC questions: %', cybersec_count, dmarc_count;
    
    -- Get next available question ID
    SELECT COALESCE(MAX(id), 0) + 1 INTO next_question_id FROM assessment_questions;
    RAISE NOTICE 'Starting question ID: %', next_question_id;
    
    -- Only proceed if we don't already have questions
    IF cybersec_count = 0 THEN
        RAISE NOTICE 'Inserting Cybersecurity Maturity Assessment questions...';
        
        -- Insert Cybersecurity questions with explicit integer IDs
        INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active) VALUES
        (next_question_id + 0, cybersec_type_id, 'How often does your organization conduct cybersecurity risk assessments?', 'Risk Management', 1, true),
        (next_question_id + 1, cybersec_type_id, 'What type of cybersecurity training do you provide to employees?', 'Security Awareness', 2, true),
        (next_question_id + 2, cybersec_type_id, 'How does your organization manage software updates and patches?', 'Patch Management', 3, true),
        (next_question_id + 3, cybersec_type_id, 'What backup and recovery procedures do you have in place?', 'Business Continuity', 4, true),
        (next_question_id + 4, cybersec_type_id, 'How do you control access to sensitive systems and data?', 'Access Control', 5, true),
        (next_question_id + 5, cybersec_type_id, 'Do you have an incident response plan for cybersecurity breaches?', 'Incident Response', 6, true),
        (next_question_id + 6, cybersec_type_id, 'How do you monitor your network for security threats?', 'Threat Detection', 7, true),
        (next_question_id + 7, cybersec_type_id, 'What is your approach to vendor and third-party security?', 'Third-Party Risk', 8, true);
        
        RAISE NOTICE 'Inserted % cybersecurity questions', 8;
    ELSE
        RAISE NOTICE 'Skipping cybersecurity questions - % already exist', cybersec_count;
    END IF;
    
    IF dmarc_count = 0 THEN
        RAISE NOTICE 'Inserting DMARC Compliance Assessment questions...';
        
        -- Insert DMARC questions (starting after cybersecurity questions)
        INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active) VALUES
        (next_question_id + 10, dmarc_type_id, 'Have you implemented SPF (Sender Policy Framework) for your domain?', 'Email Authentication', 1, true),
        (next_question_id + 11, dmarc_type_id, 'Do you have DKIM (DomainKeys Identified Mail) configured?', 'Email Authentication', 2, true),
        (next_question_id + 12, dmarc_type_id, 'What is your current DMARC policy setting?', 'DMARC Policy', 3, true),
        (next_question_id + 13, dmarc_type_id, 'How do you monitor DMARC reports and failures?', 'Monitoring', 4, true),
        (next_question_id + 14, dmarc_type_id, 'Do you have email security awareness training for employees?', 'Security Awareness', 5, true),
        (next_question_id + 15, dmarc_type_id, 'How do you handle email from external domains that fail authentication?', 'Email Filtering', 6, true);
        
        RAISE NOTICE 'Inserted % DMARC questions', 6;
    ELSE
        RAISE NOTICE 'Skipping DMARC questions - % already exist', dmarc_count;
    END IF;
    
END $$;

-- Now insert the question options for all questions
DO $$
DECLARE
    q_id INTEGER;
BEGIN
    RAISE NOTICE 'Inserting question options...';

    -- Cybersecurity Question 1: Risk assessments frequency
    SELECT id INTO q_id FROM assessment_questions aq
    JOIN assessment_types at ON aq.assessment_type_id = at.id
    WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 1;

    IF q_id IS NOT NULL THEN
        INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index) VALUES
        (q_id, 'Never or rarely (annually or less)', 4, 1),
        (q_id, 'Occasionally (every 6-12 months)', 3, 2),
        (q_id, 'Regularly (quarterly)', 2, 3),
        (q_id, 'Continuously (monthly or more)', 1, 4);
    END IF;

    -- Cybersecurity Question 2: Security training
    SELECT id INTO q_id FROM assessment_questions aq
    JOIN assessment_types at ON aq.assessment_type_id = at.id
    WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 2;

    IF q_id IS NOT NULL THEN
        INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index) VALUES
        (q_id, 'No formal training provided', 4, 1),
        (q_id, 'Basic annual training only', 3, 2),
        (q_id, 'Regular training with updates', 2, 3),
        (q_id, 'Comprehensive ongoing training with simulations', 1, 4);
    END IF;

    -- Cybersecurity Question 3: Patch management
    SELECT id INTO q_id FROM assessment_questions aq
    JOIN assessment_types at ON aq.assessment_type_id = at.id
    WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 3;

    IF q_id IS NOT NULL THEN
        INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index) VALUES
        (q_id, 'Manual updates when remembered', 4, 1),
        (q_id, 'Scheduled monthly updates', 3, 2),
        (q_id, 'Automated updates for most systems', 2, 3),
        (q_id, 'Comprehensive automated patch management', 1, 4);
    END IF;

    -- Cybersecurity Question 4: Backup and recovery
    SELECT id INTO q_id FROM assessment_questions aq
    JOIN assessment_types at ON aq.assessment_type_id = at.id
    WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 4;

    IF q_id IS NOT NULL THEN
        INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index) VALUES
        (q_id, 'No formal backup procedures', 4, 1),
        (q_id, 'Basic local backups', 3, 2),
        (q_id, 'Regular offsite backups', 2, 3),
        (q_id, 'Comprehensive backup with tested recovery', 1, 4);
    END IF;

    RAISE NOTICE 'Completed first 4 cybersecurity question options';
END $$;
