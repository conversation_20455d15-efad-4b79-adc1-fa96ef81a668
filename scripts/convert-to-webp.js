#!/usr/bin/env node

/**
 * BlackVeil Security Platform - PNG to WebP Conversion Script
 * 
 * Converts PNG files to WebP format for better compression and performance.
 * Creates WebP versions alongside original files for progressive enhancement.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const CONFIG = {
  webpQuality: 85,
  effort: 6, // WebP compression effort (0-6, higher = better compression)
  preserveOriginal: true
};

// PNG files that need WebP conversion (from audit)
const PNG_FILES = [
  '0cc02049-f173-48e7-93d4-d0405ee75657.png',
  '3857aeda-44a1-477c-8ba5-4094972a5968.png',
  '497cce26-2e85-4fb5-8c91-983cebc78bc4.png',
  '9bfd7c51-c9fc-477a-af08-5e9e92bf859a.png',
  'a370e317-0b2d-4728-bd60-414882929bdb.png',
  'b83ce18c-eb5d-4e30-ad2b-17858e544962.png',
  'bcf0e73f-ca81-446c-8265-d52694bc26a8.png',
  'da195ee7-f00d-43ea-b0b3-fc0407b14e57.png',
  'e171b7ae-495e-401e-ad43-47b786a9e724.png',
  'e600a731-e377-4da2-86a9-e463920147cb.png',
  'eb1605e3-60e6-45f7-8e76-b5519c8b68a0.png',
  'ed9cd95a-b550-48a6-9f75-6dc819e136c6.png',
  'f5571c80-738e-4366-b61c-69a9d2ade2de.png'
];

/**
 * Convert PNG to WebP using sharp
 */
async function convertToWebP(inputPath, outputPath) {
  try {
    const sharp = await import('sharp');
    
    const image = sharp.default(inputPath);
    const metadata = await image.metadata();
    
    console.log(`  Original: ${metadata.width}x${metadata.height}, ${metadata.format}`);
    
    await image
      .webp({
        quality: CONFIG.webpQuality,
        effort: CONFIG.effort,
        lossless: false
      })
      .toFile(outputPath);
    
    const originalStats = fs.statSync(inputPath);
    const webpStats = fs.statSync(outputPath);
    
    const savings = ((originalStats.size - webpStats.size) / originalStats.size * 100).toFixed(1);
    
    console.log(`  WebP: ${(webpStats.size / 1024).toFixed(1)}KB (${savings}% smaller)`);
    
    return {
      success: true,
      originalSize: originalStats.size,
      webpSize: webpStats.size,
      savings: parseFloat(savings)
    };
  } catch (error) {
    console.error(`  Error converting to WebP: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Convert a single PNG file to WebP
 */
async function convertPngFile(fileName) {
  const inputPath = path.join(process.cwd(), 'public', 'lovable-uploads', fileName);
  const webpFileName = fileName.replace('.png', '.webp');
  const outputPath = path.join(process.cwd(), 'public', 'lovable-uploads', webpFileName);
  
  if (!fs.existsSync(inputPath)) {
    console.log(`❌ PNG file not found: ${fileName}`);
    return { success: false, error: 'File not found' };
  }
  
  // Check if WebP version already exists
  if (fs.existsSync(outputPath)) {
    console.log(`⚠️  WebP version already exists: ${webpFileName}`);
    const originalStats = fs.statSync(inputPath);
    const webpStats = fs.statSync(outputPath);
    const savings = ((originalStats.size - webpStats.size) / originalStats.size * 100).toFixed(1);
    
    return {
      success: true,
      originalSize: originalStats.size,
      webpSize: webpStats.size,
      savings: parseFloat(savings),
      skipped: true
    };
  }
  
  const originalStats = fs.statSync(inputPath);
  console.log(`🔄 Converting: ${fileName}`);
  console.log(`  Original size: ${(originalStats.size / 1024).toFixed(1)}KB`);
  
  const result = await convertToWebP(inputPath, outputPath);
  
  if (result.success) {
    console.log(`  ✅ Created: ${webpFileName}`);
  } else {
    console.log(`  ❌ Conversion failed: ${result.error}`);
  }
  
  return { fileName, webpFileName, ...result };
}

/**
 * Convert all PNG files to WebP
 */
async function convertAllPngFiles() {
  console.log('🔄 Starting PNG to WebP conversion for BlackVeil Security Platform...\n');
  
  const results = [];
  
  for (const fileName of PNG_FILES) {
    const result = await convertPngFile(fileName);
    results.push(result);
    console.log(''); // Add spacing between files
  }
  
  // Summary
  console.log('📊 CONVERSION SUMMARY:');
  console.log('======================');
  
  let totalOriginalSize = 0;
  let totalWebpSize = 0;
  let successCount = 0;
  let skippedCount = 0;
  
  for (const result of results) {
    if (result.success) {
      totalOriginalSize += result.originalSize || 0;
      totalWebpSize += result.webpSize || 0;
      
      if (result.skipped) {
        skippedCount++;
        console.log(`⏭️  ${result.fileName}: Already exists (${result.savings}% savings)`);
      } else {
        successCount++;
        console.log(`✅ ${result.fileName}: ${result.savings}% savings`);
      }
    } else {
      console.log(`❌ ${result.fileName}: ${result.error}`);
    }
  }
  
  if (successCount > 0 || skippedCount > 0) {
    const totalSavings = totalOriginalSize > 0 ? 
      ((totalOriginalSize - totalWebpSize) / totalOriginalSize * 100).toFixed(1) : 0;
    
    console.log(`\n🎉 Total WebP savings: ${totalSavings}% (${(totalOriginalSize / 1024).toFixed(1)}KB → ${(totalWebpSize / 1024).toFixed(1)}KB)`);
    console.log(`📈 Performance improvement: ${successCount} new WebP files created, ${skippedCount} already existed`);
  }
  
  console.log('\n💡 NEXT STEPS:');
  console.log('- Update image references to use WebP with PNG fallbacks');
  console.log('- Test WebP support detection in browsers');
  console.log('- Monitor page load performance improvements');
  console.log('- Consider implementing automatic WebP serving');
  
  return results;
}

/**
 * Update image references to use WebP with fallbacks
 */
function generateWebPUsageExamples() {
  console.log('\n📝 WEBP USAGE EXAMPLES:');
  console.log('=======================');
  
  console.log('\n1. Using Picture Element (Recommended):');
  console.log(`
<picture>
  <source srcSet="/lovable-uploads/image.webp" type="image/webp" />
  <img src="/lovable-uploads/image.png" alt="Description" />
</picture>
  `);
  
  console.log('\n2. Using OptimizedImage Component:');
  console.log(`
<OptimizedImage
  src="/lovable-uploads/image.png"
  alt="Description"
  webpSupport={true}
  className="w-full h-auto"
/>
  `);
  
  console.log('\n3. CSS Background Images:');
  console.log(`
.hero-bg {
  background-image: url('/lovable-uploads/image.png');
}

.webp .hero-bg {
  background-image: url('/lovable-uploads/image.webp');
}
  `);
}

/**
 * Check Sharp availability
 */
async function checkSharpAvailability() {
  try {
    await import('sharp');
    return true;
  } catch (e) {
    return false;
  }
}

/**
 * Main conversion function
 */
async function main() {
  // Check if sharp is available
  if (!(await checkSharpAvailability())) {
    console.log('❌ Sharp library not found!');
    console.log('Please install sharp: npm install sharp');
    process.exit(1);
  }
  
  const results = await convertAllPngFiles();
  generateWebPUsageExamples();
  
  // Return results for potential use by other scripts
  return results;
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { convertAllPngFiles, convertPngFile };
