// Simple Table Test Script
// Quick test to see what's happening with the tables

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://wikngnwwakatokbgvenw.supabase.co';
const anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NjY0NTQsImV4cCI6MjA2NDE0MjQ1NH0.CCoK3cPN0Pe-AVUgePrxmWvf9qZho0b-prGDilfKmZ4';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODU2NjQ1NCwiZXhwIjoyMDY0MTQyNDU0fQ.j1sjkEHL2LR78ChzOL6MdieomYgSk1CNXXR0n5sq9eo';

const supabaseAnon = createClient(supabaseUrl, anonKey);
const supabaseService = createClient(supabaseUrl, serviceKey);

async function simpleTest() {
  console.log('🔍 Simple Table Test');
  console.log('=' .repeat(30));
  
  const tables = ['enhanced_lead_scores', 'user_journey_events', 'ab_tests'];
  
  for (const table of tables) {
    console.log(`\n📋 Testing: ${table}`);
    
    // Test with anon key
    try {
      const { count, error } = await supabaseAnon
        .from(table)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.log(`❌ Anon: ${error.message}`);
      } else {
        console.log(`✅ Anon: ${count} records`);
      }
    } catch (err) {
      console.log(`❌ Anon exception: ${err.message}`);
    }
    
    // Test with service key
    try {
      const { count, error } = await supabaseService
        .from(table)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.log(`❌ Service: ${error.message}`);
      } else {
        console.log(`✅ Service: ${count} records`);
      }
    } catch (err) {
      console.log(`❌ Service exception: ${err.message}`);
    }
  }
  
  // Test control table
  console.log(`\n📋 Control test: assessment_submissions`);
  try {
    const { count, error } = await supabaseAnon
      .from('assessment_submissions')
      .select('*', { count: 'exact', head: true });
    
    if (error) {
      console.log(`❌ Control: ${error.message}`);
    } else {
      console.log(`✅ Control: ${count} records`);
    }
  } catch (err) {
    console.log(`❌ Control exception: ${err.message}`);
  }
}

simpleTest().then(() => {
  console.log('\n✅ Simple test completed');
}).catch(error => {
  console.error('❌ Test failed:', error);
});
