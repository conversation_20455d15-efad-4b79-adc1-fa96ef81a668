#!/usr/bin/env node

/**
 * Populate Missing Assessment Questions Script
 * Adds questions and options for assessments that are missing them
 */

import { createClient } from '@supabase/supabase-js';

// Database credentials
const supabaseUrl = 'https://wikngnwwakatokbgvenw.supabase.co';
// NOTE: This script requires the SERVICE ROLE key to bypass RLS policies
// The anon key cannot insert into assessment_questions due to RLS restrictions
// You need to get the service_role key from Supabase Dashboard > Settings > API
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NjY0NTQsImV4cCI6MjA2NDE0MjQ1NH0.CCoK3cPN0Pe-AVUgePrxmWvf9qZho0b-prGDilfKmZ4';

if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.log('⚠️  WARNING: Using anon key instead of service role key.');
  console.log('   This will likely fail due to Row Level Security policies.');
  console.log('   To fix: Set SUPABASE_SERVICE_ROLE_KEY environment variable.');
  console.log('   Get the service_role key from: Supabase Dashboard > Settings > API');
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Assessment questions data
const assessmentQuestions = {
  'cybersecurity-maturity': [
    {
      question_text: "How often does your organization conduct cybersecurity risk assessments?",
      category: "Risk Management",
      order_index: 1,
      options: [
        { option_text: "Never or rarely (annually or less)", risk_score: 4, order_index: 1 },
        { option_text: "Occasionally (every 6-12 months)", risk_score: 3, order_index: 2 },
        { option_text: "Regularly (quarterly)", risk_score: 2, order_index: 3 },
        { option_text: "Continuously (monthly or more)", risk_score: 1, order_index: 4 }
      ]
    },
    {
      question_text: "What type of cybersecurity training do you provide to employees?",
      category: "Security Awareness",
      order_index: 2,
      options: [
        { option_text: "No formal training provided", risk_score: 4, order_index: 1 },
        { option_text: "Basic annual training only", risk_score: 3, order_index: 2 },
        { option_text: "Regular training with updates", risk_score: 2, order_index: 3 },
        { option_text: "Comprehensive ongoing training with simulations", risk_score: 1, order_index: 4 }
      ]
    },
    {
      question_text: "How does your organization manage software updates and patches?",
      category: "Patch Management",
      order_index: 3,
      options: [
        { option_text: "Manual updates when remembered", risk_score: 4, order_index: 1 },
        { option_text: "Scheduled monthly updates", risk_score: 3, order_index: 2 },
        { option_text: "Automated updates for most systems", risk_score: 2, order_index: 3 },
        { option_text: "Comprehensive automated patch management", risk_score: 1, order_index: 4 }
      ]
    },
    {
      question_text: "What backup and recovery procedures do you have in place?",
      category: "Business Continuity",
      order_index: 4,
      options: [
        { option_text: "No formal backup procedures", risk_score: 4, order_index: 1 },
        { option_text: "Basic local backups", risk_score: 3, order_index: 2 },
        { option_text: "Regular offsite backups", risk_score: 2, order_index: 3 },
        { option_text: "Comprehensive backup with tested recovery", risk_score: 1, order_index: 4 }
      ]
    },
    {
      question_text: "How do you control access to sensitive systems and data?",
      category: "Access Control",
      order_index: 5,
      options: [
        { option_text: "Basic password protection only", risk_score: 4, order_index: 1 },
        { option_text: "Role-based access with passwords", risk_score: 3, order_index: 2 },
        { option_text: "Multi-factor authentication for some systems", risk_score: 2, order_index: 3 },
        { option_text: "Comprehensive MFA and zero-trust approach", risk_score: 1, order_index: 4 }
      ]
    },
    {
      question_text: "Do you have an incident response plan for cybersecurity breaches?",
      category: "Incident Response",
      order_index: 6,
      options: [
        { option_text: "No formal incident response plan", risk_score: 4, order_index: 1 },
        { option_text: "Basic plan but not tested", risk_score: 3, order_index: 2 },
        { option_text: "Documented plan with some testing", risk_score: 2, order_index: 3 },
        { option_text: "Comprehensive tested plan with regular drills", risk_score: 1, order_index: 4 }
      ]
    },
    {
      question_text: "How do you monitor your network for security threats?",
      category: "Threat Detection",
      order_index: 7,
      options: [
        { option_text: "No active monitoring", risk_score: 4, order_index: 1 },
        { option_text: "Basic antivirus software only", risk_score: 3, order_index: 2 },
        { option_text: "Network monitoring with some alerting", risk_score: 2, order_index: 3 },
        { option_text: "Advanced threat detection and response", risk_score: 1, order_index: 4 }
      ]
    },
    {
      question_text: "What is your approach to vendor and third-party security?",
      category: "Third-Party Risk",
      order_index: 8,
      options: [
        { option_text: "No vendor security assessments", risk_score: 4, order_index: 1 },
        { option_text: "Basic vendor questionnaires", risk_score: 3, order_index: 2 },
        { option_text: "Regular vendor security reviews", risk_score: 2, order_index: 3 },
        { option_text: "Comprehensive third-party risk management", risk_score: 1, order_index: 4 }
      ]
    }
  ],
  'dmarc-compliance': [
    {
      question_text: "Have you implemented SPF (Sender Policy Framework) for your domain?",
      category: "Email Authentication",
      order_index: 1,
      options: [
        { option_text: "No SPF record configured", risk_score: 4, order_index: 1 },
        { option_text: "Basic SPF record with some gaps", risk_score: 3, order_index: 2 },
        { option_text: "Comprehensive SPF record configured", risk_score: 2, order_index: 3 },
        { option_text: "SPF with strict policy and monitoring", risk_score: 1, order_index: 4 }
      ]
    },
    {
      question_text: "Do you have DKIM (DomainKeys Identified Mail) configured?",
      category: "Email Authentication",
      order_index: 2,
      options: [
        { option_text: "No DKIM signing configured", risk_score: 4, order_index: 1 },
        { option_text: "DKIM for some email services only", risk_score: 3, order_index: 2 },
        { option_text: "DKIM configured for all outbound email", risk_score: 2, order_index: 3 },
        { option_text: "DKIM with key rotation and monitoring", risk_score: 1, order_index: 4 }
      ]
    },
    {
      question_text: "What is your current DMARC policy setting?",
      category: "DMARC Policy",
      order_index: 3,
      options: [
        { option_text: "No DMARC record published", risk_score: 4, order_index: 1 },
        { option_text: "DMARC with 'none' policy (monitoring only)", risk_score: 3, order_index: 2 },
        { option_text: "DMARC with 'quarantine' policy", risk_score: 2, order_index: 3 },
        { option_text: "DMARC with 'reject' policy", risk_score: 1, order_index: 4 }
      ]
    },
    {
      question_text: "How do you monitor DMARC reports and failures?",
      category: "Monitoring",
      order_index: 4,
      options: [
        { option_text: "No DMARC report monitoring", risk_score: 4, order_index: 1 },
        { option_text: "Occasional manual review of reports", risk_score: 3, order_index: 2 },
        { option_text: "Regular monitoring with basic analysis", risk_score: 2, order_index: 3 },
        { option_text: "Automated monitoring with alerting", risk_score: 1, order_index: 4 }
      ]
    },
    {
      question_text: "Do you have email security awareness training for employees?",
      category: "Security Awareness",
      order_index: 5,
      options: [
        { option_text: "No email security training", risk_score: 4, order_index: 1 },
        { option_text: "Basic annual training", risk_score: 3, order_index: 2 },
        { option_text: "Regular training with phishing simulations", risk_score: 2, order_index: 3 },
        { option_text: "Comprehensive ongoing training program", risk_score: 1, order_index: 4 }
      ]
    },
    {
      question_text: "How do you handle email from external domains that fail authentication?",
      category: "Email Filtering",
      order_index: 6,
      options: [
        { option_text: "No special handling for failed authentication", risk_score: 4, order_index: 1 },
        { option_text: "Basic spam filtering only", risk_score: 3, order_index: 2 },
        { option_text: "Enhanced filtering with some authentication checks", risk_score: 2, order_index: 3 },
        { option_text: "Strict authentication-based filtering", risk_score: 1, order_index: 4 }
      ]
    }
  ]
};

async function populateAssessmentQuestions() {
  console.log('🚀 Starting to populate missing assessment questions...\n');

  try {
    // Get assessment types that need questions
    const { data: assessmentTypes, error: typesError } = await supabase
      .from('assessment_types')
      .select('id, slug, title')
      .in('slug', ['cybersecurity-maturity', 'dmarc-compliance']);

    if (typesError) {
      throw new Error(`Failed to fetch assessment types: ${typesError.message}`);
    }

    console.log(`Found ${assessmentTypes.length} assessment types to populate`);

    for (const assessmentType of assessmentTypes) {
      console.log(`\n📝 Processing ${assessmentType.title} (${assessmentType.slug})...`);
      
      const questions = assessmentQuestions[assessmentType.slug];
      if (!questions) {
        console.log(`⚠️  No questions defined for ${assessmentType.slug}`);
        continue;
      }

      // Insert questions for this assessment type
      for (const questionData of questions) {
        console.log(`  Adding question: ${questionData.question_text.substring(0, 50)}...`);
        
        // Insert the question
        const { data: insertedQuestion, error: questionError } = await supabase
          .from('assessment_questions')
          .insert({
            assessment_type_id: assessmentType.id,
            question_text: questionData.question_text,
            category: questionData.category,
            order_index: questionData.order_index,
            is_active: true
          })
          .select()
          .single();

        if (questionError) {
          console.error(`    ❌ Error inserting question: ${questionError.message}`);
          continue;
        }

        console.log(`    ✅ Question inserted with ID: ${insertedQuestion.id}`);

        // Insert options for this question
        const optionsToInsert = questionData.options.map(option => ({
          question_id: insertedQuestion.id,
          option_text: option.option_text,
          risk_score: option.risk_score,
          order_index: option.order_index
        }));

        const { error: optionsError } = await supabase
          .from('assessment_question_options')
          .insert(optionsToInsert);

        if (optionsError) {
          console.error(`    ❌ Error inserting options: ${optionsError.message}`);
        } else {
          console.log(`    ✅ ${optionsToInsert.length} options inserted`);
        }
      }

      console.log(`✅ Completed ${assessmentType.title} - ${questions.length} questions added`);
    }

    console.log('\n🎉 Assessment population completed successfully!');
    
  } catch (error) {
    console.error('💥 Error populating assessments:', error.message);
    process.exit(1);
  }
}

// Run the population script
populateAssessmentQuestions();
