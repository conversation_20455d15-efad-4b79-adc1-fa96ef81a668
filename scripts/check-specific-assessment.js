#!/usr/bin/env node

/**
 * Check specific assessment data to understand the structure
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://wikngnwwakatokbgvenw.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NjY0NTQsImV4cCI6MjA2NDE0MjQ1NH0.CCoK3cPN0Pe-AVUgePrxmWvf9qZho0b-prGDilfKmZ4';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkAssessmentDetails() {
  console.log('🔍 Checking Assessment Details\n');

  try {
    // Check phishing-risk assessment (which has questions)
    console.log('📋 Checking Phishing Risk Assessment...');
    const { data: phishingType, error: phishingError } = await supabase
      .from('assessment_types')
      .select('*')
      .eq('slug', 'phishing-risk')
      .single();

    if (phishingError) {
      console.log('❌ Error:', phishingError.message);
    } else {
      console.log('✅ Assessment Type:', phishingType.title);
      console.log('   Category:', phishingType.category);
      console.log('   Description:', phishingType.description);
      console.log('   Time estimate:', phishingType.estimated_time_minutes, 'minutes');

      // Get questions for phishing assessment
      const { data: questions, error: questionsError } = await supabase
        .from('assessment_questions')
        .select('*')
        .eq('assessment_type_id', phishingType.id)
        .order('order_index');

      if (questionsError) {
        console.log('❌ Questions Error:', questionsError.message);
      } else {
        console.log(`\n📝 Found ${questions.length} questions:`);
        questions.forEach((q, index) => {
          console.log(`   ${index + 1}. ${q.question_text}`);
          console.log(`      Category: ${q.category}, Order: ${q.order_index}`);
        });

        // Get options for first question
        if (questions.length > 0) {
          const { data: options, error: optionsError } = await supabase
            .from('assessment_question_options')
            .select('*')
            .eq('question_id', questions[0].id)
            .order('order_index');

          if (optionsError) {
            console.log('❌ Options Error:', optionsError.message);
          } else {
            console.log(`\n🎯 Options for first question (${options.length} total):`);
            options.forEach((opt, index) => {
              console.log(`   ${index + 1}. ${opt.option_text} (Risk Score: ${opt.risk_score})`);
            });
          }
        }
      }
    }

    // Check cybersecurity-maturity assessment (which lacks questions)
    console.log('\n\n📋 Checking Cybersecurity Maturity Assessment...');
    const { data: maturityType, error: maturityError } = await supabase
      .from('assessment_types')
      .select('*')
      .eq('slug', 'cybersecurity-maturity')
      .single();

    if (maturityError) {
      console.log('❌ Error:', maturityError.message);
    } else {
      console.log('✅ Assessment Type:', maturityType.title);
      console.log('   Category:', maturityType.category);
      console.log('   Description:', maturityType.description);

      const { data: maturityQuestions, error: maturityQuestionsError } = await supabase
        .from('assessment_questions')
        .select('*')
        .eq('assessment_type_id', maturityType.id);

      if (maturityQuestionsError) {
        console.log('❌ Questions Error:', maturityQuestionsError.message);
      } else {
        console.log(`📝 Questions found: ${maturityQuestions.length}`);
        if (maturityQuestions.length === 0) {
          console.log('⚠️  This assessment has no questions - this is why it\'s not working!');
        }
      }
    }

    // Check what assessments are actually showing in the library
    console.log('\n\n📚 Checking Assessment Library Data...');
    const { data: allTypes, error: allTypesError } = await supabase
      .from('assessment_types')
      .select('*')
      .eq('is_active', true)
      .order('order_index');

    if (allTypesError) {
      console.log('❌ Error:', allTypesError.message);
    } else {
      console.log(`Found ${allTypes.length} active assessment types:`);
      for (const type of allTypes) {
        const { count: questionCount } = await supabase
          .from('assessment_questions')
          .select('*', { count: 'exact', head: true })
          .eq('assessment_type_id', type.id)
          .eq('is_active', true);

        const status = questionCount > 0 ? '✅' : '❌';
        console.log(`   ${status} ${type.title} (${type.slug}) - ${questionCount} questions`);
      }
    }

  } catch (error) {
    console.error('💥 Error:', error.message);
  }
}

checkAssessmentDetails();
