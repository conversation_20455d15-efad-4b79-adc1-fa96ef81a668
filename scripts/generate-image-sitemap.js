#!/usr/bin/env node

/**
 * BlackVeil Security Platform - Image Sitemap Generator
 * 
 * Generates an XML sitemap specifically for images to improve SEO
 * and help search engines discover and index images.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const CONFIG = {
  baseUrl: 'https://blackveil.co.nz',
  outputPath: './public/image-sitemap.xml',
  imageExtensions: ['.jpg', '.jpeg', '.png', '.webp', '.avif', '.svg'],
  excludePatterns: [
    /backup-/,
    /placeholder/,
    /test-/,
    /temp-/
  ]
};

// Image metadata for better SEO
const IMAGE_METADATA = {
  // Blog images
  'cf258467-204f-4404-9f34-4cbb861db674.webp': {
    title: 'DMARC Email Security Dashboard',
    caption: 'Email security dashboard showing DMARC implementation progress and threat detection analytics',
    license: 'https://blackveil.co.nz/terms'
  },
  '4deb7e82-aa37-4ab5-94f4-876c1d8787d4.png': {
    title: 'AI Voice Synthesis Technology',
    caption: 'AI-powered voice synthesis technology visualization showing waveforms and neural networks',
    license: 'https://blackveil.co.nz/terms'
  },
  '7982392e-5308-407f-a054-9dcc9a21824f.png': {
    title: 'Digital Identity Verification',
    caption: 'Digital identity verification concept showing synthetic identity creation and detection systems',
    license: 'https://blackveil.co.nz/terms'
  },
  
  // Service images
  'fba527f9-0c6d-4116-8361-695ae2687b1c.webp': {
    title: 'Cybersecurity Service Illustration',
    caption: 'BlackVeil Security cybersecurity service illustration for comprehensive protection',
    license: 'https://blackveil.co.nz/terms'
  },
  
  // Mobile hero
  'mobile-hero-image.webp': {
    title: 'BlackVeil Security Mobile Hero',
    caption: 'BlackVeil Security platform mobile hero image showcasing cybersecurity services',
    license: 'https://blackveil.co.nz/terms'
  }
};

/**
 * Scan for images in the public directory
 */
function scanForImages(dir = './public', baseDir = './public') {
  const images = [];
  
  try {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory() && !file.startsWith('.')) {
        // Recursively scan subdirectories
        images.push(...scanForImages(filePath, baseDir));
      } else if (CONFIG.imageExtensions.some(ext => file.toLowerCase().endsWith(ext))) {
        // Check if image should be excluded
        const shouldExclude = CONFIG.excludePatterns.some(pattern => pattern.test(file));
        
        if (!shouldExclude) {
          const relativePath = path.relative(baseDir, filePath);
          const urlPath = relativePath.replace(/\\/g, '/'); // Convert Windows paths
          
          images.push({
            filename: file,
            path: filePath,
            url: `${CONFIG.baseUrl}/${urlPath}`,
            size: stat.size,
            lastModified: stat.mtime.toISOString(),
            metadata: IMAGE_METADATA[file] || {}
          });
        }
      }
    }
  } catch (error) {
    console.error(`Error scanning directory ${dir}:`, error.message);
  }
  
  return images;
}

/**
 * Generate XML sitemap for images
 */
function generateImageSitemap(images) {
  const xmlHeader = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">`;

  const xmlFooter = `</urlset>`;

  // Group images by page/context
  const imageGroups = {
    homepage: images.filter(img => img.filename.includes('hero') || img.filename.includes('mobile')),
    blog: images.filter(img => 
      img.filename.includes('cf258467') || 
      img.filename.includes('4deb7e82') || 
      img.filename.includes('7982392e')
    ),
    services: images.filter(img => 
      img.filename.includes('fba527f9') || 
      img.filename.includes('service')
    ),
    general: images.filter(img => 
      !img.filename.includes('hero') && 
      !img.filename.includes('mobile') &&
      !img.filename.includes('cf258467') &&
      !img.filename.includes('4deb7e82') &&
      !img.filename.includes('7982392e') &&
      !img.filename.includes('fba527f9') &&
      !img.filename.includes('service')
    )
  };

  let xmlContent = '';

  // Homepage images
  if (imageGroups.homepage.length > 0) {
    xmlContent += `
  <url>
    <loc>${CONFIG.baseUrl}/</loc>
    <lastmod>${new Date().toISOString()}</lastmod>`;
    
    for (const image of imageGroups.homepage) {
      xmlContent += `
    <image:image>
      <image:loc>${image.url}</image:loc>
      <image:title>${image.metadata.title || 'BlackVeil Security - Cybersecurity Services'}</image:title>
      <image:caption>${image.metadata.caption || 'BlackVeil Security cybersecurity services for New Zealand businesses'}</image:caption>
      ${image.metadata.license ? `<image:license>${image.metadata.license}</image:license>` : ''}
    </image:image>`;
    }
    
    xmlContent += `
  </url>`;
  }

  // Blog images
  if (imageGroups.blog.length > 0) {
    xmlContent += `
  <url>
    <loc>${CONFIG.baseUrl}/blog</loc>
    <lastmod>${new Date().toISOString()}</lastmod>`;
    
    for (const image of imageGroups.blog) {
      xmlContent += `
    <image:image>
      <image:loc>${image.url}</image:loc>
      <image:title>${image.metadata.title || 'BlackVeil Security Blog'}</image:title>
      <image:caption>${image.metadata.caption || 'BlackVeil Security blog post illustration'}</image:caption>
      ${image.metadata.license ? `<image:license>${image.metadata.license}</image:license>` : ''}
    </image:image>`;
    }
    
    xmlContent += `
  </url>`;
  }

  // Services images
  if (imageGroups.services.length > 0) {
    xmlContent += `
  <url>
    <loc>${CONFIG.baseUrl}/services</loc>
    <lastmod>${new Date().toISOString()}</lastmod>`;
    
    for (const image of imageGroups.services) {
      xmlContent += `
    <image:image>
      <image:loc>${image.url}</image:loc>
      <image:title>${image.metadata.title || 'BlackVeil Security Services'}</image:title>
      <image:caption>${image.metadata.caption || 'BlackVeil Security cybersecurity service illustration'}</image:caption>
      ${image.metadata.license ? `<image:license>${image.metadata.license}</image:license>` : ''}
    </image:image>`;
    }
    
    xmlContent += `
  </url>`;
  }

  // General images (grouped under main site)
  if (imageGroups.general.length > 0) {
    xmlContent += `
  <url>
    <loc>${CONFIG.baseUrl}/</loc>
    <lastmod>${new Date().toISOString()}</lastmod>`;
    
    for (const image of imageGroups.general) {
      xmlContent += `
    <image:image>
      <image:loc>${image.url}</image:loc>
      <image:title>${image.metadata.title || 'BlackVeil Security'}</image:title>
      <image:caption>${image.metadata.caption || 'BlackVeil Security cybersecurity illustration'}</image:caption>
      ${image.metadata.license ? `<image:license>${image.metadata.license}</image:license>` : ''}
    </image:image>`;
    }
    
    xmlContent += `
  </url>`;
  }

  return xmlHeader + xmlContent + xmlFooter;
}

/**
 * Main function to generate image sitemap
 */
function generateSitemap() {
  console.log('🗺️  Generating image sitemap for BlackVeil Security Platform...\n');
  
  // Scan for images
  console.log('📁 Scanning for images...');
  const images = scanForImages();
  console.log(`Found ${images.length} images\n`);
  
  if (images.length === 0) {
    console.log('⚠️  No images found to include in sitemap');
    return;
  }
  
  // Generate sitemap XML
  console.log('🔧 Generating XML sitemap...');
  const sitemapXml = generateImageSitemap(images);
  
  // Write sitemap to file
  fs.writeFileSync(CONFIG.outputPath, sitemapXml, 'utf8');
  console.log(`✅ Image sitemap generated: ${CONFIG.outputPath}\n`);
  
  // Summary
  console.log('📊 SITEMAP SUMMARY:');
  console.log('===================');
  console.log(`Total images: ${images.length}`);
  console.log(`Sitemap URL: ${CONFIG.baseUrl}/image-sitemap.xml`);
  console.log(`File size: ${(sitemapXml.length / 1024).toFixed(1)}KB`);
  
  // List included images
  console.log('\n📸 INCLUDED IMAGES:');
  images.forEach(image => {
    const metadata = image.metadata.title ? ` (${image.metadata.title})` : '';
    console.log(`- ${image.filename}${metadata}`);
  });
  
  console.log('\n💡 NEXT STEPS:');
  console.log('- Submit sitemap to Google Search Console');
  console.log('- Add sitemap reference to robots.txt');
  console.log('- Monitor image indexing in search results');
  console.log('- Update sitemap when adding new images');
  
  return {
    imageCount: images.length,
    sitemapPath: CONFIG.outputPath,
    sitemapUrl: `${CONFIG.baseUrl}/image-sitemap.xml`
  };
}

/**
 * Update robots.txt to include image sitemap
 */
function updateRobotsTxt() {
  const robotsPath = './public/robots.txt';
  const sitemapUrl = `${CONFIG.baseUrl}/image-sitemap.xml`;
  
  try {
    let robotsContent = '';
    
    if (fs.existsSync(robotsPath)) {
      robotsContent = fs.readFileSync(robotsPath, 'utf8');
    } else {
      robotsContent = `User-agent: *
Allow: /

`;
    }
    
    // Check if image sitemap is already referenced
    if (!robotsContent.includes('image-sitemap.xml')) {
      robotsContent += `Sitemap: ${sitemapUrl}\n`;
      fs.writeFileSync(robotsPath, robotsContent, 'utf8');
      console.log('✅ Updated robots.txt with image sitemap reference');
    } else {
      console.log('ℹ️  Image sitemap already referenced in robots.txt');
    }
  } catch (error) {
    console.error('❌ Failed to update robots.txt:', error.message);
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  const result = generateSitemap();
  if (result) {
    updateRobotsTxt();
  }
}

export { generateSitemap, scanForImages };
