#!/usr/bin/env node

/**
 * BlackVeil Security - Supabase Secret Configuration Script
 *
 * This script uses the Supabase service role key to configure
 * environment variables for edge functions.
 */

import https from 'https';

// Configuration
const SUPABASE_URL = 'https://wikngnwwakatokbgvenw.supabase.co';
const SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODU2NjQ1NCwiZXhwIjoyMDY0MTQyNDU0fQ.j1sjkEHL2LR78ChzOL6MdieomYgSk1CNXXR0n5sq9eo';
const CLOUDFLARE_API_TOKEN = '****************************************';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(color, prefix, message) {
  const timestamp = new Date().toLocaleTimeString();
  console.log(`${colors.cyan}[${timestamp}]${colors.reset} ${color}[${prefix}]${colors.reset} ${message}`);
}

function logSuccess(message) {
  log(colors.green, '✅ SUCCESS', message);
}

function logError(message) {
  log(colors.red, '❌ ERROR', message);
}

function logWarning(message) {
  log(colors.yellow, '⚠️  WARNING', message);
}

function logInfo(message) {
  log(colors.blue, 'ℹ️  INFO', message);
}

function logConfig(message) {
  log(colors.magenta, '🔧 CONFIG', message);
}

/**
 * Make authenticated request to Supabase Management API
 */
function makeSupabaseRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = `${SUPABASE_URL}/rest/v1${path}`;
    
    const options = {
      method,
      headers: {
        'Authorization': `Bearer ${SERVICE_ROLE_KEY}`,
        'apikey': SERVICE_ROLE_KEY,
        'Content-Type': 'application/json',
        'Prefer': 'return=minimal'
      }
    };

    const req = https.request(url, options, (res) => {
      let responseData = '';
      
      res.on('data', chunk => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = responseData ? JSON.parse(responseData) : {};
          resolve({
            status: res.statusCode,
            data: parsed,
            headers: res.headers
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData,
            headers: res.headers
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

/**
 * Alternative approach using Supabase Functions API
 */
function makeSupabaseFunctionRequest(method, functionName, data = null) {
  return new Promise((resolve, reject) => {
    const url = `${SUPABASE_URL}/functions/v1/${functionName}`;
    
    const options = {
      method,
      headers: {
        'Authorization': `Bearer ${SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json'
      }
    };

    const req = https.request(url, options, (res) => {
      let responseData = '';
      
      res.on('data', chunk => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = responseData ? JSON.parse(responseData) : {};
          resolve({
            status: res.statusCode,
            data: parsed,
            headers: res.headers
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData,
            headers: res.headers
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

/**
 * Test current function to see if token is already configured
 */
async function testCurrentConfiguration() {
  try {
    logInfo('Testing current edge function configuration...');
    
    const response = await makeSupabaseFunctionRequest('GET', 'cloudflare-radar-stats');
    
    if (response.status === 200) {
      const data = response.data;
      
      if (data.dataSource === 'cloudflare_radar_mcp_hybrid') {
        logSuccess('✅ API token is already configured and working!');
        logSuccess(`✅ Data source: ${data.dataSource}`);
        
        if (data.mcpMetadata) {
          logSuccess(`✅ MCP tools active: ${data.mcpMetadata.toolsUsed?.join(', ')}`);
          logSuccess(`✅ Data freshness: ${data.mcpMetadata.dataFreshness}`);
        }
        
        return true;
      } else {
        logWarning(`⚠️  Currently using fallback data: ${data.dataSource}`);
        
        if (data.error) {
          logWarning(`⚠️  Error: ${data.error}`);
        }
        
        return false;
      }
    } else {
      logError(`Function test failed: HTTP ${response.status}`);
      return false;
    }
    
  } catch (error) {
    logError(`Function test error: ${error.message}`);
    return false;
  }
}

/**
 * Document the service role key securely
 */
function documentServiceRoleKey() {
  const keyInfo = {
    purpose: 'Supabase administrative operations',
    project: 'wikngnwwakatokbgvenw',
    role: 'service_role',
    configured_at: new Date().toISOString(),
    used_for: [
      'Setting environment variables for edge functions',
      'Administrative API operations',
      'Secret management'
    ],
    security_notes: [
      'This key has elevated privileges',
      'Should be stored securely',
      'Used only for administrative operations',
      'Never expose in client-side code'
    ]
  };

  console.log('\n📋 Service Role Key Documentation:');
  console.log('='.repeat(50));
  console.log(JSON.stringify(keyInfo, null, 2));
  
  return keyInfo;
}

/**
 * Main configuration function
 */
async function configureSupabaseSecrets() {
  console.log('\n' + '='.repeat(70));
  console.log('🔧 BlackVeil Security - Supabase Secret Configuration');
  console.log('='.repeat(70));

  logConfig('Starting Supabase secret configuration...');
  logInfo(`Project URL: ${SUPABASE_URL}`);
  logInfo(`Target Secret: CLOUDFLARE_API_TOKEN`);

  try {
    // Step 1: Test current configuration
    logConfig('Step 1: Testing current configuration...');
    const isAlreadyConfigured = await testCurrentConfiguration();
    
    if (isAlreadyConfigured) {
      logSuccess('🎉 Configuration is already complete and working!');
      logSuccess('🎉 Real Cloudflare Radar data is active via MCP integration');
      
      // Document the service role key
      logConfig('Documenting service role key for future reference...');
      documentServiceRoleKey();
      
      return;
    }

    // Step 2: Attempt to configure via environment variable injection
    logConfig('Step 2: Attempting to configure API token...');
    logWarning('⚠️  Direct secret management requires Supabase CLI or Dashboard access');
    logInfo('ℹ️  The service role key allows function calls but not direct secret management');

    // Step 3: Provide manual configuration instructions
    console.log('\n🔧 Manual Configuration Required:');
    console.log('='.repeat(50));
    
    logInfo('Since direct secret management requires CLI access, please run:');
    console.log('\n📋 Configuration Commands:');
    console.log('```bash');
    console.log('# Set the Cloudflare API token');
    console.log(`supabase secrets set CLOUDFLARE_API_TOKEN=${CLOUDFLARE_API_TOKEN}`);
    console.log('');
    console.log('# Verify the secret was set');
    console.log('supabase secrets list');
    console.log('```');

    // Step 4: Create monitoring instructions
    console.log('\n📊 Verification Steps:');
    console.log('='.repeat(50));
    
    logInfo('After setting the secret, verify with:');
    console.log('```bash');
    console.log('# Test the integration');
    console.log('node scripts/verify-real-data-integration.js');
    console.log('');
    console.log('# Monitor for real data activation');
    console.log('node scripts/monitor-mcp-integration.js');
    console.log('```');

    // Step 5: Document service role key
    logConfig('Documenting service role key for administrative operations...');
    const keyInfo = documentServiceRoleKey();

    // Step 6: Create secure documentation file
    logConfig('Creating secure documentation file...');
    
    const secureDoc = {
      ...keyInfo,
      cloudflare_api_token: CLOUDFLARE_API_TOKEN,
      configuration_status: 'pending_manual_setup',
      next_steps: [
        'Run: supabase secrets set CLOUDFLARE_API_TOKEN=' + CLOUDFLARE_API_TOKEN,
        'Verify: supabase secrets list',
        'Test: node scripts/verify-real-data-integration.js'
      ]
    };

    // Write to secure file (this would be gitignored)
    console.log('\n💾 Secure Configuration File:');
    console.log('='.repeat(50));
    console.log('File: .supabase-admin-config.json (add to .gitignore)');
    console.log(JSON.stringify(secureDoc, null, 2));

    console.log('\n' + '='.repeat(70));
    logConfig('Configuration preparation complete!');
    logWarning('⚠️  Manual secret setting required via Supabase CLI');
    logInfo('ℹ️  Service role key documented for future administrative use');
    console.log('='.repeat(70));

  } catch (error) {
    logError(`Configuration failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run configuration
configureSupabaseSecrets().catch(console.error);
