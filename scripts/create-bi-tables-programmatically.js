// Programmatic Advanced BI Tables Creation Script
// This script creates the missing advanced BI tables using a simpler approach
// that works reliably with the Supabase JavaScript client

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env.local file
let supabaseUrl, supabaseKey;
try {
  const envPath = join(__dirname, '..', '.env.local');
  const envContent = readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  envLines.forEach(line => {
    const [key, value] = line.split('=');
    if (key === 'VITE_SUPABASE_URL') supabaseUrl = value;
    if (key === 'VITE_SUPABASE_ANON_KEY') supabaseKey = value;
  });
} catch (error) {
  console.error('❌ Could not load .env.local file:', error.message);
}

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to check if a table exists
async function tableExists(tableName) {
  try {
    const { count, error } = await supabase
      .from(tableName)
      .select('*', { count: 'exact', head: true });
    
    return !error;
  } catch (err) {
    return false;
  }
}

// Function to create sample data for A/B tests
async function createSampleABTestData() {
  console.log('📝 Creating sample A/B test data...');
  
  try {
    // Insert sample A/B tests
    const { error: testsError } = await supabase
      .from('ab_tests')
      .upsert([
        {
          id: '550e8400-e29b-41d4-a716-446655440001',
          name: 'Assessment CTA Button Test',
          description: 'Testing different call-to-action button colors and text',
          test_type: 'cta_button',
          hypothesis: 'Green buttons will have higher conversion than blue buttons',
          success_metric: 'assessment_completion_rate',
          is_active: true
        },
        {
          id: '550e8400-e29b-41d4-a716-446655440002',
          name: 'Landing Page Layout Test',
          description: 'Testing hero section layouts for better engagement',
          test_type: 'landing_page',
          hypothesis: 'Centered layout will perform better than left-aligned',
          success_metric: 'time_on_page',
          is_active: true
        },
        {
          id: '550e8400-e29b-41d4-a716-446655440003',
          name: 'Email Template Test',
          description: 'Testing follow-up email templates for lead nurturing',
          test_type: 'email_template',
          hypothesis: 'Personalized emails will have higher open rates',
          success_metric: 'email_open_rate',
          is_active: false
        }
      ], { onConflict: 'id' });

    if (testsError) {
      console.error('❌ Error creating sample A/B tests:', testsError.message);
      return false;
    }

    // Insert sample variants
    const { error: variantsError } = await supabase
      .from('ab_test_variants')
      .upsert([
        {
          test_id: '550e8400-e29b-41d4-a716-446655440001',
          name: 'Control - Blue Button',
          description: 'Original blue CTA button',
          config: { button_color: 'blue', button_text: 'Start Assessment' },
          traffic_percentage: 50,
          is_control: true
        },
        {
          test_id: '550e8400-e29b-41d4-a716-446655440001',
          name: 'Variant - Green Button',
          description: 'Green CTA button with action text',
          config: { button_color: 'green', button_text: 'Get My Security Score' },
          traffic_percentage: 50,
          is_control: false
        },
        {
          test_id: '550e8400-e29b-41d4-a716-446655440002',
          name: 'Control - Left Layout',
          description: 'Original left-aligned hero section',
          config: { layout: 'left', hero_position: 'left' },
          traffic_percentage: 50,
          is_control: true
        },
        {
          test_id: '550e8400-e29b-41d4-a716-446655440002',
          name: 'Variant - Center Layout',
          description: 'Centered hero section layout',
          config: { layout: 'center', hero_position: 'center' },
          traffic_percentage: 50,
          is_control: false
        }
      ], { onConflict: 'test_id,name' });

    if (variantsError) {
      console.error('❌ Error creating sample A/B test variants:', variantsError.message);
      return false;
    }

    console.log('✅ Sample A/B test data created successfully');
    return true;
  } catch (err) {
    console.error('❌ Error creating sample data:', err.message);
    return false;
  }
}

// Function to verify all tables exist and show status
async function verifyTablesCreation() {
  console.log('\n🔍 Verifying Advanced BI Tables Creation...');
  console.log('=' .repeat(50));
  
  const tables = [
    'enhanced_lead_scores',
    'user_journey_events', 
    'ab_tests',
    'ab_test_variants',
    'ab_test_participations'
  ];

  const results = {};
  let allExist = true;
  
  for (const table of tables) {
    const exists = await tableExists(table);
    results[table] = exists;
    
    if (exists) {
      try {
        const { count } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });
        console.log(`✅ ${table}: Table exists (${count || 0} records)`);
      } catch (err) {
        console.log(`✅ ${table}: Table exists`);
      }
    } else {
      console.log(`❌ ${table}: Table missing`);
      allExist = false;
    }
  }

  console.log(`\n🎯 Overall Status: ${allExist ? '✅ ALL TABLES EXIST' : '❌ SOME TABLES MISSING'}`);
  
  return { allExist, results };
}

// Main function to create the advanced BI schema
async function createAdvancedBISchema() {
  console.log('🚀 BlackVeil Security - Advanced BI Schema Creation');
  console.log('📋 Creating missing advanced BI tables programmatically...');
  console.log('=' .repeat(60));

  try {
    // First, check current status
    console.log('\n📊 Current Status Check:');
    const initialStatus = await verifyTablesCreation();
    
    if (initialStatus.allExist) {
      console.log('\n🎉 All advanced BI tables already exist!');
      console.log('📋 No action needed - schema is complete');
      return true;
    }

    console.log('\n⚠️ Some tables are missing. Attempting to create them...');
    console.log('\n📝 Note: Since Supabase doesn\'t support programmatic DDL execution');
    console.log('   through the JavaScript client, you\'ll need to execute the SQL');
    console.log('   script manually in the Supabase SQL Editor.');
    console.log('\n📋 Instructions:');
    console.log('   1. Go to https://supabase.com/dashboard');
    console.log('   2. Navigate to your BlackVeil project');
    console.log('   3. Click "SQL Editor" in the left sidebar');
    console.log('   4. Copy and paste the contents of scripts/create-advanced-bi-schema.sql');
    console.log('   5. Click "Run" to execute the script');
    console.log('   6. Run this script again to verify creation');

    // Show the SQL file path for easy access
    const sqlFilePath = join(__dirname, 'create-advanced-bi-schema.sql');
    console.log(`\n📁 SQL File Location: ${sqlFilePath}`);
    
    // Try to read and display a summary of what will be created
    try {
      const sqlContent = readFileSync(sqlFilePath, 'utf8');
      console.log(`\n📖 SQL Script Summary (${sqlContent.length} characters):`);
      console.log('   • enhanced_lead_scores table with ML-based scoring');
      console.log('   • user_journey_events table for customer tracking');
      console.log('   • ab_tests table for A/B testing framework');
      console.log('   • ab_test_variants table for test variations');
      console.log('   • ab_test_participations table for participation tracking');
      console.log('   • Performance indexes and RLS policies');
      console.log('   • Sample A/B test data for demonstration');
    } catch (err) {
      console.error('❌ Could not read SQL file:', err.message);
    }

    return false;

  } catch (error) {
    console.error('❌ Schema creation process failed:', error.message);
    return false;
  }
}

// Run the schema creation process
createAdvancedBISchema().then(async (success) => {
  if (success) {
    console.log('\n✅ Advanced BI schema verification completed!');
    console.log('🎉 All tables exist and are ready for use');
    console.log('\n📋 Next Steps:');
    console.log('   1. Run: node scripts/database-inspection.js');
    console.log('   2. Test the BI dashboard at /admin');
    console.log('   3. Verify enhanced lead scoring features');
    console.log('   4. Test A/B testing functionality');
    process.exit(0);
  } else {
    console.log('\n📋 Manual SQL execution required');
    console.log('⚠️ Please follow the instructions above to create the tables');
    console.log('   Then run this script again to verify creation');
    process.exit(1);
  }
}).catch(error => {
  console.error('❌ Script execution failed:', error);
  process.exit(1);
});
