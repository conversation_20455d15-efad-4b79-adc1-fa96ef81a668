// Fix RLS Policies for Advanced BI Tables
// This script uses the service role to create proper RLS policies

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env.local file
let supabaseUrl;
try {
  const envPath = join(__dirname, '..', '.env.local');
  const envContent = readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  envLines.forEach(line => {
    const [key, value] = line.split('=');
    if (key === 'VITE_SUPABASE_URL') supabaseUrl = value;
  });
} catch (error) {
  console.error('❌ Could not load .env.local file:', error.message);
}

if (!supabaseUrl) {
  console.error('❌ Missing Supabase URL');
  process.exit(1);
}

// Service role key (bypasses RLS)
const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODU2NjQ1NCwiZXhwIjoyMDY0MTQyNDU0fQ.j1sjkEHL2LR78ChzOL6MdieomYgSk1CNXXR0n5sq9eo';

// Anon key for testing
const anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NjY0NTQsImV4cCI6MjA2NDE0MjQ1NH0.CCoK3cPN0Pe-AVUgePrxmWvf9qZho0b-prGDilfKmZ4';

// Create Supabase clients
const supabaseService = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

const supabaseAnon = createClient(supabaseUrl, anonKey);

// RLS policies to create/update
const rlsPolicies = [
  {
    table: 'enhanced_lead_scores',
    policies: [
      {
        name: 'Allow anon read access to enhanced_lead_scores',
        action: 'SELECT',
        role: 'anon',
        using: 'true'
      },
      {
        name: 'Allow authenticated full access to enhanced_lead_scores',
        action: 'ALL',
        role: 'authenticated',
        using: 'true'
      }
    ]
  },
  {
    table: 'user_journey_events',
    policies: [
      {
        name: 'Allow anon read access to user_journey_events',
        action: 'SELECT',
        role: 'anon',
        using: 'true'
      },
      {
        name: 'Allow anon insert to user_journey_events',
        action: 'INSERT',
        role: 'anon',
        check: 'true'
      },
      {
        name: 'Allow authenticated full access to user_journey_events',
        action: 'ALL',
        role: 'authenticated',
        using: 'true'
      }
    ]
  },
  {
    table: 'ab_tests',
    policies: [
      {
        name: 'Allow anon read access to ab_tests',
        action: 'SELECT',
        role: 'anon',
        using: 'true'
      },
      {
        name: 'Allow authenticated full access to ab_tests',
        action: 'ALL',
        role: 'authenticated',
        using: 'true'
      }
    ]
  },
  {
    table: 'ab_test_variants',
    policies: [
      {
        name: 'Allow anon read access to ab_test_variants',
        action: 'SELECT',
        role: 'anon',
        using: 'true'
      },
      {
        name: 'Allow authenticated full access to ab_test_variants',
        action: 'ALL',
        role: 'authenticated',
        using: 'true'
      }
    ]
  },
  {
    table: 'ab_test_participations',
    policies: [
      {
        name: 'Allow anon read access to ab_test_participations',
        action: 'SELECT',
        role: 'anon',
        using: 'true'
      },
      {
        name: 'Allow anon insert to ab_test_participations',
        action: 'INSERT',
        role: 'anon',
        check: 'true'
      },
      {
        name: 'Allow authenticated full access to ab_test_participations',
        action: 'ALL',
        role: 'authenticated',
        using: 'true'
      }
    ]
  }
];

// Function to execute SQL with service role
async function executeServiceSQL(sql) {
  try {
    console.log(`🔄 Executing: ${sql.substring(0, 100)}...`);
    
    // Try different methods to execute SQL
    const methods = [
      () => supabaseService.rpc('exec_sql', { sql_query: sql }),
      () => supabaseService.rpc('execute_sql', { query: sql }),
      () => supabaseService.rpc('run_sql', { sql: sql })
    ];

    for (const method of methods) {
      try {
        const { data, error } = await method();
        if (!error) {
          console.log('✅ SQL executed successfully');
          return { success: true, data };
        }
      } catch (methodError) {
        // Try next method
        continue;
      }
    }

    // If all methods fail, return error
    console.log('❌ All SQL execution methods failed');
    return { success: false, error: 'No available SQL execution method' };

  } catch (err) {
    console.error('❌ SQL execution error:', err.message);
    return { success: false, error: err.message };
  }
}

// Function to drop existing policies
async function dropExistingPolicies(tableName) {
  console.log(`🧹 Dropping existing policies for ${tableName}...`);
  
  const dropPolicies = [
    `DROP POLICY IF EXISTS "Authenticated users can access ${tableName}" ON ${tableName};`,
    `DROP POLICY IF EXISTS "Allow anon read access to ${tableName}" ON ${tableName};`,
    `DROP POLICY IF EXISTS "Allow anon insert to ${tableName}" ON ${tableName};`,
    `DROP POLICY IF EXISTS "Allow authenticated full access to ${tableName}" ON ${tableName};`
  ];

  for (const dropSQL of dropPolicies) {
    await executeServiceSQL(dropSQL);
  }
}

// Function to create RLS policies
async function createRLSPolicies() {
  console.log('🔐 Creating RLS policies for advanced BI tables...');
  
  let successCount = 0;
  let errorCount = 0;

  for (const tableConfig of rlsPolicies) {
    console.log(`\n📋 Processing table: ${tableConfig.table}`);
    
    // First, ensure RLS is enabled
    const enableRLSSQL = `ALTER TABLE ${tableConfig.table} ENABLE ROW LEVEL SECURITY;`;
    await executeServiceSQL(enableRLSSQL);
    
    // Drop existing policies to avoid conflicts
    await dropExistingPolicies(tableConfig.table);
    
    // Create new policies
    for (const policy of tableConfig.policies) {
      console.log(`   🔒 Creating policy: ${policy.name}`);
      
      let policySQL;
      if (policy.action === 'ALL') {
        policySQL = `
          CREATE POLICY "${policy.name}" ON ${tableConfig.table}
          FOR ALL TO ${policy.role}
          USING (${policy.using || 'true'});
        `;
      } else if (policy.action === 'SELECT') {
        policySQL = `
          CREATE POLICY "${policy.name}" ON ${tableConfig.table}
          FOR SELECT TO ${policy.role}
          USING (${policy.using || 'true'});
        `;
      } else if (policy.action === 'INSERT') {
        policySQL = `
          CREATE POLICY "${policy.name}" ON ${tableConfig.table}
          FOR INSERT TO ${policy.role}
          WITH CHECK (${policy.check || 'true'});
        `;
      }
      
      const result = await executeServiceSQL(policySQL);
      
      if (result.success) {
        console.log(`   ✅ Policy created: ${policy.name}`);
        successCount++;
      } else {
        console.log(`   ❌ Policy failed: ${policy.name} - ${result.error}`);
        errorCount++;
      }
    }
  }

  return { successCount, errorCount };
}

// Function to test access with anon key
async function testAnonAccess() {
  console.log('\n🧪 Testing anon key access to advanced BI tables...');
  
  const tables = [
    'enhanced_lead_scores',
    'user_journey_events', 
    'ab_tests',
    'ab_test_variants',
    'ab_test_participations'
  ];

  const results = {};
  let allAccessible = true;
  
  for (const table of tables) {
    try {
      const { count, error } = await supabaseAnon
        .from(table)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.log(`❌ ${table}: ${error.message}`);
        results[table] = false;
        allAccessible = false;
      } else {
        console.log(`✅ ${table}: Accessible (${count || 0} records)`);
        results[table] = true;
      }
    } catch (err) {
      console.log(`❌ ${table}: ${err.message}`);
      results[table] = false;
      allAccessible = false;
    }
  }

  return { allAccessible, results };
}

// Function to populate sample A/B test data
async function populateSampleData() {
  console.log('\n📝 Populating sample A/B test data...');
  
  try {
    // Insert sample A/B tests
    const { error: testsError } = await supabaseService
      .from('ab_tests')
      .upsert([
        {
          id: '550e8400-e29b-41d4-a716-446655440001',
          name: 'Assessment CTA Button Test',
          description: 'Testing different call-to-action button colors and text',
          test_type: 'cta_button',
          hypothesis: 'Green buttons will have higher conversion than blue buttons',
          success_metric: 'assessment_completion_rate',
          is_active: true
        },
        {
          id: '550e8400-e29b-41d4-a716-446655440002',
          name: 'Landing Page Layout Test',
          description: 'Testing hero section layouts for better engagement',
          test_type: 'landing_page',
          hypothesis: 'Centered layout will perform better than left-aligned',
          success_metric: 'time_on_page',
          is_active: true
        },
        {
          id: '550e8400-e29b-41d4-a716-446655440003',
          name: 'Email Template Test',
          description: 'Testing follow-up email templates for lead nurturing',
          test_type: 'email_template',
          hypothesis: 'Personalized emails will have higher open rates',
          success_metric: 'email_open_rate',
          is_active: false
        }
      ], { onConflict: 'id' });

    if (testsError) {
      console.error('❌ Error creating sample A/B tests:', testsError.message);
      return false;
    }

    // Insert sample variants
    const { error: variantsError } = await supabaseService
      .from('ab_test_variants')
      .upsert([
        {
          test_id: '550e8400-e29b-41d4-a716-446655440001',
          name: 'Control - Blue Button',
          description: 'Original blue CTA button',
          config: { button_color: 'blue', button_text: 'Start Assessment' },
          traffic_percentage: 50,
          is_control: true
        },
        {
          test_id: '550e8400-e29b-41d4-a716-446655440001',
          name: 'Variant - Green Button',
          description: 'Green CTA button with action text',
          config: { button_color: 'green', button_text: 'Get My Security Score' },
          traffic_percentage: 50,
          is_control: false
        },
        {
          test_id: '550e8400-e29b-41d4-a716-446655440002',
          name: 'Control - Left Layout',
          description: 'Original left-aligned hero section',
          config: { layout: 'left', hero_position: 'left' },
          traffic_percentage: 50,
          is_control: true
        },
        {
          test_id: '550e8400-e29b-41d4-a716-446655440002',
          name: 'Variant - Center Layout',
          description: 'Centered hero section layout',
          config: { layout: 'center', hero_position: 'center' },
          traffic_percentage: 50,
          is_control: false
        }
      ], { onConflict: 'test_id,name' });

    if (variantsError) {
      console.error('❌ Error creating sample A/B test variants:', variantsError.message);
      return false;
    }

    console.log('✅ Sample A/B test data created successfully');
    return true;
  } catch (err) {
    console.error('❌ Error creating sample data:', err.message);
    return false;
  }
}

// Main execution function
async function fixRLSPolicies() {
  console.log('🚀 BlackVeil Security - RLS Policy Fix');
  console.log('🔐 Fixing Row Level Security policies for advanced BI tables');
  console.log('=' .repeat(60));

  try {
    // Create RLS policies
    const policyResult = await createRLSPolicies();
    console.log(`\n📊 Policy Creation Summary:`);
    console.log(`   ✅ Successful: ${policyResult.successCount}`);
    console.log(`   ❌ Failed: ${policyResult.errorCount}`);

    // Populate sample data
    await populateSampleData();

    // Test anon access
    const accessResult = await testAnonAccess();
    
    if (accessResult.allAccessible) {
      console.log('\n🎉 All advanced BI tables are now accessible!');
      console.log('✅ RLS policies fixed successfully');
      return true;
    } else {
      console.log('\n⚠️ Some tables may still have access issues');
      console.log('📋 Check the errors above for details');
      return false;
    }

  } catch (error) {
    console.error('❌ RLS policy fix failed:', error.message);
    return false;
  }
}

// Run the RLS fix
fixRLSPolicies().then((success) => {
  if (success) {
    console.log('\n✅ RLS policy fix completed successfully!');
    console.log('📊 Advanced BI tables are now accessible');
    console.log('\n📋 Next Steps:');
    console.log('   1. Run: node scripts/database-inspection.js');
    console.log('   2. Test the BI dashboard at /admin');
    console.log('   3. Verify all advanced features work');
    process.exit(0);
  } else {
    console.log('\n❌ RLS policy fix incomplete');
    console.log('📋 Some manual intervention may be required');
    process.exit(1);
  }
}).catch(error => {
  console.error('❌ Script execution failed:', error);
  process.exit(1);
});
