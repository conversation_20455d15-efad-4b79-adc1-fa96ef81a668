// Automated Advanced BI Schema Creation Script
// This script programmatically executes the create-advanced-bi-schema.sql file
// to create missing advanced BI tables in the BlackVeil Security database

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env.local file
let supabaseUrl, supabaseKey;
try {
  const envPath = join(__dirname, '..', '.env.local');
  const envContent = readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  envLines.forEach(line => {
    const [key, value] = line.split('=');
    if (key === 'VITE_SUPABASE_URL') supabaseUrl = value;
    if (key === 'VITE_SUPABASE_ANON_KEY') supabaseKey = value;
  });
} catch (error) {
  console.error('❌ Could not load .env.local file:', error.message);
}

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to split SQL into individual statements
function splitSQLStatements(sql) {
  // Remove comments and normalize whitespace
  const cleanSQL = sql
    .replace(/--.*$/gm, '') // Remove line comments
    .replace(/\/\*[\s\S]*?\*\//g, '') // Remove block comments
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();

  // Split by semicolons, but be careful with semicolons inside strings
  const statements = [];
  let current = '';
  let inString = false;
  let stringChar = '';

  for (let i = 0; i < cleanSQL.length; i++) {
    const char = cleanSQL[i];
    const prevChar = i > 0 ? cleanSQL[i - 1] : '';

    if (!inString && (char === "'" || char === '"')) {
      inString = true;
      stringChar = char;
    } else if (inString && char === stringChar && prevChar !== '\\') {
      inString = false;
      stringChar = '';
    } else if (!inString && char === ';') {
      if (current.trim()) {
        statements.push(current.trim());
        current = '';
        continue;
      }
    }
    
    current += char;
  }

  // Add the last statement if it exists
  if (current.trim()) {
    statements.push(current.trim());
  }

  return statements.filter(stmt => stmt.length > 0);
}

// Function to execute individual SQL statements using Supabase client
async function executeSQL(statements) {
  const results = [];
  let successCount = 0;
  let errorCount = 0;

  console.log(`📝 Executing ${statements.length} SQL statements...`);

  for (let i = 0; i < statements.length; i++) {
    const statement = statements[i];
    console.log(`\n🔄 Executing statement ${i + 1}/${statements.length}:`);
    console.log(`   ${statement.substring(0, 80)}${statement.length > 80 ? '...' : ''}`);

    try {
      // Use the sql template literal for raw SQL execution
      const { data, error } = await supabase.rpc('exec_sql', {
        sql_statement: statement
      });

      if (error) {
        console.error(`❌ Error in statement ${i + 1}:`, error.message);
        errorCount++;
        results.push({ statement: i + 1, success: false, error: error.message });
      } else {
        console.log(`✅ Statement ${i + 1} executed successfully`);
        successCount++;
        results.push({ statement: i + 1, success: true, data });
      }
    } catch (err) {
      console.error(`❌ Unexpected error in statement ${i + 1}:`, err.message);
      errorCount++;
      results.push({ statement: i + 1, success: false, error: err.message });
    }

    // Small delay between statements to avoid overwhelming the database
    await new Promise(resolve => setTimeout(resolve, 200));
  }

  return { results, successCount, errorCount };
}

// Main execution function
async function createAdvancedBISchema() {
  console.log('🚀 BlackVeil Security - Advanced BI Schema Creation');
  console.log('=' .repeat(60));
  
  try {
    // Read the SQL schema file
    const sqlFilePath = join(__dirname, 'create-advanced-bi-schema.sql');
    console.log(`📖 Reading SQL file: ${sqlFilePath}`);
    
    const sqlContent = readFileSync(sqlFilePath, 'utf8');
    console.log(`✅ SQL file loaded (${sqlContent.length} characters)`);

    // Split SQL into individual statements
    const statements = splitSQLStatements(sqlContent);
    console.log(`📝 Parsed ${statements.length} SQL statements`);

    // Execute SQL statements
    const { results, successCount, errorCount } = await executeSQL(statements);

    // Summary
    console.log('\n📊 Execution Summary:');
    console.log('=' .repeat(40));
    console.log(`✅ Successful statements: ${successCount}`);
    console.log(`❌ Failed statements: ${errorCount}`);
    console.log(`📝 Total statements: ${statements.length}`);

    if (errorCount > 0) {
      console.log('\n❌ Failed Statements:');
      results.filter(r => !r.success).forEach(r => {
        console.log(`   Statement ${r.statement}: ${r.error}`);
      });
    }

    return errorCount === 0;

  } catch (error) {
    console.error('❌ Schema creation failed:', error.message);
    return false;
  }
}

// Verification function
async function verifySchemaCreation() {
  console.log('\n🔍 Verifying Advanced BI Schema Creation...');
  
  const tables = [
    'enhanced_lead_scores',
    'user_journey_events', 
    'ab_tests',
    'ab_test_variants',
    'ab_test_participations'
  ];

  const results = {};
  
  for (const table of tables) {
    try {
      const { count, error } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.log(`❌ ${table}: ${error.message}`);
        results[table] = { exists: false, error: error.message };
      } else {
        console.log(`✅ ${table}: Table exists (${count || 0} records)`);
        results[table] = { exists: true, count: count || 0 };
      }
    } catch (err) {
      console.log(`❌ ${table}: ${err.message}`);
      results[table] = { exists: false, error: err.message };
    }
  }

  const allTablesExist = Object.values(results).every(r => r.exists);
  
  console.log(`\n🎯 Schema Verification: ${allTablesExist ? '✅ SUCCESS' : '❌ INCOMPLETE'}`);
  
  return { allTablesExist, results };
}

// Run the schema creation
createAdvancedBISchema().then(async (success) => {
  if (success) {
    console.log('\n✅ Advanced BI schema creation completed successfully!');
    
    // Verify the creation
    const verification = await verifySchemaCreation();
    
    if (verification.allTablesExist) {
      console.log('\n🎉 All advanced BI tables created successfully!');
      console.log('📊 BI Dashboard features are now fully operational');
      console.log('\n📋 Next Steps:');
      console.log('   1. Run: node scripts/database-inspection.js');
      console.log('   2. Test the BI dashboard at /admin');
      console.log('   3. Verify enhanced lead scoring features');
      process.exit(0);
    } else {
      console.log('\n⚠️ Some tables may not have been created properly');
      console.log('📋 Please check the errors above and retry if needed');
      process.exit(1);
    }
  } else {
    console.log('\n❌ Schema creation encountered errors');
    console.log('📋 Please check the error messages above');
    process.exit(1);
  }
}).catch(error => {
  console.error('❌ Script execution failed:', error);
  process.exit(1);
});
