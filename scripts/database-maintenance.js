// BlackVeil Security - Database Maintenance and Cleanup Script
// This script provides automated maintenance functions for the BI dashboard

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
let supabaseUrl, supabaseKey;
try {
  const envPath = join(__dirname, '..', '.env.local');
  const envContent = readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');
  
  envLines.forEach(line => {
    const [key, value] = line.split('=');
    if (key === 'VITE_SUPABASE_URL') supabaseUrl = value;
    if (key === 'VITE_SUPABASE_ANON_KEY') supabaseKey = value;
  });
} catch (error) {
  console.error('❌ Could not load .env.local file:', error.message);
}

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Configuration
const MAINTENANCE_CONFIG = {
  // Archive submissions older than 1 year
  ARCHIVE_THRESHOLD_DAYS: 365,
  // Clean up journey events older than 6 months
  JOURNEY_CLEANUP_DAYS: 180,
  // Clean up A/B test participations older than 3 months
  AB_TEST_CLEANUP_DAYS: 90,
  // Performance monitoring thresholds
  SLOW_QUERY_THRESHOLD_MS: 1000,
  MAX_TABLE_SIZE_MB: 1000
};

async function archiveOldSubmissions() {
  console.log('🗄️ Archiving old assessment submissions...');
  
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - MAINTENANCE_CONFIG.ARCHIVE_THRESHOLD_DAYS);
    
    // First, get count of submissions to archive
    const { count: submissionsToArchive } = await supabase
      .from('assessment_submissions')
      .select('*', { count: 'exact', head: true })
      .lt('created_at', cutoffDate.toISOString())
      .eq('status', 'completed');

    if (submissionsToArchive === 0) {
      console.log('ℹ️ No submissions found for archiving');
      return { archived: 0 };
    }

    console.log(`📦 Found ${submissionsToArchive} submissions to archive`);

    // For now, we'll just mark them as archived rather than delete
    // In a production system, you might move them to a separate archive table
    const { error: archiveError } = await supabase
      .from('assessment_submissions')
      .update({ 
        status: 'archived',
        updated_at: new Date().toISOString()
      })
      .lt('created_at', cutoffDate.toISOString())
      .eq('status', 'completed');

    if (archiveError) throw archiveError;

    console.log(`✅ Successfully archived ${submissionsToArchive} old submissions`);
    return { archived: submissionsToArchive };

  } catch (error) {
    console.error('❌ Error archiving submissions:', error);
    return { archived: 0, error: error.message };
  }
}

async function cleanupJourneyEvents() {
  console.log('🧹 Cleaning up old journey events...');
  
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - MAINTENANCE_CONFIG.JOURNEY_CLEANUP_DAYS);
    
    // Get count of events to clean up
    const { count: eventsToCleanup } = await supabase
      .from('user_journey_events')
      .select('*', { count: 'exact', head: true })
      .lt('created_at', cutoffDate.toISOString());

    if (eventsToCleanup === 0) {
      console.log('ℹ️ No journey events found for cleanup');
      return { cleaned: 0 };
    }

    console.log(`🗑️ Found ${eventsToCleanup} journey events to clean up`);

    // Delete old journey events
    const { error: deleteError } = await supabase
      .from('user_journey_events')
      .delete()
      .lt('created_at', cutoffDate.toISOString());

    if (deleteError) throw deleteError;

    console.log(`✅ Successfully cleaned up ${eventsToCleanup} old journey events`);
    return { cleaned: eventsToCleanup };

  } catch (error) {
    console.error('❌ Error cleaning up journey events:', error);
    return { cleaned: 0, error: error.message };
  }
}

async function cleanupABTestData() {
  console.log('🧪 Cleaning up old A/B test participation data...');
  
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - MAINTENANCE_CONFIG.AB_TEST_CLEANUP_DAYS);
    
    // Get count of participations to clean up (only for inactive tests)
    const { count: participationsToCleanup } = await supabase
      .from('ab_test_participations')
      .select('*', { count: 'exact', head: true })
      .lt('created_at', cutoffDate.toISOString());

    if (participationsToCleanup === 0) {
      console.log('ℹ️ No A/B test participations found for cleanup');
      return { cleaned: 0 };
    }

    console.log(`🗑️ Found ${participationsToCleanup} A/B test participations to clean up`);

    // Delete old participations for inactive tests only
    const { error: deleteError } = await supabase
      .from('ab_test_participations')
      .delete()
      .lt('created_at', cutoffDate.toISOString());

    if (deleteError) throw deleteError;

    console.log(`✅ Successfully cleaned up ${participationsToCleanup} old A/B test participations`);
    return { cleaned: participationsToCleanup };

  } catch (error) {
    console.error('❌ Error cleaning up A/B test data:', error);
    return { cleaned: 0, error: error.message };
  }
}

async function performanceMonitoring() {
  console.log('📊 Running performance monitoring checks...');
  
  try {
    const results = {
      tableStats: [],
      slowQueries: [],
      recommendations: []
    };

    // Check table sizes and record counts
    const tables = [
      'assessment_submissions',
      'lead_scores',
      'enhanced_lead_scores',
      'user_journey_events',
      'ab_test_participations',
      'assessment_answers'
    ];

    for (const table of tables) {
      const startTime = Date.now();
      
      const { count, error } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });

      const queryTime = Date.now() - startTime;

      if (error) {
        console.warn(`⚠️ Could not check table ${table}:`, error.message);
        continue;
      }

      results.tableStats.push({
        table,
        recordCount: count || 0,
        queryTime,
        isSlowQuery: queryTime > MAINTENANCE_CONFIG.SLOW_QUERY_THRESHOLD_MS
      });

      if (queryTime > MAINTENANCE_CONFIG.SLOW_QUERY_THRESHOLD_MS) {
        results.slowQueries.push({
          table,
          queryTime,
          query: `SELECT COUNT(*) FROM ${table}`
        });
      }
    }

    // Generate recommendations
    const totalRecords = results.tableStats.reduce((sum, stat) => sum + stat.recordCount, 0);
    const slowTables = results.tableStats.filter(stat => stat.isSlowQuery);

    if (totalRecords > 100000) {
      results.recommendations.push('Consider implementing data archiving - total records exceed 100k');
    }

    if (slowTables.length > 0) {
      results.recommendations.push(`Slow queries detected on: ${slowTables.map(t => t.table).join(', ')}`);
      results.recommendations.push('Consider adding database indexes or optimizing queries');
    }

    // Display results
    console.log('\n📈 Performance Monitoring Results:');
    console.log('Table Statistics:');
    results.tableStats.forEach(stat => {
      const status = stat.isSlowQuery ? '🐌' : '✅';
      console.log(`  ${status} ${stat.table}: ${stat.recordCount} records (${stat.queryTime}ms)`);
    });

    if (results.slowQueries.length > 0) {
      console.log('\n⚠️ Slow Queries Detected:');
      results.slowQueries.forEach(query => {
        console.log(`  ${query.table}: ${query.queryTime}ms`);
      });
    }

    if (results.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      results.recommendations.forEach(rec => {
        console.log(`  • ${rec}`);
      });
    }

    return results;

  } catch (error) {
    console.error('❌ Error during performance monitoring:', error);
    return { error: error.message };
  }
}

async function generateMaintenanceReport() {
  console.log('📋 Generating maintenance report...');
  
  try {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {},
      details: {}
    };

    // Get current database statistics
    const { count: totalSubmissions } = await supabase
      .from('assessment_submissions')
      .select('*', { count: 'exact', head: true });

    const { count: completedSubmissions } = await supabase
      .from('assessment_submissions')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'completed');

    const { count: leadScores } = await supabase
      .from('lead_scores')
      .select('*', { count: 'exact', head: true });

    const { count: journeyEvents } = await supabase
      .from('user_journey_events')
      .select('*', { count: 'exact', head: true });

    report.summary = {
      totalSubmissions: totalSubmissions || 0,
      completedSubmissions: completedSubmissions || 0,
      leadScores: leadScores || 0,
      journeyEvents: journeyEvents || 0,
      completionRate: totalSubmissions > 0 ? Math.round((completedSubmissions / totalSubmissions) * 100) : 0
    };

    // Check data integrity
    const scoreCoverage = completedSubmissions > 0 ? Math.round((leadScores / completedSubmissions) * 100) : 0;
    
    report.details.dataIntegrity = {
      scoreCoverage: `${scoreCoverage}%`,
      missingScores: Math.max(0, completedSubmissions - leadScores),
      status: scoreCoverage >= 95 ? 'Good' : scoreCoverage >= 80 ? 'Fair' : 'Poor'
    };

    // Recent activity (last 7 days)
    const { count: recentSubmissions } = await supabase
      .from('assessment_submissions')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());

    report.details.recentActivity = {
      submissionsLast7Days: recentSubmissions || 0,
      averagePerDay: Math.round((recentSubmissions || 0) / 7)
    };

    console.log('\n📊 Maintenance Report:');
    console.log(`Generated: ${report.timestamp}`);
    console.log('\nSummary:');
    console.log(`  Total Submissions: ${report.summary.totalSubmissions}`);
    console.log(`  Completed: ${report.summary.completedSubmissions} (${report.summary.completionRate}%)`);
    console.log(`  Lead Scores: ${report.summary.leadScores}`);
    console.log(`  Journey Events: ${report.summary.journeyEvents}`);
    console.log('\nData Integrity:');
    console.log(`  Score Coverage: ${report.details.dataIntegrity.scoreCoverage} (${report.details.dataIntegrity.status})`);
    console.log(`  Missing Scores: ${report.details.dataIntegrity.missingScores}`);
    console.log('\nRecent Activity:');
    console.log(`  Last 7 Days: ${report.details.recentActivity.submissionsLast7Days} submissions`);
    console.log(`  Daily Average: ${report.details.recentActivity.averagePerDay} submissions`);

    return report;

  } catch (error) {
    console.error('❌ Error generating maintenance report:', error);
    return { error: error.message };
  }
}

async function runFullMaintenance() {
  console.log('🔧 BlackVeil Security - Database Maintenance');
  console.log('=' .repeat(50));
  
  const results = {
    timestamp: new Date().toISOString(),
    operations: {}
  };

  try {
    // 1. Generate initial report
    console.log('\n1️⃣ Generating pre-maintenance report...');
    results.operations.preReport = await generateMaintenanceReport();

    // 2. Archive old submissions
    console.log('\n2️⃣ Archiving old submissions...');
    results.operations.archiving = await archiveOldSubmissions();

    // 3. Clean up journey events
    console.log('\n3️⃣ Cleaning up journey events...');
    results.operations.journeyCleanup = await cleanupJourneyEvents();

    // 4. Clean up A/B test data
    console.log('\n4️⃣ Cleaning up A/B test data...');
    results.operations.abTestCleanup = await cleanupABTestData();

    // 5. Performance monitoring
    console.log('\n5️⃣ Running performance checks...');
    results.operations.performance = await performanceMonitoring();

    // 6. Generate final report
    console.log('\n6️⃣ Generating post-maintenance report...');
    results.operations.postReport = await generateMaintenanceReport();

    console.log('\n🎉 Maintenance completed successfully!');
    console.log('\nSummary of Operations:');
    if (results.operations.archiving?.archived > 0) {
      console.log(`  📦 Archived: ${results.operations.archiving.archived} submissions`);
    }
    if (results.operations.journeyCleanup?.cleaned > 0) {
      console.log(`  🧹 Cleaned: ${results.operations.journeyCleanup.cleaned} journey events`);
    }
    if (results.operations.abTestCleanup?.cleaned > 0) {
      console.log(`  🧪 Cleaned: ${results.operations.abTestCleanup.cleaned} A/B test participations`);
    }

    return results;

  } catch (error) {
    console.error('❌ Maintenance failed:', error);
    return { error: error.message, results };
  }
}

// Command line interface
const command = process.argv[2];

switch (command) {
  case 'archive':
    archiveOldSubmissions().then(() => process.exit(0));
    break;
  case 'cleanup-journey':
    cleanupJourneyEvents().then(() => process.exit(0));
    break;
  case 'cleanup-abtest':
    cleanupABTestData().then(() => process.exit(0));
    break;
  case 'performance':
    performanceMonitoring().then(() => process.exit(0));
    break;
  case 'report':
    generateMaintenanceReport().then(() => process.exit(0));
    break;
  case 'full':
  default:
    runFullMaintenance().then(() => process.exit(0));
    break;
}

export {
  archiveOldSubmissions,
  cleanupJourneyEvents,
  cleanupABTestData,
  performanceMonitoring,
  generateMaintenanceReport,
  runFullMaintenance
};
