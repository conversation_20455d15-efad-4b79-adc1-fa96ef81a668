-- Populate Missing Assessment Questions - FINAL CORRECTED VERSION
-- Run this script in the Supabase SQL Editor to add questions for assessments that are missing them

-- This script uses the correct data types:
-- assessment_questions.id = integer (auto-increment)
-- assessment_question_options.id = uuid (auto-generated)
-- assessment_question_options.question_id = integer (references assessment_questions.id)

-- First, let's create a temporary function to help us insert questions and options
CREATE OR REPLACE FUNCTION insert_assessment_question_with_options(
  p_assessment_slug text,
  p_question_text text,
  p_category text,
  p_order_index integer,
  p_options jsonb
) RETURNS integer AS $$
DECLARE
  v_assessment_type_id uuid;
  v_question_id integer;
  v_option jsonb;
BEGIN
  -- Get the assessment type ID
  SELECT id INTO v_assessment_type_id 
  FROM assessment_types 
  WHERE slug = p_assessment_slug;
  
  IF v_assessment_type_id IS NULL THEN
    RAISE EXCEPTION 'Assessment type with slug % not found', p_assessment_slug;
  END IF;
  
  -- Insert the question (let the id auto-increment)
  INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
  VALUES (v_assessment_type_id, p_question_text, p_category, p_order_index, true)
  RETURNING id INTO v_question_id;
  
  -- Insert the options
  FOR v_option IN SELECT * FROM jsonb_array_elements(p_options)
  LOOP
    INSERT INTO assessment_question_options (id, question_id, option_text, risk_score, order_index)
    VALUES (
      gen_random_uuid(),
      v_question_id,
      v_option->>'text',
      (v_option->>'risk_score')::integer,
      (v_option->>'order_index')::integer
    );
  END LOOP;
  
  RETURN v_question_id;
END;
$$ LANGUAGE plpgsql;

-- Now insert questions for Cybersecurity Maturity Assessment
SELECT insert_assessment_question_with_options(
  'cybersecurity-maturity',
  'How often does your organization conduct cybersecurity risk assessments?',
  'Risk Management',
  1,
  '[
    {"text": "Never or rarely (annually or less)", "risk_score": 4, "order_index": 1},
    {"text": "Occasionally (every 6-12 months)", "risk_score": 3, "order_index": 2},
    {"text": "Regularly (quarterly)", "risk_score": 2, "order_index": 3},
    {"text": "Continuously (monthly or more)", "risk_score": 1, "order_index": 4}
  ]'::jsonb
);

SELECT insert_assessment_question_with_options(
  'cybersecurity-maturity',
  'What type of cybersecurity training do you provide to employees?',
  'Security Awareness',
  2,
  '[
    {"text": "No formal training provided", "risk_score": 4, "order_index": 1},
    {"text": "Basic annual training only", "risk_score": 3, "order_index": 2},
    {"text": "Regular training with updates", "risk_score": 2, "order_index": 3},
    {"text": "Comprehensive ongoing training with simulations", "risk_score": 1, "order_index": 4}
  ]'::jsonb
);

SELECT insert_assessment_question_with_options(
  'cybersecurity-maturity',
  'How does your organization manage software updates and patches?',
  'Patch Management',
  3,
  '[
    {"text": "Manual updates when remembered", "risk_score": 4, "order_index": 1},
    {"text": "Scheduled monthly updates", "risk_score": 3, "order_index": 2},
    {"text": "Automated updates for most systems", "risk_score": 2, "order_index": 3},
    {"text": "Comprehensive automated patch management", "risk_score": 1, "order_index": 4}
  ]'::jsonb
);

SELECT insert_assessment_question_with_options(
  'cybersecurity-maturity',
  'What backup and recovery procedures do you have in place?',
  'Business Continuity',
  4,
  '[
    {"text": "No formal backup procedures", "risk_score": 4, "order_index": 1},
    {"text": "Basic local backups", "risk_score": 3, "order_index": 2},
    {"text": "Regular offsite backups", "risk_score": 2, "order_index": 3},
    {"text": "Comprehensive backup with tested recovery", "risk_score": 1, "order_index": 4}
  ]'::jsonb
);

SELECT insert_assessment_question_with_options(
  'cybersecurity-maturity',
  'How do you control access to sensitive systems and data?',
  'Access Control',
  5,
  '[
    {"text": "Basic password protection only", "risk_score": 4, "order_index": 1},
    {"text": "Role-based access with passwords", "risk_score": 3, "order_index": 2},
    {"text": "Multi-factor authentication for some systems", "risk_score": 2, "order_index": 3},
    {"text": "Comprehensive MFA and zero-trust approach", "risk_score": 1, "order_index": 4}
  ]'::jsonb
);

SELECT insert_assessment_question_with_options(
  'cybersecurity-maturity',
  'Do you have an incident response plan for cybersecurity breaches?',
  'Incident Response',
  6,
  '[
    {"text": "No formal incident response plan", "risk_score": 4, "order_index": 1},
    {"text": "Basic plan but not tested", "risk_score": 3, "order_index": 2},
    {"text": "Documented plan with some testing", "risk_score": 2, "order_index": 3},
    {"text": "Comprehensive tested plan with regular drills", "risk_score": 1, "order_index": 4}
  ]'::jsonb
);

SELECT insert_assessment_question_with_options(
  'cybersecurity-maturity',
  'How do you monitor your network for security threats?',
  'Threat Detection',
  7,
  '[
    {"text": "No active monitoring", "risk_score": 4, "order_index": 1},
    {"text": "Basic antivirus software only", "risk_score": 3, "order_index": 2},
    {"text": "Network monitoring with some alerting", "risk_score": 2, "order_index": 3},
    {"text": "Advanced threat detection and response", "risk_score": 1, "order_index": 4}
  ]'::jsonb
);

SELECT insert_assessment_question_with_options(
  'cybersecurity-maturity',
  'What is your approach to vendor and third-party security?',
  'Third-Party Risk',
  8,
  '[
    {"text": "No vendor security assessments", "risk_score": 4, "order_index": 1},
    {"text": "Basic vendor questionnaires", "risk_score": 3, "order_index": 2},
    {"text": "Regular vendor security reviews", "risk_score": 2, "order_index": 3},
    {"text": "Comprehensive third-party risk management", "risk_score": 1, "order_index": 4}
  ]'::jsonb
);

-- Now insert questions for DMARC Compliance Assessment
SELECT insert_assessment_question_with_options(
  'dmarc-compliance',
  'Have you implemented SPF (Sender Policy Framework) for your domain?',
  'Email Authentication',
  1,
  '[
    {"text": "No SPF record configured", "risk_score": 4, "order_index": 1},
    {"text": "Basic SPF record with some gaps", "risk_score": 3, "order_index": 2},
    {"text": "Comprehensive SPF record configured", "risk_score": 2, "order_index": 3},
    {"text": "SPF with strict policy and monitoring", "risk_score": 1, "order_index": 4}
  ]'::jsonb
);

SELECT insert_assessment_question_with_options(
  'dmarc-compliance',
  'Do you have DKIM (DomainKeys Identified Mail) configured?',
  'Email Authentication',
  2,
  '[
    {"text": "No DKIM signing configured", "risk_score": 4, "order_index": 1},
    {"text": "DKIM for some email services only", "risk_score": 3, "order_index": 2},
    {"text": "DKIM configured for all outbound email", "risk_score": 2, "order_index": 3},
    {"text": "DKIM with key rotation and monitoring", "risk_score": 1, "order_index": 4}
  ]'::jsonb
);

SELECT insert_assessment_question_with_options(
  'dmarc-compliance',
  'What is your current DMARC policy setting?',
  'DMARC Policy',
  3,
  '[
    {"text": "No DMARC record published", "risk_score": 4, "order_index": 1},
    {"text": "DMARC with ''none'' policy (monitoring only)", "risk_score": 3, "order_index": 2},
    {"text": "DMARC with ''quarantine'' policy", "risk_score": 2, "order_index": 3},
    {"text": "DMARC with ''reject'' policy", "risk_score": 1, "order_index": 4}
  ]'::jsonb
);

SELECT insert_assessment_question_with_options(
  'dmarc-compliance',
  'How do you monitor DMARC reports and failures?',
  'Monitoring',
  4,
  '[
    {"text": "No DMARC report monitoring", "risk_score": 4, "order_index": 1},
    {"text": "Occasional manual review of reports", "risk_score": 3, "order_index": 2},
    {"text": "Regular monitoring with basic analysis", "risk_score": 2, "order_index": 3},
    {"text": "Automated monitoring with alerting", "risk_score": 1, "order_index": 4}
  ]'::jsonb
);

SELECT insert_assessment_question_with_options(
  'dmarc-compliance',
  'Do you have email security awareness training for employees?',
  'Security Awareness',
  5,
  '[
    {"text": "No email security training", "risk_score": 4, "order_index": 1},
    {"text": "Basic annual training", "risk_score": 3, "order_index": 2},
    {"text": "Regular training with phishing simulations", "risk_score": 2, "order_index": 3},
    {"text": "Comprehensive ongoing training program", "risk_score": 1, "order_index": 4}
  ]'::jsonb
);

SELECT insert_assessment_question_with_options(
  'dmarc-compliance',
  'How do you handle email from external domains that fail authentication?',
  'Email Filtering',
  6,
  '[
    {"text": "No special handling for failed authentication", "risk_score": 4, "order_index": 1},
    {"text": "Basic spam filtering only", "risk_score": 3, "order_index": 2},
    {"text": "Enhanced filtering with some authentication checks", "risk_score": 2, "order_index": 3},
    {"text": "Strict authentication-based filtering", "risk_score": 1, "order_index": 4}
  ]'::jsonb
);

-- Clean up the temporary function
DROP FUNCTION insert_assessment_question_with_options(text, text, text, integer, jsonb);

-- Verification query to check what we've added
SELECT 
  at.title as assessment_title,
  at.slug,
  COUNT(aq.id) as question_count,
  COUNT(aqo.id) as option_count
FROM assessment_types at
LEFT JOIN assessment_questions aq ON at.id = aq.assessment_type_id
LEFT JOIN assessment_question_options aqo ON aq.id = aqo.question_id
WHERE at.slug IN ('cybersecurity-maturity', 'dmarc-compliance')
GROUP BY at.id, at.title, at.slug
ORDER BY at.title;
