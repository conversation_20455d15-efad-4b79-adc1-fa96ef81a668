// Direct SQL Execution Script for Advanced BI Schema
// This script executes the SQL schema file using a direct database connection approach

import { createClient } from '@supabase/supabase-js';
import { readFileSync, writeFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { spawn } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env.local file
let supabaseUrl, supabaseKey;
try {
  const envPath = join(__dirname, '..', '.env.local');
  const envContent = readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  envLines.forEach(line => {
    const [key, value] = line.split('=');
    if (key === 'VITE_SUPABASE_URL') supabaseUrl = value;
    if (key === 'VITE_SUPABASE_ANON_KEY') supabaseKey = value;
  });
} catch (error) {
  console.error('❌ Could not load .env.local file:', error.message);
}

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Extract database connection details from Supabase URL
function parseSupabaseUrl(url) {
  try {
    const urlObj = new URL(url);
    const projectRef = urlObj.hostname.split('.')[0];
    
    return {
      host: `db.${projectRef}.supabase.co`,
      port: 5432,
      database: 'postgres',
      projectRef: projectRef
    };
  } catch (err) {
    console.error('❌ Could not parse Supabase URL:', err.message);
    return null;
  }
}

// Function to execute SQL using psql command line tool
async function executeSQLWithPsql(sqlFilePath, connectionDetails) {
  return new Promise((resolve, reject) => {
    console.log('🔄 Attempting to execute SQL using psql command line tool...');
    
    // Note: This requires the user to have psql installed and configured
    // We'll provide instructions if psql is not available
    
    const psqlArgs = [
      '-h', connectionDetails.host,
      '-p', connectionDetails.port.toString(),
      '-d', connectionDetails.database,
      '-f', sqlFilePath,
      '--single-transaction'
    ];

    console.log(`📝 Command: psql ${psqlArgs.join(' ')}`);
    console.log('⚠️ Note: You will be prompted for the database password');
    console.log('   Use your Supabase project password (not the anon key)');

    const psql = spawn('psql', psqlArgs, {
      stdio: ['inherit', 'pipe', 'pipe']
    });

    let stdout = '';
    let stderr = '';

    psql.stdout.on('data', (data) => {
      stdout += data.toString();
      console.log(data.toString());
    });

    psql.stderr.on('data', (data) => {
      stderr += data.toString();
      console.error(data.toString());
    });

    psql.on('close', (code) => {
      if (code === 0) {
        console.log('✅ SQL execution completed successfully');
        resolve(true);
      } else {
        console.error(`❌ psql exited with code ${code}`);
        reject(new Error(`psql execution failed with code ${code}`));
      }
    });

    psql.on('error', (err) => {
      if (err.code === 'ENOENT') {
        console.error('❌ psql command not found');
        console.log('📋 Please install PostgreSQL client tools:');
        console.log('   macOS: brew install postgresql');
        console.log('   Ubuntu: sudo apt-get install postgresql-client');
        console.log('   Windows: Download from https://www.postgresql.org/download/');
        reject(new Error('psql not available'));
      } else {
        reject(err);
      }
    });
  });
}

// Function to create a simplified SQL file for execution
function createExecutableSQL() {
  const sqlFilePath = join(__dirname, 'create-advanced-bi-schema.sql');
  const outputPath = join(__dirname, 'temp-schema-execution.sql');
  
  try {
    let sqlContent = readFileSync(sqlFilePath, 'utf8');
    
    // Remove problematic statements that might cause issues
    sqlContent = sqlContent
      .replace(/-- 11\. Verification query[\s\S]*$/m, '') // Remove verification section
      .replace(/DO \$\$[\s\S]*?\$\$;/g, '') // Remove DO blocks
      .trim();
    
    // Add a simple verification at the end
    sqlContent += '\n\n-- Simple verification\n';
    sqlContent += 'SELECT \'Schema creation completed\' as status;\n';
    
    writeFileSync(outputPath, sqlContent, 'utf8');
    console.log(`📝 Created executable SQL file: ${outputPath}`);
    
    return outputPath;
  } catch (err) {
    console.error('❌ Error creating executable SQL file:', err.message);
    return null;
  }
}

// Function to verify table creation
async function verifyTablesCreation() {
  console.log('\n🔍 Verifying Advanced BI Tables Creation...');
  
  const tables = [
    'enhanced_lead_scores',
    'user_journey_events', 
    'ab_tests',
    'ab_test_variants',
    'ab_test_participations'
  ];

  const results = {};
  let allExist = true;
  
  for (const table of tables) {
    try {
      const { count, error } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.log(`❌ ${table}: ${error.message}`);
        results[table] = false;
        allExist = false;
      } else {
        console.log(`✅ ${table}: Table exists (${count || 0} records)`);
        results[table] = true;
      }
    } catch (err) {
      console.log(`❌ ${table}: ${err.message}`);
      results[table] = false;
      allExist = false;
    }
  }

  return { allExist, results };
}

// Alternative approach: Use curl to execute SQL via Supabase REST API
async function executeSQLViaAPI(sql) {
  console.log('🔄 Attempting to execute SQL via Supabase REST API...');
  
  try {
    // This is a more advanced approach that requires the service role key
    // For now, we'll provide instructions for manual execution
    
    console.log('⚠️ API-based SQL execution requires service role key');
    console.log('📋 For security reasons, using the anon key for DDL operations');
    console.log('   is not recommended. Please use the manual approach.');
    
    return false;
  } catch (err) {
    console.error('❌ API execution failed:', err.message);
    return false;
  }
}

// Main execution function
async function executeAdvancedBISchema() {
  console.log('🚀 BlackVeil Security - Direct SQL Execution');
  console.log('📋 Attempting to execute advanced BI schema programmatically...');
  console.log('=' .repeat(60));

  try {
    // First check if tables already exist
    console.log('\n📊 Checking current table status...');
    const initialStatus = await verifyTablesCreation();
    
    if (initialStatus.allExist) {
      console.log('\n🎉 All advanced BI tables already exist!');
      return true;
    }

    // Parse Supabase connection details
    const connectionDetails = parseSupabaseUrl(supabaseUrl);
    if (!connectionDetails) {
      throw new Error('Could not parse Supabase connection details');
    }

    console.log(`\n🔗 Database Connection Details:`);
    console.log(`   Host: ${connectionDetails.host}`);
    console.log(`   Port: ${connectionDetails.port}`);
    console.log(`   Database: ${connectionDetails.database}`);

    // Create executable SQL file
    const executableSQLPath = createExecutableSQL();
    if (!executableSQLPath) {
      throw new Error('Could not create executable SQL file');
    }

    // Try to execute using psql
    try {
      await executeSQLWithPsql(executableSQLPath, connectionDetails);
      
      // Verify creation after execution
      console.log('\n🔍 Verifying table creation after SQL execution...');
      const finalStatus = await verifyTablesCreation();
      
      if (finalStatus.allExist) {
        console.log('\n🎉 All tables created successfully!');
        return true;
      } else {
        console.log('\n⚠️ Some tables may not have been created properly');
        return false;
      }
      
    } catch (psqlError) {
      console.log('\n⚠️ Direct psql execution failed, providing manual instructions...');
      
      // Provide manual execution instructions
      console.log('\n📋 Manual Execution Instructions:');
      console.log('=' .repeat(40));
      console.log('1. Option A - Supabase SQL Editor (Recommended):');
      console.log('   • Go to https://supabase.com/dashboard');
      console.log('   • Navigate to your BlackVeil project');
      console.log('   • Click "SQL Editor" in the left sidebar');
      console.log('   • Copy contents of scripts/create-advanced-bi-schema.sql');
      console.log('   • Paste and click "Run"');
      
      console.log('\n2. Option B - Command Line (Advanced):');
      console.log(`   • Install psql: brew install postgresql`);
      console.log(`   • Get your database password from Supabase dashboard`);
      console.log(`   • Run: psql -h ${connectionDetails.host} -p ${connectionDetails.port} -d ${connectionDetails.database} -f ${executableSQLPath}`);
      
      return false;
    }

  } catch (error) {
    console.error('❌ Schema execution failed:', error.message);
    return false;
  }
}

// Run the execution
executeAdvancedBISchema().then(async (success) => {
  if (success) {
    console.log('\n✅ Advanced BI schema execution completed successfully!');
    console.log('🎉 All tables are ready for use');
    console.log('\n📋 Next Steps:');
    console.log('   1. Run: node scripts/database-inspection.js');
    console.log('   2. Test the BI dashboard at /admin');
    console.log('   3. Verify enhanced lead scoring features');
    process.exit(0);
  } else {
    console.log('\n📋 Manual execution required');
    console.log('⚠️ Please follow the instructions above to create the tables');
    console.log('   Then run: node scripts/database-inspection.js to verify');
    process.exit(1);
  }
}).catch(error => {
  console.error('❌ Script execution failed:', error);
  process.exit(1);
});
