-- FINAL FIX PART 2: Assessment Question Options Population
-- Run this AFTER populate-assessments-final-fix-corrected.sql
-- This script populates all question options with proper data types
-- Note: assessment_question_options.id IS auto-generated (UUID), so we don't specify it

-- Step 1: Insert options for Cybersecurity Maturity Assessment

-- Question 1: Risk assessments frequency
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
  aq.id,
  'Never or rarely (annually or less)',
  4,
  1
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 1
AND NOT EXISTS (
  SELECT 1 FROM assessment_question_options aqo 
  WHERE aqo.question_id = aq.id AND aqo.order_index = 1
);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
  aq.id,
  'Occasionally (every 6-12 months)',
  3,
  2
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 1
AND NOT EXISTS (
  SELECT 1 FROM assessment_question_options aqo 
  WHERE aqo.question_id = aq.id AND aqo.order_index = 2
);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
  aq.id,
  'Regularly (quarterly)',
  2,
  3
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 1
AND NOT EXISTS (
  SELECT 1 FROM assessment_question_options aqo 
  WHERE aqo.question_id = aq.id AND aqo.order_index = 3
);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
  aq.id,
  'Continuously (monthly or more)',
  1,
  4
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 1
AND NOT EXISTS (
  SELECT 1 FROM assessment_question_options aqo 
  WHERE aqo.question_id = aq.id AND aqo.order_index = 4
);

-- Question 2: Security training
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
  aq.id,
  'No formal training provided',
  4,
  1
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 2
AND NOT EXISTS (
  SELECT 1 FROM assessment_question_options aqo 
  WHERE aqo.question_id = aq.id AND aqo.order_index = 1
);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
  aq.id,
  'Basic annual training only',
  3,
  2
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 2
AND NOT EXISTS (
  SELECT 1 FROM assessment_question_options aqo 
  WHERE aqo.question_id = aq.id AND aqo.order_index = 2
);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
  aq.id,
  'Regular training with updates',
  2,
  3
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 2
AND NOT EXISTS (
  SELECT 1 FROM assessment_question_options aqo 
  WHERE aqo.question_id = aq.id AND aqo.order_index = 3
);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
  aq.id,
  'Comprehensive ongoing training with simulations',
  1,
  4
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 2
AND NOT EXISTS (
  SELECT 1 FROM assessment_question_options aqo 
  WHERE aqo.question_id = aq.id AND aqo.order_index = 4
);

-- Question 3: Patch management
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
  aq.id,
  'Manual updates when remembered',
  4,
  1
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 3
AND NOT EXISTS (
  SELECT 1 FROM assessment_question_options aqo 
  WHERE aqo.question_id = aq.id AND aqo.order_index = 1
);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
  aq.id,
  'Scheduled monthly updates',
  3,
  2
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 3
AND NOT EXISTS (
  SELECT 1 FROM assessment_question_options aqo 
  WHERE aqo.question_id = aq.id AND aqo.order_index = 2
);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
  aq.id,
  'Automated updates for most systems',
  2,
  3
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 3
AND NOT EXISTS (
  SELECT 1 FROM assessment_question_options aqo 
  WHERE aqo.question_id = aq.id AND aqo.order_index = 3
);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
  aq.id,
  'Comprehensive automated patch management',
  1,
  4
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 3
AND NOT EXISTS (
  SELECT 1 FROM assessment_question_options aqo 
  WHERE aqo.question_id = aq.id AND aqo.order_index = 4
);

-- Question 4: Backup and recovery
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
  aq.id,
  'No formal backup procedures',
  4,
  1
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 4
AND NOT EXISTS (
  SELECT 1 FROM assessment_question_options aqo 
  WHERE aqo.question_id = aq.id AND aqo.order_index = 1
);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
  aq.id,
  'Basic local backups',
  3,
  2
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 4
AND NOT EXISTS (
  SELECT 1 FROM assessment_question_options aqo 
  WHERE aqo.question_id = aq.id AND aqo.order_index = 2
);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
  aq.id,
  'Regular offsite backups',
  2,
  3
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 4
AND NOT EXISTS (
  SELECT 1 FROM assessment_question_options aqo 
  WHERE aqo.question_id = aq.id AND aqo.order_index = 3
);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
  aq.id,
  'Comprehensive backup with tested recovery',
  1,
  4
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 4
AND NOT EXISTS (
  SELECT 1 FROM assessment_question_options aqo 
  WHERE aqo.question_id = aq.id AND aqo.order_index = 4
);

-- Verification query
SELECT 
  'Cybersecurity Options Status' as status,
  COUNT(*) as total_options
FROM assessment_question_options aqo
JOIN assessment_questions aq ON aqo.question_id = aq.id
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity';
