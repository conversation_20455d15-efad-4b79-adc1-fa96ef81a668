// BlackVeil Security - Email Template Testing Script
// This script tests email personalization with real submission data

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
let supabaseUrl, anonKey;
try {
  const envPath = join(__dirname, '..', '.env.local');
  const envContent = readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  envLines.forEach(line => {
    const [key, value] = line.split('=');
    if (key === 'VITE_SUPABASE_URL') supabaseUrl = value;
    if (key === 'VITE_SUPABASE_ANON_KEY') anonKey = value;
  });
} catch (error) {
  console.error('❌ Could not load .env.local file:', error.message);
}

if (!supabaseUrl || !anonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, anonKey);

// Email templates (same as in Edge Function)
const emailTemplates = {
  HIGH: {
    subject: "Critical Security Gaps Identified - Immediate Action Recommended",
    delay: "Immediate (0 hours)",
    textContent: `Hi {contact_name},

Thank you for completing the {assessment_type} assessment for {company_name}.

CRITICAL SECURITY FINDINGS:
Our analysis has identified several critical security vulnerabilities that require immediate attention. Your organization scored {risk_percentage}% risk level, indicating significant exposure to cyber threats.

KEY SECURITY GAPS:
{top_recommendations_text}

IMMEDIATE ACTIONS RECOMMENDED:
1. Implement Multi-Factor Authentication across all critical systems
2. Conduct Security Awareness Training for all employees  
3. Establish Incident Response Procedures for rapid threat containment
4. Review Access Controls and implement principle of least privilege

COMPLIMENTARY SECURITY CONSULTATION:
I'd like to schedule a brief 15-minute call to discuss these findings and provide specific recommendations tailored to {company_name}'s infrastructure.

Would you be available for a quick security consultation this week?

Best regards,
BlackVeil Security Team
Protecting New Zealand businesses from cyber threats

P.S. We've prepared a detailed security report with actionable steps. Reply to this email to receive your complimentary copy.`
  },

  MEDIUM: {
    subject: "Security Assessment Results & Recommendations for {company_name}",
    delay: "24 hours",
    textContent: `Hi {contact_name},

Thank you for taking the time to complete our {assessment_type} assessment.

YOUR SECURITY SCORE: {risk_percentage}% Risk Level

Your results show {company_name} has a moderate security posture with some areas for improvement. While not critical, addressing these gaps will significantly strengthen your defenses.

KEY AREAS FOR IMPROVEMENT:
{top_recommendations_text}

INDUSTRY INSIGHTS:
Based on our analysis of {industry} organizations, companies that address these areas typically see:
- 60% reduction in security incidents
- Improved compliance posture  
- Enhanced customer trust and reputation
- Reduced cyber insurance premiums

INDUSTRY SECURITY GUIDE:
I've prepared some {industry}-specific security best practices that might be valuable for {company_name}. Would you like me to send them over?

Best regards,
BlackVeil Security Team
Helping {industry} organizations strengthen their security`
  },

  LOW: {
    subject: "Great Security Foundation - Enhancement Opportunities for {company_name}",
    delay: "72 hours",
    textContent: `Hi {contact_name},

CONGRATULATIONS!
{company_name} demonstrates a strong security foundation with a {risk_percentage}% risk level.

Your proactive approach to cybersecurity puts you ahead of many organizations in the {industry} sector.

YOUR SECURITY STRENGTHS:
- Strong foundational security practices in place
- Good awareness of security principles
- Proactive approach to risk assessment
- Commitment to continuous improvement

ENHANCEMENT OPPORTUNITIES:
While your current security posture is solid, there are always opportunities to enhance your defenses and stay ahead of evolving threats:

{top_recommendations_text}

STAYING AHEAD OF THREATS:
The cybersecurity landscape evolves rapidly. I'll be sharing monthly security insights and emerging threat updates that might be valuable for {company_name}.

These updates include:
- Latest threat intelligence and attack trends
- New security technologies and best practices
- Industry-specific security recommendations
- Compliance updates and regulatory changes

Would you like to receive our monthly security newsletter with the latest threat intelligence and best practices?

Best regards,
BlackVeil Security Team
Supporting security-conscious {industry} organizations`
  }
};

function personalizeEmailTemplate(template, submissionData, leadScoreData) {
  // Format recommendations for text
  const recommendationsText = leadScoreData.top_recommendations
    ? leadScoreData.top_recommendations.map((rec, index) => `${index + 1}. ${rec}`).join('\n')
    : 'No specific recommendations available';

  // Replace placeholders
  const replacements = {
    '{contact_name}': submissionData.contact_name || 'there',
    '{company_name}': submissionData.company_name,
    '{email}': submissionData.email,
    '{industry}': submissionData.industry,
    '{employee_count}': submissionData.employee_count,
    '{assessment_type}': submissionData.assessment_type,
    '{risk_percentage}': leadScoreData.risk_percentage?.toString() || '0',
    '{risk_level}': leadScoreData.risk_level,
    '{lead_priority}': leadScoreData.lead_priority,
    '{total_risk_score}': leadScoreData.total_risk_score?.toString() || '0',
    '{top_recommendations_text}': recommendationsText
  };

  let personalizedSubject = template.subject;
  let personalizedContent = template.textContent;

  // Apply all replacements
  Object.entries(replacements).forEach(([placeholder, value]) => {
    const regex = new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g');
    personalizedSubject = personalizedSubject.replace(regex, value);
    personalizedContent = personalizedContent.replace(regex, value);
  });

  return {
    subject: personalizedSubject,
    content: personalizedContent,
    delay: template.delay
  };
}

async function testEmailTemplates() {
  console.log('📧 BlackVeil Security - Email Template Testing');
  console.log('🧪 Testing personalized email templates with real submission data');
  console.log('=' .repeat(70));

  try {
    // Get real submissions with lead scores
    const { data: submissions, error: submissionsError } = await supabase
      .from('assessment_submissions')
      .select(`
        id,
        company_name,
        contact_name,
        email,
        industry,
        employee_count,
        status,
        created_at,
        assessment_types (
          title
        ),
        lead_scores (
          risk_level,
          risk_percentage,
          lead_priority,
          total_risk_score,
          top_recommendations
        )
      `)
      .eq('status', 'completed');

    if (submissionsError) {
      throw new Error(`Failed to fetch submissions: ${submissionsError.message}`);
    }

    if (!submissions || submissions.length === 0) {
      console.log('❌ No completed submissions found for testing');
      return false;
    }

    console.log(`\n📊 Found ${submissions.length} completed submissions for testing`);

    // Test email templates for each submission
    for (let i = 0; i < submissions.length; i++) {
      const submission = submissions[i];
      const leadScore = submission.lead_scores;
      
      if (!leadScore) {
        console.log(`⚠️ No lead score found for ${submission.company_name} - skipping`);
        continue;
      }

      console.log(`\n${'='.repeat(60)}`);
      console.log(`📧 EMAIL TEMPLATE TEST ${i + 1}: ${submission.company_name}`);
      console.log(`${'='.repeat(60)}`);
      
      console.log('📋 Submission Data:');
      console.log(`   Company: ${submission.company_name}`);
      console.log(`   Contact: ${submission.contact_name}`);
      console.log(`   Email: ${submission.email}`);
      console.log(`   Industry: ${submission.industry}`);
      console.log(`   Assessment: ${submission.assessment_types?.title || 'Unknown'}`);
      
      console.log('\n🎯 Lead Score Data:');
      console.log(`   Risk Level: ${leadScore.risk_level}`);
      console.log(`   Risk Percentage: ${leadScore.risk_percentage}%`);
      console.log(`   Lead Priority: ${leadScore.lead_priority}`);
      console.log(`   Total Risk Score: ${leadScore.total_risk_score}`);
      console.log(`   Recommendations: ${leadScore.top_recommendations?.length || 0} items`);

      // Get appropriate email template
      const template = emailTemplates[leadScore.risk_level];
      if (!template) {
        console.log(`❌ No template found for risk level: ${leadScore.risk_level}`);
        continue;
      }

      // Personalize the email
      const personalizedEmail = personalizeEmailTemplate(
        template,
        {
          company_name: submission.company_name,
          contact_name: submission.contact_name,
          email: submission.email,
          industry: submission.industry,
          employee_count: submission.employee_count,
          assessment_type: submission.assessment_types?.title || 'Security Assessment'
        },
        leadScore
      );

      console.log('\n📧 PERSONALIZED EMAIL:');
      console.log(`   Template: ${leadScore.risk_level} Priority`);
      console.log(`   Delay: ${personalizedEmail.delay}`);
      console.log(`   Subject: ${personalizedEmail.subject}`);
      
      console.log('\n📝 Email Content:');
      console.log('-'.repeat(50));
      console.log(personalizedEmail.content);
      console.log('-'.repeat(50));

      // Analyze email characteristics
      console.log('\n📊 Email Analysis:');
      const wordCount = personalizedEmail.content.split(' ').length;
      const hasPersonalization = personalizedEmail.content.includes(submission.company_name);
      const hasRecommendations = personalizedEmail.content.includes('1.') || personalizedEmail.content.includes('•');
      const isConsultative = !personalizedEmail.content.toLowerCase().includes('buy') && 
                            !personalizedEmail.content.toLowerCase().includes('purchase') &&
                            !personalizedEmail.content.toLowerCase().includes('sales');

      console.log(`   Word count: ${wordCount}`);
      console.log(`   Personalized: ${hasPersonalization ? '✅' : '❌'}`);
      console.log(`   Has recommendations: ${hasRecommendations ? '✅' : '❌'}`);
      console.log(`   Consultative tone: ${isConsultative ? '✅' : '❌'}`);
      
      // Determine email effectiveness score
      let effectivenessScore = 0;
      if (hasPersonalization) effectivenessScore += 25;
      if (hasRecommendations) effectivenessScore += 25;
      if (isConsultative) effectivenessScore += 25;
      if (wordCount >= 100 && wordCount <= 500) effectivenessScore += 25;

      console.log(`   Effectiveness score: ${effectivenessScore}/100`);
      
      if (effectivenessScore >= 75) {
        console.log('   🎉 Email quality: Excellent');
      } else if (effectivenessScore >= 50) {
        console.log('   ✅ Email quality: Good');
      } else {
        console.log('   ⚠️ Email quality: Needs improvement');
      }
    }

    // Summary
    console.log(`\n${'='.repeat(60)}`);
    console.log('📊 EMAIL TEMPLATE TESTING SUMMARY');
    console.log(`${'='.repeat(60)}`);
    
    const riskLevelCounts = submissions.reduce((acc, sub) => {
      const riskLevel = sub.lead_scores?.risk_level || 'UNKNOWN';
      acc[riskLevel] = (acc[riskLevel] || 0) + 1;
      return acc;
    }, {});

    console.log('📈 Risk Level Distribution:');
    Object.entries(riskLevelCounts).forEach(([level, count]) => {
      const template = emailTemplates[level];
      if (template) {
        console.log(`   ${level}: ${count} submissions (${template.delay} delay)`);
      } else {
        console.log(`   ${level}: ${count} submissions (no template)`);
      }
    });

    console.log('\n✅ Email Template Testing Completed Successfully!');
    console.log('📧 All templates maintain consultative, value-driven approach');
    console.log('🎯 Personalization working correctly with real data');
    console.log('⏰ Appropriate delays configured based on risk levels');

    return true;

  } catch (error) {
    console.error('❌ Email template testing failed:', error.message);
    return false;
  }
}

// Run testing if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testEmailTemplates().then((success) => {
    if (success) {
      console.log('\n🎉 Email templates ready for production use!');
      console.log('📧 Professional, consultative emails will build trust with leads');
    } else {
      console.log('\n❌ Email template testing failed');
    }
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ Script execution failed:', error);
    process.exit(1);
  });
}

export { testEmailTemplates, personalizeEmailTemplate, emailTemplates };
