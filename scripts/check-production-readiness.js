/**
 * Assessment Platform Readiness Check
 * Verifies that all major placeholder content has been replaced with dynamic systems
 */

const fs = require('fs');
const path = require('path');

console.log('🛡️  BlackVeil Security Assessment Platform - Readiness Check\n');

// Files to check for production readiness
const checkFiles = [
  {
    path: 'supabase/functions/send-assessment-results/index.ts',
    name: 'Email Function',
    criticalChecks: [
      {
        description: 'Dynamic Risk Reduction',
        check: (content) => content.includes('calculateRiskReduction(leadScore)') && !content.includes('82%'),
        status: null
      },
      {
        description: 'Dynamic Recommendations',
        check: (content) => content.includes('generateDynamicRecommendations') && !content.includes('Implement comprehensive phishing training'), // old hardcoded rec
        status: null
      },
      {
        description: 'Proper Error Handling',
        check: (content) => content.includes('status: 202') && !content.includes('totalRiskScore: 35'), // old fallback
        status: null
      },
      {
        description: 'Fixed Phone Number',
        check: (content) => content.includes('0508-HACKED') && !content.includes('+64 XXX XXX XXX'),
        status: null
      }
    ]
  },
  {
    path: 'src/components/assessment/AssessmentResults.tsx',
    name: 'Results Component',
    criticalChecks: [
      {
        description: 'Dynamic Risk Display',
        check: (content) => content.includes('Math.min(85, Math.round(leadScore.risk_percentage * 0.8))') && !content.includes('Up to 70%'),
        status: null
      },
      {
        description: 'Smart Control Stats',
        check: (content) => content.includes('getControlStats') || content.includes('leadScore.risk_percentage'),
        status: null
      }
    ]
  }
];

// Assessment flow completeness checks
const assessmentFlowChecks = [
  {
    description: 'Assessment Question Hook',
    check: () => fs.existsSync('src/hooks/use-assessment-by-slug.ts'),
    status: null
  },
  {
    description: 'Dynamic Assessment Submission',
    check: () => fs.existsSync('src/hooks/use-dynamic-assessment-submission.ts'),
    status: null
  },
  {
    description: 'Assessment Questions Component',
    check: () => fs.existsSync('src/components/assessment/AssessmentQuestions.tsx'),
    status: null
  },
  {
    description: 'Assessment Results Component',
    check: () => fs.existsSync('src/components/assessment/AssessmentResults.tsx'),
    status: null
  }
];

function runChecks() {
  let totalChecks = 0;
  let passedChecks = 0;
  let criticalFailures = [];

  console.log('📋 CRITICAL SYSTEM CHECKS\n');

  // Check each file
  checkFiles.forEach(file => {
    console.log(`🔍 Checking ${file.name}...`);
    
    const filePath = path.join(process.cwd(), file.path);
    
    if (!fs.existsSync(filePath)) {
      console.log(`   ❌ File not found: ${file.path}`);
      criticalFailures.push(`Missing file: ${file.path}`);
      return;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    
    file.criticalChecks.forEach(check => {
      totalChecks++;
      const passed = check.check(content);
      
      if (passed) {
        console.log(`   ✅ ${check.description}`);
        passedChecks++;
      } else {
        console.log(`   ❌ ${check.description} - FAILED`);
        criticalFailures.push(`${file.name}: ${check.description}`);
      }
    });
    
    console.log('');
  });

  // Check assessment flow
  console.log('🔄 ASSESSMENT FLOW CHECKS\n');
  
  assessmentFlowChecks.forEach(check => {
    totalChecks++;
    const passed = check.check();
    
    if (passed) {
      console.log(`✅ ${check.description}`);
      passedChecks++;
    } else {
      console.log(`❌ ${check.description} - MISSING`);
      criticalFailures.push(`Assessment Flow: ${check.description}`);
    }
  });

  // Summary
  console.log('\n📊 READINESS SUMMARY');
  console.log('='.repeat(50));
  
  const passRate = (passedChecks / totalChecks * 100).toFixed(1);
  console.log(`Status: ${passedChecks}/${totalChecks} checks passed (${passRate}%)`);
  
  if (criticalFailures.length === 0) {
    console.log('🎉 All critical checks PASSED! System ready for production.');
  } else {
    console.log(`⚠️  ${criticalFailures.length} critical issue(s) found:`);
    criticalFailures.forEach(failure => {
      console.log(`   • ${failure}`);
    });
  }

  // Next steps
  console.log('\n💡 NEXT STEPS:');
  if (criticalFailures.length === 0) {
    console.log('✅ Code Quality: All placeholder content successfully replaced');
    console.log('📝 Next: Database content verification needed');
    console.log('   • Verify assessment questions are populated');
    console.log('   • Verify question options with risk scores');
    console.log('   • Test end-to-end assessment flow');
    console.log('   • Test email generation with real data');
  } else {
    console.log('🔧 Fix the critical issues listed above');
    console.log('🔄 Re-run this check after fixes');
  }

  return criticalFailures.length === 0;
}

// Run the checks
const allPassed = runChecks();
process.exit(allPassed ? 0 : 1);
