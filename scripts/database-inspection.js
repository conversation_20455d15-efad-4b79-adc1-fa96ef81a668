// Database Inspection Script for BlackVeil Security BI Dashboard
// This script checks the current state of all BI-related tables

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env.local file
let supabaseUrl, supabaseKey;
try {
  const envPath = join(__dirname, '..', '.env.local');
  const envContent = readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  envLines.forEach(line => {
    const [key, value] = line.split('=');
    if (key === 'VITE_SUPABASE_URL') supabaseUrl = value;
    if (key === 'VITE_SUPABASE_ANON_KEY') supabaseKey = value;
  });
} catch (error) {
  console.error('❌ Could not load .env.local file:', error.message);
}

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function inspectDatabase() {
  console.log('🔍 BlackVeil Security - Database Inspection Report');
  console.log('=' .repeat(60));
  
  try {
    // 1. Check Assessment Types
    console.log('\n📋 Assessment Types:');
    const { data: assessmentTypes, error: typesError } = await supabase
      .from('assessment_types')
      .select('id, slug, title, is_active');
    
    if (typesError) {
      console.error('❌ Error fetching assessment types:', typesError.message);
    } else {
      console.log(`✅ Found ${assessmentTypes?.length || 0} assessment types`);
      assessmentTypes?.forEach(type => {
        console.log(`   - ${type.title} (${type.slug}) - ${type.is_active ? 'Active' : 'Inactive'}`);
      });
    }

    // 2. Check Assessment Questions
    console.log('\n❓ Assessment Questions:');
    const { data: questions, error: questionsError } = await supabase
      .from('assessment_questions')
      .select('id, assessment_type_id, question_text, category, order_index, is_active');
    
    if (questionsError) {
      console.error('❌ Error fetching questions:', questionsError.message);
    } else {
      console.log(`✅ Found ${questions?.length || 0} assessment questions`);
      
      // Group by assessment type
      const questionsByType = questions?.reduce((acc, q) => {
        if (!acc[q.assessment_type_id]) acc[q.assessment_type_id] = [];
        acc[q.assessment_type_id].push(q);
        return acc;
      }, {}) || {};
      
      Object.entries(questionsByType).forEach(([typeId, typeQuestions]) => {
        const type = assessmentTypes?.find(t => t.id === typeId);
        console.log(`   ${type?.title || 'Unknown'}: ${typeQuestions.length} questions`);
      });
    }

    // 3. Check Question Options
    console.log('\n⚙️ Question Options:');
    const { data: options, error: optionsError } = await supabase
      .from('assessment_question_options')
      .select('id, question_id, option_text, risk_score, order_index');
    
    if (optionsError) {
      console.error('❌ Error fetching options:', optionsError.message);
    } else {
      console.log(`✅ Found ${options?.length || 0} question options`);
      
      // Check options per question
      const optionsByQuestion = options?.reduce((acc, o) => {
        if (!acc[o.question_id]) acc[o.question_id] = [];
        acc[o.question_id].push(o);
        return acc;
      }, {}) || {};
      
      const questionsWithOptions = Object.keys(optionsByQuestion).length;
      const totalQuestions = questions?.length || 0;
      console.log(`   Questions with options: ${questionsWithOptions}/${totalQuestions}`);
    }

    // 4. Check Assessment Submissions
    console.log('\n📝 Assessment Submissions:');
    const { data: submissions, error: submissionsError } = await supabase
      .from('assessment_submissions')
      .select('id, company_name, status, industry, employee_count, created_at');
    
    if (submissionsError) {
      console.error('❌ Error fetching submissions:', submissionsError.message);
    } else {
      console.log(`✅ Found ${submissions?.length || 0} assessment submissions`);
      
      const statusCounts = submissions?.reduce((acc, s) => {
        acc[s.status] = (acc[s.status] || 0) + 1;
        return acc;
      }, {}) || {};
      
      Object.entries(statusCounts).forEach(([status, count]) => {
        console.log(`   ${status}: ${count}`);
      });
      
      // Check for sample data
      const sampleSubmissions = submissions?.filter(s => s.company_name.includes('Sample Company')) || [];
      console.log(`   Sample data: ${sampleSubmissions.length} submissions`);
    }

    // 5. Check Lead Scores
    console.log('\n🎯 Lead Scores:');
    const { data: leadScores, error: scoresError } = await supabase
      .from('lead_scores')
      .select('id, submission_id, risk_level, risk_percentage, lead_priority, total_risk_score, max_possible_score');
    
    if (scoresError) {
      console.error('❌ Error fetching lead scores:', scoresError.message);
    } else {
      console.log(`✅ Found ${leadScores?.length || 0} lead scores`);
      
      const riskCounts = leadScores?.reduce((acc, s) => {
        acc[s.risk_level] = (acc[s.risk_level] || 0) + 1;
        return acc;
      }, {}) || {};
      
      Object.entries(riskCounts).forEach(([risk, count]) => {
        console.log(`   ${risk} risk: ${count}`);
      });
    }

    // 6. Check Enhanced Lead Scores
    console.log('\n🚀 Enhanced Lead Scores:');
    const { count: enhancedCount, error: enhancedError } = await supabase
      .from('enhanced_lead_scores')
      .select('*', { count: 'exact', head: true });

    if (enhancedError) {
      console.error('❌ Error fetching enhanced scores:', enhancedError.message);
    } else {
      console.log(`✅ Found ${enhancedCount || 0} enhanced lead scores`);
    }

    // 7. Check User Journey Events
    console.log('\n🛤️ User Journey Events:');
    const { count: journeyCount, error: journeyError } = await supabase
      .from('user_journey_events')
      .select('*', { count: 'exact', head: true });

    if (journeyError) {
      console.error('❌ Error fetching journey events:', journeyError.message);
    } else {
      console.log(`✅ Found ${journeyCount || 0} journey events`);

      // If there are events, get a sample to show event types
      if (journeyCount > 0) {
        const { data: sampleEvents } = await supabase
          .from('user_journey_events')
          .select('event_type')
          .limit(100);

        const eventCounts = sampleEvents?.reduce((acc, e) => {
          acc[e.event_type] = (acc[e.event_type] || 0) + 1;
          return acc;
        }, {}) || {};

        Object.entries(eventCounts).forEach(([event, count]) => {
          console.log(`   ${event}: ${count}`);
        });
      }
    }

    // 8. Check A/B Tests
    console.log('\n🧪 A/B Tests:');
    const { count: abTestCount, error: abError } = await supabase
      .from('ab_tests')
      .select('*', { count: 'exact', head: true });

    if (abError) {
      console.error('❌ Error fetching A/B tests:', abError.message);
    } else {
      console.log(`✅ Found ${abTestCount || 0} A/B tests`);

      // If there are tests, get a sample to show test types
      if (abTestCount > 0) {
        const { data: sampleTests } = await supabase
          .from('ab_tests')
          .select('name, test_type, is_active')
          .limit(10);

        sampleTests?.forEach(test => {
          console.log(`   - ${test.name} (${test.test_type}) - ${test.is_active ? 'Active' : 'Inactive'}`);
        });
      }
    }

    // 9. Data Integrity Check
    console.log('\n🔍 Data Integrity Check:');
    const completedSubmissions = submissions?.filter(s => s.status === 'completed') || [];
    const submissionsWithScores = leadScores?.length || 0;
    
    console.log(`   Completed submissions: ${completedSubmissions.length}`);
    console.log(`   Submissions with lead scores: ${submissionsWithScores}`);
    console.log(`   Score coverage: ${completedSubmissions.length > 0 ? Math.round((submissionsWithScores / completedSubmissions.length) * 100) : 0}%`);

    // 10. BI Dashboard Readiness
    console.log('\n📊 BI Dashboard Readiness:');
    const hasQuestions = (questions?.length || 0) > 0;
    const hasOptions = (options?.length || 0) > 0;
    const hasSubmissions = (submissions?.length || 0) > 0;
    const hasLeadScores = (leadScores?.length || 0) > 0;
    
    console.log(`   ✅ Assessment Questions: ${hasQuestions ? 'Ready' : '❌ Missing'}`);
    console.log(`   ✅ Question Options: ${hasOptions ? 'Ready' : '❌ Missing'}`);
    console.log(`   ✅ Assessment Data: ${hasSubmissions ? 'Ready' : '❌ Missing'}`);
    console.log(`   ✅ Lead Scoring: ${hasLeadScores ? 'Ready' : '❌ Missing'}`);
    
    const dashboardReady = hasQuestions && hasOptions && hasSubmissions && hasLeadScores;
    console.log(`\n🎯 Overall Status: ${dashboardReady ? '✅ BI Dashboard Ready' : '❌ Requires Data Population'}`);

  } catch (error) {
    console.error('❌ Database inspection failed:', error.message);
  }
}

// Run the inspection
inspectDatabase().then(() => {
  console.log('\n✅ Database inspection completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Inspection failed:', error);
  process.exit(1);
});
