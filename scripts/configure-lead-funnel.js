// Production Lead Funnel Configuration Script
// This script sets up automated email sequences and lead nurturing for BlackVeil Security

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
let supabaseUrl;
try {
  const envPath = join(__dirname, '..', '.env.local');
  const envContent = readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  envLines.forEach(line => {
    const [key, value] = line.split('=');
    if (key === 'VITE_SUPABASE_URL') supabaseUrl = value;
  });
} catch (error) {
  console.error('❌ Could not load .env.local file:', error.message);
}

if (!supabaseUrl) {
  console.error('❌ Missing Supabase URL');
  process.exit(1);
}

// Service role key for admin operations
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODU2NjQ1NCwiZXhwIjoyMDY0MTQyNDU0fQ.j1sjkEHL2LR78ChzOL6MdieomYgSk1CNXXR0n5sq9eo';

const supabase = createClient(supabaseUrl, serviceKey);

// Lead scoring thresholds for automated follow-up
const leadScoringConfig = {
  HIGH_PRIORITY: {
    riskPercentage: 70,
    followUpDelay: 0, // Immediate
    emailSequence: 'high_priority',
    description: 'Immediate follow-up for high-risk organizations'
  },
  MEDIUM_PRIORITY: {
    riskPercentage: 40,
    followUpDelay: 24, // 24 hours
    emailSequence: 'medium_priority',
    description: 'Next-day follow-up for medium-risk organizations'
  },
  LOW_PRIORITY: {
    riskPercentage: 0,
    followUpDelay: 72, // 3 days
    emailSequence: 'low_priority',
    description: 'Educational follow-up for low-risk organizations'
  }
};

// Professional email templates (consultative, not sales-focused)
const emailTemplates = {
  high_priority: {
    subject: 'Critical Security Gaps Identified - Immediate Action Recommended',
    template: `Hi {contact_name},

Thank you for completing the {assessment_type} assessment for {company_name}.

Our analysis has identified several critical security vulnerabilities that require immediate attention. Your organization scored {risk_percentage}% risk level, indicating significant exposure to cyber threats.

**Key Findings:**
{top_recommendations}

**Immediate Actions Recommended:**
1. Review and implement multi-factor authentication across all systems
2. Conduct employee security awareness training
3. Establish incident response procedures

I'd like to schedule a brief 15-minute call to discuss these findings and provide specific recommendations tailored to {company_name}'s infrastructure.

Would you be available for a quick security consultation this week?

Best regards,
BlackVeil Security Team

P.S. We've prepared a detailed security report with actionable steps. Reply to this email to receive your complimentary copy.`,
    cta: 'Schedule Security Consultation',
    followUpDays: [3, 7, 14]
  },
  
  medium_priority: {
    subject: 'Security Assessment Results & Recommendations for {company_name}',
    template: `Hi {contact_name},

Thank you for taking the time to complete our {assessment_type} assessment.

Your results show {company_name} has a moderate security posture with some areas for improvement. While not critical, addressing these gaps will significantly strengthen your defenses.

**Your Security Score: {risk_percentage}% Risk Level**

**Key Areas for Improvement:**
{top_recommendations}

**Industry Insights:**
Based on our analysis of {industry} organizations, companies that address these areas typically see:
- 60% reduction in security incidents
- Improved compliance posture
- Enhanced customer trust

I've prepared some industry-specific security best practices that might be valuable for {company_name}. Would you like me to send them over?

Best regards,
BlackVeil Security Team`,
    cta: 'Get Industry Security Guide',
    followUpDays: [5, 14]
  },

  low_priority: {
    subject: 'Great Security Foundation - Enhancement Opportunities for {company_name}',
    template: `Hi {contact_name},

Congratulations! {company_name} demonstrates a strong security foundation with a {risk_percentage}% risk level.

While your current security posture is solid, there are always opportunities to enhance your defenses and stay ahead of evolving threats.

**Your Strengths:**
- Strong foundational security practices
- Good awareness of security principles
- Proactive approach to risk assessment

**Enhancement Opportunities:**
{top_recommendations}

**Staying Ahead:**
The cybersecurity landscape evolves rapidly. I'll be sharing monthly security insights and emerging threat updates that might be valuable for {company_name}.

Would you like to receive our monthly security newsletter with the latest threat intelligence and best practices?

Best regards,
BlackVeil Security Team`,
    cta: 'Subscribe to Security Insights',
    followUpDays: [7, 30]
  }
};

// A/B test configurations for CTA optimization
const abTestConfigs = [
  {
    name: 'Email CTA Button Test',
    description: 'Testing consultative vs action-oriented CTAs in follow-up emails',
    test_type: 'email_template',
    hypothesis: 'Consultative CTAs will have higher engagement than direct action CTAs',
    success_metric: 'email_click_rate',
    variants: [
      {
        name: 'Control - Direct Action',
        config: { cta_text: 'Schedule Security Review', cta_style: 'action' },
        traffic_percentage: 50,
        is_control: true
      },
      {
        name: 'Variant - Consultative',
        config: { cta_text: 'Discuss Your Results', cta_style: 'consultative' },
        traffic_percentage: 50,
        is_control: false
      }
    ]
  },
  {
    name: 'Assessment Completion CTA Test',
    description: 'Testing different completion page CTAs for lead conversion',
    test_type: 'cta_button',
    hypothesis: 'Value-focused CTAs will convert better than meeting-focused CTAs',
    success_metric: 'contact_form_completion',
    variants: [
      {
        name: 'Control - Meeting Focus',
        config: { cta_text: 'Schedule Consultation', cta_color: 'blue' },
        traffic_percentage: 50,
        is_control: true
      },
      {
        name: 'Variant - Value Focus',
        config: { cta_text: 'Get Detailed Report', cta_color: 'green' },
        traffic_percentage: 50,
        is_control: false
      }
    ]
  }
];

async function setupLeadFunnel() {
  console.log('🎯 BlackVeil Security - Lead Funnel Configuration');
  console.log('📧 Setting up automated email sequences and lead nurturing');
  console.log('=' .repeat(60));

  try {
    // Step 1: Create A/B tests for CTA optimization
    console.log('\n🧪 Step 1: Setting up A/B tests for CTA optimization...');
    
    for (const testConfig of abTestConfigs) {
      console.log(`   📋 Creating test: ${testConfig.name}`);
      
      // Create A/B test
      const { data: testData, error: testError } = await supabase
        .from('ab_tests')
        .upsert({
          name: testConfig.name,
          description: testConfig.description,
          test_type: testConfig.test_type,
          hypothesis: testConfig.hypothesis,
          success_metric: testConfig.success_metric,
          is_active: true
        }, { onConflict: 'name' })
        .select()
        .single();

      if (testError) {
        console.warn(`   ⚠️ Warning creating test: ${testError.message}`);
        continue;
      }

      // Create test variants
      for (const variant of testConfig.variants) {
        const { error: variantError } = await supabase
          .from('ab_test_variants')
          .upsert({
            test_id: testData.id,
            name: variant.name,
            description: variant.name,
            config: variant.config,
            traffic_percentage: variant.traffic_percentage,
            is_control: variant.is_control
          }, { onConflict: 'test_id,name' });

        if (variantError) {
          console.warn(`   ⚠️ Warning creating variant: ${variantError.message}`);
        }
      }

      console.log(`   ✅ Test created: ${testConfig.name}`);
    }

    // Step 2: Generate enhanced lead scores for existing submissions
    console.log('\n🎯 Step 2: Generating enhanced lead scores...');
    
    const { data: submissions, error: submissionsError } = await supabase
      .from('assessment_submissions')
      .select(`
        id,
        company_name,
        contact_name,
        email,
        industry,
        employee_count,
        lead_scores (
          risk_level,
          risk_percentage,
          lead_priority,
          total_risk_score,
          top_recommendations
        )
      `)
      .eq('status', 'completed');

    if (submissionsError) {
      throw new Error(`Failed to fetch submissions: ${submissionsError.message}`);
    }

    for (const submission of submissions) {
      if (!submission.lead_scores) continue;

      const leadScore = submission.lead_scores;
      const riskPercentage = leadScore.risk_percentage || 0;
      
      // Calculate enhanced scores
      const enhancedScore = {
        submission_id: submission.id,
        completion_score: 100, // All are completed
        industry_score: calculateIndustryScore(submission.industry),
        size_score: calculateSizeScore(submission.employee_count),
        engagement_score: 85, // Base engagement score
        urgency_score: Math.min(riskPercentage + 10, 100),
        total_score: Math.min(riskPercentage * 4 + 100, 500),
        conversion_probability: calculateConversionProbability(riskPercentage, submission.industry),
        priority_level: determinePriorityLevel(riskPercentage),
        recommended_actions: {
          immediate: generateImmediateActions(leadScore.risk_level),
          follow_up: generateFollowUpActions(submission.industry),
          timeline: determineTimeline(riskPercentage)
        }
      };

      const { error: enhancedError } = await supabase
        .from('enhanced_lead_scores')
        .upsert(enhancedScore, { onConflict: 'submission_id' });

      if (enhancedError) {
        console.warn(`   ⚠️ Warning creating enhanced score: ${enhancedError.message}`);
      } else {
        console.log(`   ✅ Enhanced score created for ${submission.company_name}`);
      }
    }

    // Step 3: Create sample user journey events for existing submissions
    console.log('\n🛤️ Step 3: Creating user journey tracking...');
    
    for (const submission of submissions) {
      const journeyEvents = [
        {
          event_type: 'assessment_complete',
          session_id: `session_${submission.id.slice(0, 8)}`,
          submission_id: submission.id,
          page_url: '/assessment/results',
          event_data: {
            assessment_type: 'completed',
            risk_level: submission.lead_scores?.risk_level,
            completion_time: 300 // 5 minutes average
          }
        },
        {
          event_type: 'result_view',
          session_id: `session_${submission.id.slice(0, 8)}`,
          submission_id: submission.id,
          page_url: '/assessment/results',
          event_data: {
            time_on_results: 120,
            downloaded_report: false
          }
        }
      ];

      for (const event of journeyEvents) {
        const { error: eventError } = await supabase
          .from('user_journey_events')
          .insert(event);

        if (eventError) {
          console.warn(`   ⚠️ Warning creating journey event: ${eventError.message}`);
        }
      }
    }

    console.log('   ✅ User journey tracking configured');

    return {
      success: true,
      testsCreated: abTestConfigs.length,
      enhancedScoresCreated: submissions.length,
      journeyEventsCreated: submissions.length * 2
    };

  } catch (error) {
    console.error('❌ Lead funnel configuration failed:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// Helper functions for enhanced lead scoring
function calculateIndustryScore(industry) {
  const industryRiskMap = {
    'finance': 90,
    'healthcare': 85,
    'government': 80,
    'technology': 75,
    'professional-services': 70,
    'manufacturing': 65,
    'retail': 60,
    'education': 55
  };
  return industryRiskMap[industry?.toLowerCase()] || 70;
}

function calculateSizeScore(employeeCount) {
  const sizeScoreMap = {
    '1-10': 60,
    '11-50': 70,
    '51-200': 80,
    '200+': 90
  };
  return sizeScoreMap[employeeCount] || 70;
}

function calculateConversionProbability(riskPercentage, industry) {
  let baseProbability = riskPercentage * 0.8; // Base on risk
  
  // Industry adjustments
  const industryMultipliers = {
    'finance': 1.2,
    'healthcare': 1.15,
    'government': 1.1,
    'technology': 1.0,
    'professional-services': 0.9,
    'manufacturing': 0.85,
    'retail': 0.8,
    'education': 0.75
  };
  
  const multiplier = industryMultipliers[industry?.toLowerCase()] || 1.0;
  return Math.min(baseProbability * multiplier, 95);
}

function determinePriorityLevel(riskPercentage) {
  if (riskPercentage >= 70) return 'URGENT';
  if (riskPercentage >= 50) return 'HIGH';
  if (riskPercentage >= 30) return 'MEDIUM';
  return 'LOW';
}

function generateImmediateActions(riskLevel) {
  const actionMap = {
    'HIGH': ['Implement MFA immediately', 'Conduct security audit', 'Review access controls'],
    'MEDIUM': ['Update security policies', 'Employee training', 'Backup verification'],
    'LOW': ['Security awareness review', 'Policy documentation', 'Regular updates']
  };
  return actionMap[riskLevel] || actionMap['MEDIUM'];
}

function generateFollowUpActions(industry) {
  const industryActions = {
    'finance': ['Compliance review', 'PCI DSS assessment'],
    'healthcare': ['HIPAA compliance check', 'Patient data security'],
    'technology': ['Code security review', 'API security assessment'],
    'default': ['Regular security reviews', 'Incident response planning']
  };
  return industryActions[industry?.toLowerCase()] || industryActions['default'];
}

function determineTimeline(riskPercentage) {
  if (riskPercentage >= 70) return '1-2 weeks';
  if (riskPercentage >= 50) return '2-4 weeks';
  if (riskPercentage >= 30) return '1-2 months';
  return '2-3 months';
}

// Main execution
async function executeLeadFunnelSetup() {
  console.log('🚀 Starting Lead Funnel Configuration...\n');

  const result = await setupLeadFunnel();
  
  if (result.success) {
    console.log('\n🎉 Lead funnel configuration completed successfully!');
    console.log('📊 Summary:');
    console.log(`   • A/B tests created: ${result.testsCreated}`);
    console.log(`   • Enhanced lead scores: ${result.enhancedScoresCreated}`);
    console.log(`   • Journey events created: ${result.journeyEventsCreated}`);
    
    console.log('\n📋 Production Lead Funnel Features:');
    console.log('   ✅ Automated lead scoring with industry-specific thresholds');
    console.log('   ✅ Progressive nurturing based on risk levels');
    console.log('   ✅ Consultative email templates (not sales-focused)');
    console.log('   ✅ A/B testing for CTA optimization');
    console.log('   ✅ Customer journey tracking');
    console.log('   ✅ Value-driven follow-up sequences');
    
    console.log('\n📧 Email Sequence Configuration:');
    console.log('   🔴 HIGH PRIORITY: Immediate follow-up with security consultation offer');
    console.log('   🟡 MEDIUM PRIORITY: 24-hour follow-up with industry insights');
    console.log('   🟢 LOW PRIORITY: 3-day follow-up with educational content');
    
    console.log('\n📋 Next Steps:');
    console.log('   1. Run: node scripts/database-inspection.js');
    console.log('   2. Test BI dashboard at /admin');
    console.log('   3. Verify A/B testing functionality');
    console.log('   4. Configure email automation triggers');
  } else {
    console.log('\n❌ Lead funnel configuration failed');
    console.log(`Error: ${result.error}`);
  }

  return result.success;
}

// Run setup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  executeLeadFunnelSetup().then((success) => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ Script execution failed:', error);
    process.exit(1);
  });
}

export { executeLeadFunnelSetup, setupLeadFunnel, leadScoringConfig, emailTemplates };
