// Populate Sample Data for Existing Tables
// This script works with the current database schema and populates realistic test data

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
let supabaseUrl, supabaseKey;
try {
  const envPath = join(__dirname, '..', '.env.local');
  const envContent = readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');
  
  envLines.forEach(line => {
    const [key, value] = line.split('=');
    if (key === 'VITE_SUPABASE_URL') supabaseUrl = value;
    if (key === 'VITE_SUPABASE_ANON_KEY') supabaseKey = value;
  });
} catch (error) {
  console.error('❌ Could not load .env.local file:', error.message);
}

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function clearExistingData() {
  console.log('🧹 Clearing existing sample data...');
  
  try {
    // Delete existing sample submissions and related data
    const { error: deleteError } = await supabase
      .from('assessment_submissions')
      .delete()
      .like('company_name', 'Sample Company%');

    if (deleteError && deleteError.code !== 'PGRST116') { // PGRST116 = no rows found
      console.error('❌ Error clearing data:', deleteError);
      return false;
    }

    console.log('✅ Existing sample data cleared');
    return true;
  } catch (error) {
    console.error('❌ Failed to clear data:', error);
    return false;
  }
}

async function populateCompletedAssessments() {
  console.log('📝 Creating completed assessment submissions...');
  
  try {
    // Get cybersecurity assessment type
    const { data: cybersecType } = await supabase
      .from('assessment_types')
      .select('id')
      .eq('slug', 'cybersecurity-maturity')
      .single();

    if (!cybersecType) {
      throw new Error('Cybersecurity assessment type not found');
    }

    const industries = ['Technology', 'Healthcare', 'Finance', 'Manufacturing', 'Professional Services', 'Education', 'Government', 'Retail'];
    const employeeCounts = ['1-10', '11-50', '51-200', '200+'];
    const sampleSubmissions = [];

    // Create 20 sample submissions with varied data
    for (let i = 1; i <= 20; i++) {
      const submissionId = crypto.randomUUID();
      const daysAgo = Math.floor(Math.random() * 30); // Random day in last 30 days
      const createdAt = new Date(Date.now() - (daysAgo * 24 * 60 * 60 * 1000) + (i * 60 * 60 * 1000));
      const completedAt = new Date(createdAt.getTime() + (10 + Math.random() * 20) * 60 * 1000); // 10-30 min duration
      
      const submission = {
        id: submissionId,
        assessment_type_id: cybersecType.id,
        company_name: `Sample Company ${i}`,
        contact_name: `Contact Person ${i}`,
        email: `contact${i}@samplecompany${i}.co.nz`,
        employee_count: employeeCounts[i % employeeCounts.length],
        industry: industries[i % industries.length],
        status: 'completed',
        session_id: crypto.randomUUID(),
        created_at: createdAt.toISOString(),
        started_at: createdAt.toISOString(),
        completed_at: completedAt.toISOString(),
        total_duration_seconds: Math.floor((completedAt - createdAt) / 1000)
      };

      sampleSubmissions.push(submission);
    }

    // Insert submissions in batches
    const batchSize = 5;
    const insertedSubmissions = [];
    
    for (let i = 0; i < sampleSubmissions.length; i += batchSize) {
      const batch = sampleSubmissions.slice(i, i + batchSize);
      const { data: batchResult, error: submissionError } = await supabase
        .from('assessment_submissions')
        .insert(batch)
        .select();

      if (submissionError) throw submissionError;
      insertedSubmissions.push(...batchResult);
      
      console.log(`   ✅ Inserted batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(sampleSubmissions.length/batchSize)}`);
    }

    console.log(`✅ Created ${insertedSubmissions.length} completed assessment submissions`);
    return insertedSubmissions;

  } catch (error) {
    console.error('❌ Error creating submissions:', error);
    return [];
  }
}

async function populateLeadScores(submissions) {
  console.log('🎯 Creating lead scores...');
  
  try {
    const leadScores = submissions.map((submission, index) => {
      // Create varied risk scores
      const baseRisk = 8 + (index % 16); // 8-24 range
      const riskScore = Math.min(32, baseRisk + Math.floor(Math.random() * 4)); // Add some randomness
      const riskPercentage = Math.round((riskScore / 32) * 100);
      
      let riskLevel, leadPriority, followUpUrgency;
      
      if (riskScore <= 12) {
        riskLevel = 'LOW';
        leadPriority = 1;
        followUpUrgency = 'low';
      } else if (riskScore <= 20) {
        riskLevel = 'MEDIUM';
        leadPriority = Math.random() > 0.5 ? 2 : 3;
        followUpUrgency = 'medium';
      } else {
        riskLevel = 'HIGH';
        leadPriority = Math.random() > 0.3 ? 4 : 3;
        followUpUrgency = 'urgent';
      }

      return {
        submission_id: submission.id,
        total_risk_score: riskScore,
        max_possible_score: 32,
        risk_percentage: riskPercentage,
        risk_level: riskLevel,
        lead_priority: leadPriority,
        follow_up_urgency: followUpUrgency,
        top_recommendations: JSON.stringify([
          'Implement multi-factor authentication',
          'Conduct security awareness training',
          'Review and update access controls',
          'Establish incident response procedures'
        ])
      };
    });

    // Insert lead scores in batches
    const batchSize = 5;
    let insertedCount = 0;
    
    for (let i = 0; i < leadScores.length; i += batchSize) {
      const batch = leadScores.slice(i, i + batchSize);
      const { error: scoresError } = await supabase
        .from('lead_scores')
        .insert(batch);

      if (scoresError) throw scoresError;
      insertedCount += batch.length;
      
      console.log(`   ✅ Inserted batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(leadScores.length/batchSize)}`);
    }

    console.log(`✅ Created ${insertedCount} lead scores`);
    return true;

  } catch (error) {
    console.error('❌ Error creating lead scores:', error);
    return false;
  }
}

async function updateExistingSubmissions() {
  console.log('🔄 Updating existing in-progress submissions to completed...');
  
  try {
    // Get existing in-progress submissions
    const { data: inProgressSubmissions } = await supabase
      .from('assessment_submissions')
      .select('id, created_at')
      .eq('status', 'in_progress');

    if (inProgressSubmissions && inProgressSubmissions.length > 0) {
      // Update them to completed
      for (const submission of inProgressSubmissions) {
        const completedAt = new Date(new Date(submission.created_at).getTime() + 15 * 60 * 1000);
        
        const { error: updateError } = await supabase
          .from('assessment_submissions')
          .update({
            status: 'completed',
            completed_at: completedAt.toISOString(),
            total_duration_seconds: 900
          })
          .eq('id', submission.id);

        if (updateError) throw updateError;

        // Create lead score for this submission
        const riskScore = 12 + Math.floor(Math.random() * 8); // 12-20 range
        const riskPercentage = Math.round((riskScore / 32) * 100);
        const riskLevel = riskScore <= 16 ? 'MEDIUM' : 'HIGH';

        const { error: scoreError } = await supabase
          .from('lead_scores')
          .insert({
            submission_id: submission.id,
            total_risk_score: riskScore,
            max_possible_score: 32,
            risk_percentage: riskPercentage,
            risk_level: riskLevel,
            lead_priority: riskLevel === 'HIGH' ? 3 : 2,
            follow_up_urgency: riskLevel === 'HIGH' ? 'high' : 'medium'
          });

        if (scoreError && scoreError.code !== '23505') { // Ignore duplicate key errors
          throw scoreError;
        }
      }

      console.log(`✅ Updated ${inProgressSubmissions.length} existing submissions to completed`);
    } else {
      console.log('ℹ️ No in-progress submissions found to update');
    }

    return true;
  } catch (error) {
    console.error('❌ Error updating existing submissions:', error);
    return false;
  }
}

async function verifyData() {
  console.log('🔍 Verifying populated data...');
  
  try {
    const { count: totalSubmissions } = await supabase
      .from('assessment_submissions')
      .select('*', { count: 'exact', head: true });

    const { count: completedSubmissions } = await supabase
      .from('assessment_submissions')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'completed');

    const { count: leadScores } = await supabase
      .from('lead_scores')
      .select('*', { count: 'exact', head: true });

    // Get risk distribution
    const { data: riskData } = await supabase
      .from('lead_scores')
      .select('risk_level');

    const riskCounts = riskData?.reduce((acc, item) => {
      acc[item.risk_level] = (acc[item.risk_level] || 0) + 1;
      return acc;
    }, {}) || {};

    console.log('📊 Data Verification Results:');
    console.log(`   Total Submissions: ${totalSubmissions}`);
    console.log(`   Completed Submissions: ${completedSubmissions}`);
    console.log(`   Lead Scores: ${leadScores}`);
    console.log(`   Risk Distribution:`, riskCounts);

    const success = completedSubmissions >= 15 && leadScores >= 15;
    console.log(`🎯 Verification: ${success ? '✅ PASSED' : '❌ FAILED'}`);
    
    return success;
  } catch (error) {
    console.error('❌ Verification failed:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 BlackVeil Security - Sample Data Population');
  console.log('=' .repeat(50));
  
  try {
    // Step 1: Clear existing sample data
    const cleared = await clearExistingData();
    if (!cleared) {
      console.error('❌ Failed to clear existing data. Continuing anyway...');
    }

    // Step 2: Update existing in-progress submissions
    const updated = await updateExistingSubmissions();
    if (!updated) {
      console.error('❌ Failed to update existing submissions. Continuing...');
    }

    // Step 3: Create new completed assessments
    const submissions = await populateCompletedAssessments();
    if (submissions.length === 0) {
      console.error('❌ Failed to create submissions. Exiting.');
      process.exit(1);
    }

    // Step 4: Create lead scores
    const scoresCreated = await populateLeadScores(submissions);
    if (!scoresCreated) {
      console.error('❌ Failed to create lead scores. Exiting.');
      process.exit(1);
    }

    // Step 5: Verify everything worked
    const verified = await verifyData();
    if (!verified) {
      console.error('❌ Data verification failed. Check the database.');
      process.exit(1);
    }

    console.log('\n🎉 Sample data population completed successfully!');
    console.log('✅ The BI Dashboard should now show meaningful data.');
    console.log('🔗 Navigate to http://localhost:8080/admin to test the dashboard.');
    console.log('\n📋 Note: Some BI features require additional tables:');
    console.log('   - Enhanced lead scoring requires enhanced_lead_scores table');
    console.log('   - Journey analytics requires user_journey_events table');
    console.log('   - A/B testing requires ab_tests related tables');

  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

// Run the script
main();
