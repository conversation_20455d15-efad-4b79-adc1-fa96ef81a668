// Direct Database Fix and Population Script
// This script creates missing tables and populates sample data directly

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
let supabaseUrl, supabaseKey;
try {
  const envPath = join(__dirname, '..', '.env.local');
  const envContent = readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');
  
  envLines.forEach(line => {
    const [key, value] = line.split('=');
    if (key === 'VITE_SUPABASE_URL') supabaseUrl = value;
    if (key === 'VITE_SUPABASE_ANON_KEY') supabaseKey = value;
  });
} catch (error) {
  console.error('❌ Could not load .env.local file:', error.message);
}

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function createMissingTables() {
  console.log('🔧 Checking and creating missing tables...');

  try {
    // Check if enhanced_lead_scores table exists
    const { data: enhancedExists } = await supabase
      .from('enhanced_lead_scores')
      .select('id')
      .limit(1);

    if (enhancedExists === null) {
      console.log('⚠️ enhanced_lead_scores table missing - this needs to be created in Supabase SQL Editor');
      console.log('📋 Please run the following SQL in Supabase Dashboard:');
      console.log(`
CREATE TABLE enhanced_lead_scores (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    submission_id UUID NOT NULL REFERENCES assessment_submissions(id) ON DELETE CASCADE,
    completion_score NUMERIC NOT NULL,
    industry_score NUMERIC NOT NULL,
    size_score NUMERIC NOT NULL,
    engagement_score NUMERIC NOT NULL,
    urgency_score NUMERIC NOT NULL,
    total_score NUMERIC NOT NULL,
    conversion_probability NUMERIC NOT NULL,
    priority_level TEXT NOT NULL CHECK (priority_level IN ('LOW', 'MEDIUM', 'HIGH', 'URGENT')),
    recommended_actions JSONB,
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(submission_id)
);

CREATE TABLE user_journey_events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event_type TEXT NOT NULL,
    page_url TEXT,
    session_id TEXT NOT NULL,
    user_id UUID,
    submission_id UUID REFERENCES assessment_submissions(id) ON DELETE SET NULL,
    event_data JSONB,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE ab_tests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    test_type TEXT NOT NULL CHECK (test_type IN ('assessment_question', 'email_template', 'cta_button', 'landing_page')),
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    winner_variant UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE ab_test_variants (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    test_id UUID NOT NULL REFERENCES ab_tests(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    config JSONB NOT NULL,
    traffic_percentage NUMERIC NOT NULL CHECK (traffic_percentage >= 0 AND traffic_percentage <= 100),
    participants INTEGER DEFAULT 0,
    conversion_rate NUMERIC
);

CREATE TABLE ab_test_participations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    test_id UUID NOT NULL REFERENCES ab_tests(id) ON DELETE CASCADE,
    variant_id UUID NOT NULL REFERENCES ab_test_variants(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL,
    user_id UUID,
    submission_id UUID REFERENCES assessment_submissions(id) ON DELETE SET NULL,
    converted BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
      `);
      return false;
    } else {
      console.log('✅ enhanced_lead_scores table exists');
    }

    // Check user_journey_events
    const { data: journeyExists } = await supabase
      .from('user_journey_events')
      .select('id')
      .limit(1);

    if (journeyExists === null) {
      console.log('⚠️ user_journey_events table missing');
      return false;
    } else {
      console.log('✅ user_journey_events table exists');
    }

    console.log('✅ All required tables exist');
    return true;
  } catch (error) {
    console.error('❌ Error checking tables:', error);
    return false;
  }
}

async function populateSampleData() {
  console.log('📊 Populating sample data...');
  
  try {
    // Get cybersecurity assessment type
    const { data: cybersecType } = await supabase
      .from('assessment_types')
      .select('id')
      .eq('slug', 'cybersecurity-maturity')
      .single();

    if (!cybersecType) {
      throw new Error('Cybersecurity assessment type not found');
    }

    console.log('📝 Creating sample assessment submissions...');
    
    const industries = ['Technology', 'Healthcare', 'Finance', 'Manufacturing', 'Professional Services'];
    const employeeCounts = ['1-10', '11-50', '51-200', '200+'];
    const sampleSubmissions = [];

    // Create 15 sample submissions
    for (let i = 1; i <= 15; i++) {
      const submissionId = crypto.randomUUID();
      const createdAt = new Date(Date.now() - (7 * 24 * 60 * 60 * 1000) + (i * 60 * 60 * 1000));
      const completedAt = new Date(createdAt.getTime() + 15 * 60 * 1000);
      
      const submission = {
        id: submissionId,
        assessment_type_id: cybersecType.id,
        company_name: `Sample Company ${i}`,
        contact_name: `Contact Person ${i}`,
        email: `contact${i}@samplecompany${i}.co.nz`,
        employee_count: employeeCounts[i % employeeCounts.length],
        industry: industries[i % industries.length],
        status: 'completed',
        session_id: `session_${i}_${Date.now()}`,
        created_at: createdAt.toISOString(),
        started_at: createdAt.toISOString(),
        completed_at: completedAt.toISOString(),
        total_duration_seconds: 900 + (i * 60)
      };

      sampleSubmissions.push(submission);
    }

    // Insert submissions
    const { data: insertedSubmissions, error: submissionError } = await supabase
      .from('assessment_submissions')
      .insert(sampleSubmissions)
      .select();

    if (submissionError) throw submissionError;
    console.log(`✅ Created ${insertedSubmissions.length} sample submissions`);

    // Create lead scores for each submission
    console.log('🎯 Creating lead scores...');
    const leadScores = insertedSubmissions.map((submission, index) => {
      const riskScore = 8 + (index % 12);
      const riskPercentage = Math.round((riskScore / 32) * 100);
      const riskLevel = riskScore <= 12 ? 'LOW' : riskScore <= 20 ? 'MEDIUM' : 'HIGH';
      const leadPriority = riskScore <= 12 ? 1 : riskScore <= 16 ? 2 : riskScore <= 20 ? 3 : 4;

      return {
        submission_id: submission.id,
        total_risk_score: riskScore,
        max_possible_score: 32,
        risk_percentage: riskPercentage,
        risk_level: riskLevel,
        lead_priority: leadPriority,
        follow_up_urgency: riskLevel === 'HIGH' ? 'urgent' : riskLevel === 'MEDIUM' ? 'high' : 'medium'
      };
    });

    const { error: scoresError } = await supabase
      .from('lead_scores')
      .insert(leadScores);

    if (scoresError) throw scoresError;
    console.log(`✅ Created ${leadScores.length} lead scores`);

    // Create enhanced lead scores
    console.log('🚀 Creating enhanced lead scores...');
    const enhancedScores = insertedSubmissions.map((submission, index) => {
      const baseScore = 50 + (index * 3);
      return {
        submission_id: submission.id,
        completion_score: baseScore + 10,
        industry_score: baseScore + 5,
        size_score: baseScore,
        engagement_score: baseScore + 15,
        urgency_score: baseScore + 8,
        total_score: baseScore * 5,
        conversion_probability: Math.min(90, baseScore + 20),
        priority_level: baseScore > 70 ? 'HIGH' : baseScore > 50 ? 'MEDIUM' : 'LOW',
        recommended_actions: JSON.stringify([
          'Implement multi-factor authentication',
          'Conduct security awareness training',
          'Review access controls'
        ])
      };
    });

    const { error: enhancedError } = await supabase
      .from('enhanced_lead_scores')
      .insert(enhancedScores);

    if (enhancedError) throw enhancedError;
    console.log(`✅ Created ${enhancedScores.length} enhanced lead scores`);

    // Create user journey events
    console.log('🛤️ Creating user journey events...');
    const journeyEvents = [];

    insertedSubmissions.forEach((submission, index) => {
      const sessionId = submission.session_id;
      const baseTime = new Date(submission.created_at);

      // Page view event
      journeyEvents.push({
        event_type: 'page_view',
        page_url: '/assessments',
        session_id: sessionId,
        submission_id: submission.id,
        event_data: JSON.stringify({ referrer: 'https://google.com' }),
        created_at: baseTime.toISOString()
      });

      // Assessment start event
      journeyEvents.push({
        event_type: 'assessment_start',
        page_url: '/assessment/cybersecurity-maturity',
        session_id: sessionId,
        submission_id: submission.id,
        event_data: JSON.stringify({ assessmentType: 'cybersecurity-maturity' }),
        created_at: new Date(baseTime.getTime() + 2 * 60 * 1000).toISOString()
      });

      // Assessment completion event
      journeyEvents.push({
        event_type: 'assessment_complete',
        page_url: '/assessment/cybersecurity-maturity/results',
        session_id: sessionId,
        submission_id: submission.id,
        event_data: JSON.stringify({ completionTime: submission.total_duration_seconds }),
        created_at: submission.completed_at
      });
    });

    const { error: journeyError } = await supabase
      .from('user_journey_events')
      .insert(journeyEvents);

    if (journeyError) throw journeyError;
    console.log(`✅ Created ${journeyEvents.length} journey events`);

    console.log('🎉 Sample data population completed successfully!');
    return true;

  } catch (error) {
    console.error('❌ Error populating sample data:', error);
    return false;
  }
}

async function verifyData() {
  console.log('🔍 Verifying populated data...');
  
  try {
    const { count: submissions } = await supabase
      .from('assessment_submissions')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'completed');

    const { count: leadScores } = await supabase
      .from('lead_scores')
      .select('*', { count: 'exact', head: true });

    const { count: enhancedScores } = await supabase
      .from('enhanced_lead_scores')
      .select('*', { count: 'exact', head: true });

    const { count: journeyEvents } = await supabase
      .from('user_journey_events')
      .select('*', { count: 'exact', head: true });

    console.log('📊 Data Verification Results:');
    console.log(`   Completed Submissions: ${submissions}`);
    console.log(`   Lead Scores: ${leadScores}`);
    console.log(`   Enhanced Scores: ${enhancedScores}`);
    console.log(`   Journey Events: ${journeyEvents}`);

    const success = submissions > 0 && leadScores > 0 && enhancedScores > 0 && journeyEvents > 0;
    console.log(`🎯 Verification: ${success ? '✅ PASSED' : '❌ FAILED'}`);
    
    return success;
  } catch (error) {
    console.error('❌ Verification failed:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 BlackVeil Security - Direct Database Fix & Population');
  console.log('=' .repeat(60));
  
  try {
    // Step 1: Create missing tables
    const tablesCreated = await createMissingTables();
    if (!tablesCreated) {
      console.error('❌ Failed to create tables. Exiting.');
      process.exit(1);
    }

    // Step 2: Populate sample data
    const dataPopulated = await populateSampleData();
    if (!dataPopulated) {
      console.error('❌ Failed to populate data. Exiting.');
      process.exit(1);
    }

    // Step 3: Verify everything worked
    const verified = await verifyData();
    if (!verified) {
      console.error('❌ Data verification failed. Check the database.');
      process.exit(1);
    }

    console.log('\n🎉 Database fix and population completed successfully!');
    console.log('✅ The BI Dashboard should now be fully functional.');
    console.log('🔗 Navigate to http://localhost:8080/admin to test the dashboard.');

  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

// Run the script
main();
