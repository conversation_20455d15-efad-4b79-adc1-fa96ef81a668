// Test Advanced BI Tables Script
// Quick test to verify the advanced BI tables are working

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://wikngnwwakatokbgvenw.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODU2NjQ1NCwiZXhwIjoyMDY0MTQyNDU0fQ.j1sjkEHL2LR78ChzOL6MdieomYgSk1CNXXR0n5sq9eo';

const supabase = createClient(supabaseUrl, serviceKey);

async function testAdvancedBI() {
  console.log('🧪 Testing Advanced BI Tables');
  console.log('=' .repeat(30));

  try {
    // Test 1: Create a sample A/B test
    console.log('\n🧪 Test 1: Creating sample A/B test...');
    const { data: abTest, error: abTestError } = await supabase
      .from('ab_tests')
      .upsert({
        name: 'Production CTA Test',
        description: 'Testing consultative vs direct CTAs',
        test_type: 'cta_button',
        hypothesis: 'Consultative CTAs will perform better',
        success_metric: 'click_rate',
        is_active: true
      }, { onConflict: 'name' })
      .select()
      .single();

    if (abTestError) {
      console.log(`❌ A/B test error: ${abTestError.message}`);
    } else {
      console.log(`✅ A/B test created: ${abTest.name}`);
    }

    // Test 2: Create enhanced lead score
    console.log('\n🎯 Test 2: Creating enhanced lead score...');
    
    // Get a real submission
    const { data: submissions } = await supabase
      .from('assessment_submissions')
      .select('id, company_name')
      .limit(1);

    if (submissions && submissions.length > 0) {
      const submission = submissions[0];
      
      const { data: enhancedScore, error: enhancedError } = await supabase
        .from('enhanced_lead_scores')
        .upsert({
          submission_id: submission.id,
          completion_score: 95,
          industry_score: 80,
          size_score: 75,
          engagement_score: 85,
          urgency_score: 70,
          total_score: 405,
          conversion_probability: 78,
          priority_level: 'HIGH',
          recommended_actions: {
            immediate: ['Schedule security consultation', 'Review critical vulnerabilities'],
            follow_up: ['Implement MFA', 'Security training'],
            timeline: '1-2 weeks'
          }
        }, { onConflict: 'submission_id' })
        .select()
        .single();

      if (enhancedError) {
        console.log(`❌ Enhanced score error: ${enhancedError.message}`);
      } else {
        console.log(`✅ Enhanced score created for ${submission.company_name}`);
      }
    }

    // Test 3: Create user journey event
    console.log('\n🛤️ Test 3: Creating user journey event...');
    const { data: journeyEvent, error: journeyError } = await supabase
      .from('user_journey_events')
      .insert({
        event_type: 'assessment_complete',
        session_id: 'test_session_' + Date.now(),
        page_url: '/assessment/results',
        event_data: {
          assessment_type: 'phishing-risk',
          completion_time: 180,
          risk_score: 75
        }
      })
      .select()
      .single();

    if (journeyError) {
      console.log(`❌ Journey event error: ${journeyError.message}`);
    } else {
      console.log(`✅ Journey event created: ${journeyEvent.event_type}`);
    }

    // Test 4: Verify all tables
    console.log('\n📊 Test 4: Verifying all advanced BI tables...');
    
    const tables = ['enhanced_lead_scores', 'user_journey_events', 'ab_tests', 'ab_test_variants', 'ab_test_participations'];
    
    for (const table of tables) {
      const { count, error } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.log(`❌ ${table}: ${error.message}`);
      } else {
        console.log(`✅ ${table}: ${count || 0} records`);
      }
    }

    console.log('\n🎉 Advanced BI tables are working correctly!');
    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

testAdvancedBI().then((success) => {
  if (success) {
    console.log('\n✅ All advanced BI features are operational');
    console.log('📋 Ready for production lead funnel');
  } else {
    console.log('\n❌ Some issues found with advanced BI tables');
  }
}).catch(error => {
  console.error('❌ Test execution failed:', error);
});
