-- Assessment Population Script with Manual ID Assignment
-- This script manually assigns IDs to avoid the NOT NULL constraint issue

-- First, let's find the highest existing question ID to avoid conflicts
-- Run this query first to see the current max ID:
-- SELECT MAX(id) FROM assessment_questions;

-- Based on the existing data, we'll start from ID 100 to be safe
-- You can adjust these starting IDs if needed

-- Insert questions for Cybersecurity Maturity Assessment (starting from ID 100)
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 100, id, 'How often does your organization conduct cybersecurity risk assessments?', 'Risk Management', 1, true
FROM assessment_types WHERE slug = 'cybersecurity-maturity';

INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 101, id, 'What type of cybersecurity training do you provide to employees?', 'Security Awareness', 2, true
FROM assessment_types WHERE slug = 'cybersecurity-maturity';

INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 102, id, 'How does your organization manage software updates and patches?', 'Patch Management', 3, true
FROM assessment_types WHERE slug = 'cybersecurity-maturity';

INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 103, id, 'What backup and recovery procedures do you have in place?', 'Business Continuity', 4, true
FROM assessment_types WHERE slug = 'cybersecurity-maturity';

INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 104, id, 'How do you control access to sensitive systems and data?', 'Access Control', 5, true
FROM assessment_types WHERE slug = 'cybersecurity-maturity';

INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 105, id, 'Do you have an incident response plan for cybersecurity breaches?', 'Incident Response', 6, true
FROM assessment_types WHERE slug = 'cybersecurity-maturity';

INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 106, id, 'How do you monitor your network for security threats?', 'Threat Detection', 7, true
FROM assessment_types WHERE slug = 'cybersecurity-maturity';

INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 107, id, 'What is your approach to vendor and third-party security?', 'Third-Party Risk', 8, true
FROM assessment_types WHERE slug = 'cybersecurity-maturity';

-- Insert questions for DMARC Compliance Assessment (starting from ID 200)
INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 200, id, 'Have you implemented SPF (Sender Policy Framework) for your domain?', 'Email Authentication', 1, true
FROM assessment_types WHERE slug = 'dmarc-compliance';

INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 201, id, 'Do you have DKIM (DomainKeys Identified Mail) configured?', 'Email Authentication', 2, true
FROM assessment_types WHERE slug = 'dmarc-compliance';

INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 202, id, 'What is your current DMARC policy setting?', 'DMARC Policy', 3, true
FROM assessment_types WHERE slug = 'dmarc-compliance';

INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 203, id, 'How do you monitor DMARC reports and failures?', 'Monitoring', 4, true
FROM assessment_types WHERE slug = 'dmarc-compliance';

INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 204, id, 'Do you have email security awareness training for employees?', 'Security Awareness', 5, true
FROM assessment_types WHERE slug = 'dmarc-compliance';

INSERT INTO assessment_questions (id, assessment_type_id, question_text, category, order_index, is_active)
SELECT 205, id, 'How do you handle email from external domains that fail authentication?', 'Email Filtering', 6, true
FROM assessment_types WHERE slug = 'dmarc-compliance';

-- Now insert options for Cybersecurity Maturity Assessment questions
-- Question 100 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (100, 'Never or rarely (annually or less)', 4, 1);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (100, 'Occasionally (every 6-12 months)', 3, 2);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (100, 'Regularly (quarterly)', 2, 3);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (100, 'Continuously (monthly or more)', 1, 4);

-- Question 101 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (101, 'No formal training provided', 4, 1);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (101, 'Basic annual training only', 3, 2);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (101, 'Regular training with updates', 2, 3);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (101, 'Comprehensive ongoing training with simulations', 1, 4);

-- Question 102 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (102, 'Manual updates when remembered', 4, 1);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (102, 'Scheduled monthly updates', 3, 2);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (102, 'Automated updates for most systems', 2, 3);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (102, 'Comprehensive automated patch management', 1, 4);

-- Question 103 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (103, 'No formal backup procedures', 4, 1);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (103, 'Basic local backups', 3, 2);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (103, 'Regular offsite backups', 2, 3);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (103, 'Comprehensive backup with tested recovery', 1, 4);

-- Question 104 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (104, 'Basic password protection only', 4, 1);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (104, 'Role-based access with passwords', 3, 2);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (104, 'Multi-factor authentication for some systems', 2, 3);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (104, 'Comprehensive MFA and zero-trust approach', 1, 4);

-- Question 105 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (105, 'No formal incident response plan', 4, 1);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (105, 'Basic plan but not tested', 3, 2);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (105, 'Documented plan with some testing', 2, 3);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (105, 'Comprehensive tested plan with regular drills', 1, 4);

-- Question 106 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (106, 'No active monitoring', 4, 1);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (106, 'Basic antivirus software only', 3, 2);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (106, 'Network monitoring with some alerting', 2, 3);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (106, 'Advanced threat detection and response', 1, 4);

-- Question 107 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (107, 'No vendor security assessments', 4, 1);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (107, 'Basic vendor questionnaires', 3, 2);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (107, 'Regular vendor security reviews', 2, 3);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (107, 'Comprehensive third-party risk management', 1, 4);

-- Now insert options for DMARC Compliance Assessment questions
-- Question 200 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (200, 'No SPF record configured', 4, 1);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (200, 'Basic SPF record with some gaps', 3, 2);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (200, 'Comprehensive SPF record configured', 2, 3);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (200, 'SPF with strict policy and monitoring', 1, 4);

-- Question 201 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (201, 'No DKIM signing configured', 4, 1);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (201, 'DKIM for some email services only', 3, 2);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (201, 'DKIM configured for all outbound email', 2, 3);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (201, 'DKIM with key rotation and monitoring', 1, 4);

-- Question 202 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (202, 'No DMARC record published', 4, 1);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (202, 'DMARC with none policy (monitoring only)', 3, 2);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (202, 'DMARC with quarantine policy', 2, 3);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (202, 'DMARC with reject policy', 1, 4);

-- Question 203 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (203, 'No DMARC report monitoring', 4, 1);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (203, 'Occasional manual review of reports', 3, 2);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (203, 'Regular monitoring with basic analysis', 2, 3);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (203, 'Automated monitoring with alerting', 1, 4);

-- Question 204 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (204, 'No email security training', 4, 1);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (204, 'Basic annual training', 3, 2);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (204, 'Regular training with phishing simulations', 2, 3);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (204, 'Comprehensive ongoing training program', 1, 4);

-- Question 205 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (205, 'No special handling for failed authentication', 4, 1);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (205, 'Basic spam filtering only', 3, 2);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (205, 'Enhanced filtering with some authentication checks', 2, 3);

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
VALUES (205, 'Strict authentication-based filtering', 1, 4);

-- Verification query to check what we have added
SELECT
  at.title as assessment_title,
  at.slug,
  COUNT(DISTINCT aq.id) as question_count,
  COUNT(aqo.id) as option_count
FROM assessment_types at
LEFT JOIN assessment_questions aq ON at.id = aq.assessment_type_id
LEFT JOIN assessment_question_options aqo ON aq.id = aqo.question_id
WHERE at.slug IN ('cybersecurity-maturity', 'dmarc-compliance')
GROUP BY at.id, at.title, at.slug
ORDER BY at.title;
