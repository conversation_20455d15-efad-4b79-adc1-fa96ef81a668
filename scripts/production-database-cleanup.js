// Production Database Cleanup Script
// This script removes mock data and prepares the BlackVeil Security platform for production

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
let supabaseUrl;
try {
  const envPath = join(__dirname, '..', '.env.local');
  const envContent = readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  envLines.forEach(line => {
    const [key, value] = line.split('=');
    if (key === 'VITE_SUPABASE_URL') supabaseUrl = value;
  });
} catch (error) {
  console.error('❌ Could not load .env.local file:', error.message);
}

if (!supabaseUrl) {
  console.error('❌ Missing Supabase URL');
  process.exit(1);
}

// Service role key for admin operations
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODU2NjQ1NCwiZXhwIjoyMDY0MTQyNDU0fQ.j1sjkEHL2LR78ChzOL6MdieomYgSk1CNXXR0n5sq9eo';

const supabase = createClient(supabaseUrl, serviceKey);

// Real submissions to preserve (identified from analysis)
const realSubmissions = [
  'c77ef790-1baf-4267-a63b-d3f7c6c8eaf2', // Blackveil - <EMAIL>
  '1ffcb7cf-6783-4e2e-b4d4-aa32ea8e120b', // Alexander pr - <EMAIL>
  '9d824ee5-7955-4073-819b-f48e821f4a7f'  // de.iterate - <EMAIL>
];

async function cleanupMockData() {
  console.log('🧹 BlackVeil Security - Production Database Cleanup');
  console.log('📋 Removing mock data while preserving real user submissions');
  console.log('=' .repeat(60));

  try {
    // Step 1: Identify all submissions to remove
    console.log('\n📊 Step 1: Identifying mock submissions...');
    
    const { data: allSubmissions, error: fetchError } = await supabase
      .from('assessment_submissions')
      .select('id, company_name, email, contact_name');

    if (fetchError) {
      throw new Error(`Failed to fetch submissions: ${fetchError.message}`);
    }

    const mockSubmissions = allSubmissions.filter(sub => 
      !realSubmissions.includes(sub.id) && 
      (sub.company_name.includes('Sample Company') || 
       sub.email.includes('samplecompany'))
    );

    const realSubmissionsData = allSubmissions.filter(sub => 
      realSubmissions.includes(sub.id)
    );

    console.log(`✅ Found ${allSubmissions.length} total submissions`);
    console.log(`❌ Identified ${mockSubmissions.length} mock submissions to remove`);
    console.log(`✅ Preserving ${realSubmissionsData.length} real submissions`);

    // Show what will be preserved
    console.log('\n🔒 Real submissions to preserve:');
    realSubmissionsData.forEach((sub, index) => {
      console.log(`   ${index + 1}. ${sub.company_name} (${sub.email})`);
    });

    // Step 2: Remove associated data first (foreign key constraints)
    console.log('\n🗑️ Step 2: Removing associated data...');
    
    const mockSubmissionIds = mockSubmissions.map(sub => sub.id);
    
    // Remove assessment answers
    console.log('   📝 Removing assessment answers...');
    const { error: answersError } = await supabase
      .from('assessment_answers')
      .delete()
      .in('submission_id', mockSubmissionIds);

    if (answersError) {
      console.warn(`⚠️ Warning removing answers: ${answersError.message}`);
    } else {
      console.log('   ✅ Assessment answers removed');
    }

    // Remove lead scores
    console.log('   🎯 Removing lead scores...');
    const { error: scoresError } = await supabase
      .from('lead_scores')
      .delete()
      .in('submission_id', mockSubmissionIds);

    if (scoresError) {
      console.warn(`⚠️ Warning removing lead scores: ${scoresError.message}`);
    } else {
      console.log('   ✅ Lead scores removed');
    }

    // Remove enhanced lead scores
    console.log('   🚀 Removing enhanced lead scores...');
    const { error: enhancedError } = await supabase
      .from('enhanced_lead_scores')
      .delete()
      .in('submission_id', mockSubmissionIds);

    if (enhancedError) {
      console.warn(`⚠️ Warning removing enhanced scores: ${enhancedError.message}`);
    } else {
      console.log('   ✅ Enhanced lead scores removed');
    }

    // Remove user journey events
    console.log('   🛤️ Removing user journey events...');
    const { error: journeyError } = await supabase
      .from('user_journey_events')
      .delete()
      .in('submission_id', mockSubmissionIds);

    if (journeyError) {
      console.warn(`⚠️ Warning removing journey events: ${journeyError.message}`);
    } else {
      console.log('   ✅ User journey events removed');
    }

    // Remove assessment analytics
    console.log('   📊 Removing assessment analytics...');
    const { error: analyticsError } = await supabase
      .from('assessment_analytics')
      .delete()
      .in('submission_id', mockSubmissionIds);

    if (analyticsError) {
      console.warn(`⚠️ Warning removing analytics: ${analyticsError.message}`);
    } else {
      console.log('   ✅ Assessment analytics removed');
    }

    // Step 3: Remove mock submissions
    console.log('\n🗑️ Step 3: Removing mock submissions...');
    const { error: submissionsError } = await supabase
      .from('assessment_submissions')
      .delete()
      .in('id', mockSubmissionIds);

    if (submissionsError) {
      throw new Error(`Failed to remove submissions: ${submissionsError.message}`);
    }

    console.log(`✅ Removed ${mockSubmissions.length} mock submissions`);

    // Step 4: Verify cleanup
    console.log('\n🔍 Step 4: Verifying cleanup...');
    
    const { count: remainingCount, error: countError } = await supabase
      .from('assessment_submissions')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      throw new Error(`Failed to verify cleanup: ${countError.message}`);
    }

    console.log(`✅ Database cleanup completed`);
    console.log(`📊 Remaining submissions: ${remainingCount}`);

    if (remainingCount !== realSubmissions.length) {
      console.warn(`⚠️ Warning: Expected ${realSubmissions.length} submissions, found ${remainingCount}`);
    }

    // Step 5: Show final state
    console.log('\n📋 Step 5: Final database state...');
    
    const { data: finalSubmissions, error: finalError } = await supabase
      .from('assessment_submissions')
      .select('id, company_name, email, contact_name, industry, status, created_at');

    if (finalError) {
      throw new Error(`Failed to fetch final state: ${finalError.message}`);
    }

    console.log('🎉 Production-ready submissions:');
    finalSubmissions?.forEach((sub, index) => {
      console.log(`   ${index + 1}. ${sub.company_name} (${sub.email}) - ${sub.industry} - ${sub.status}`);
      console.log(`      Created: ${new Date(sub.created_at).toLocaleDateString()}`);
    });

    return {
      success: true,
      removedCount: mockSubmissions.length,
      remainingCount: remainingCount,
      realSubmissions: finalSubmissions
    };

  } catch (error) {
    console.error('❌ Database cleanup failed:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// Function to verify data integrity
async function verifyDataIntegrity() {
  console.log('\n🔍 Verifying Data Integrity...');
  console.log('=' .repeat(30));

  try {
    // Check submissions
    const { count: submissionCount } = await supabase
      .from('assessment_submissions')
      .select('*', { count: 'exact', head: true });

    // Check lead scores
    const { count: leadScoreCount } = await supabase
      .from('lead_scores')
      .select('*', { count: 'exact', head: true });

    // Check assessment answers
    const { count: answerCount } = await supabase
      .from('assessment_answers')
      .select('*', { count: 'exact', head: true });

    console.log(`📝 Assessment submissions: ${submissionCount}`);
    console.log(`🎯 Lead scores: ${leadScoreCount}`);
    console.log(`❓ Assessment answers: ${answerCount}`);

    // Check for orphaned data
    const { data: orphanedScores } = await supabase
      .from('lead_scores')
      .select('submission_id')
      .not('submission_id', 'in', `(${realSubmissions.map(id => `'${id}'`).join(',')})`);

    if (orphanedScores && orphanedScores.length > 0) {
      console.warn(`⚠️ Found ${orphanedScores.length} orphaned lead scores`);
    } else {
      console.log('✅ No orphaned data found');
    }

    return {
      submissionCount,
      leadScoreCount,
      answerCount,
      orphanedCount: orphanedScores?.length || 0
    };

  } catch (error) {
    console.error('❌ Data integrity check failed:', error.message);
    return null;
  }
}

// Main execution
async function executeCleanup() {
  console.log('🚀 Starting Production Database Cleanup...\n');

  // Perform cleanup
  const cleanupResult = await cleanupMockData();
  
  if (!cleanupResult.success) {
    console.log('\n❌ Cleanup failed. Aborting...');
    process.exit(1);
  }

  // Verify integrity
  const integrityResult = await verifyDataIntegrity();
  
  if (integrityResult) {
    console.log('\n🎉 Database cleanup completed successfully!');
    console.log('📊 Summary:');
    console.log(`   • Removed: ${cleanupResult.removedCount} mock submissions`);
    console.log(`   • Preserved: ${cleanupResult.remainingCount} real submissions`);
    console.log(`   • Data integrity: ${integrityResult.orphanedCount === 0 ? '✅ Clean' : '⚠️ Issues found'}`);
    
    console.log('\n📋 Next Steps:');
    console.log('   1. Run: node scripts/database-inspection.js');
    console.log('   2. Configure lead funnel automation');
    console.log('   3. Set up email sequences');
    console.log('   4. Test BI dashboard with clean data');
  }

  return cleanupResult.success;
}

// Run cleanup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  executeCleanup().then((success) => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ Script execution failed:', error);
    process.exit(1);
  });
}

export { executeCleanup, cleanupMockData, verifyDataIntegrity };
