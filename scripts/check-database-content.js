#!/usr/bin/env node

/**
 * Database Content Verification Script for BlackVeil Security Assessment Platform
 * Checks if the database has proper content population for production readiness
 */

import { createClient } from '@supabase/supabase-js';

// Hardcode the credentials for this check since we know them from .env.local
const supabaseUrl = 'https://wikngnwwakatokbgvenw.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NjY0NTQsImV4cCI6MjA2NDE0MjQ1NH0.CCoK3cPN0Pe-AVUgePrxmWvf9qZho0b-prGDilfKmZ4';

if (!supabaseUrl || !supabaseKey) {
  console.log('⚠️  Database credentials not available. Skipping content verification.');
  console.log('📝 To verify database content, please ensure Supabase environment variables are set.');
  process.exit(0);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkDatabaseContent() {
  console.log('🔍 BlackVeil Database Content Verification\n');

  try {
    // Check Assessment Types
    console.log('📋 Checking Assessment Types...');
    const { data: assessmentTypes, error: typesError } = await supabase
      .from('assessment_types')
      .select('*')
      .eq('is_active', true);

    if (typesError) {
      console.log('❌ Error fetching assessment types:', typesError.message);
    } else {
      console.log(`✅ Assessment Types: ${assessmentTypes.length} active types found`);
      assessmentTypes.forEach(type => {
        console.log(`   • ${type.title} (${type.slug}) - ${type.category}`);
      });
    }

    // Check Questions for each assessment type
    console.log('\n📝 Checking Assessment Questions...');
    if (assessmentTypes && assessmentTypes.length > 0) {
      for (const type of assessmentTypes) {
        const { data: questions, error: questionsError } = await supabase
          .from('assessment_questions')
          .select('*')
          .eq('assessment_type_id', type.id)
          .eq('is_active', true);

        if (questionsError) {
          console.log(`❌ Error fetching questions for ${type.title}:`, questionsError.message);
        } else {
          console.log(`   ${type.title}: ${questions.length} questions`);
          
          if (questions.length === 0) {
            console.log(`   ⚠️  No questions found for ${type.title}`);
          }
        }
      }
    }

    // Check Question Options
    console.log('\n🎯 Checking Question Options...');
    const { count: optionsCount, error: optionsError } = await supabase
      .from('assessment_question_options')
      .select('*', { count: 'exact', head: true });

    if (optionsError) {
      console.log('❌ Error fetching options:', optionsError.message);
    } else {
      console.log(`✅ Question Options: ${optionsCount} options found in database`);
    }

    // Check Recent Submissions
    console.log('\n📊 Checking Recent Activity...');
    const { data: submissions, error: submissionsError } = await supabase
      .from('assessment_submissions')
      .select('id, company_name, status, created_at')
      .order('created_at', { ascending: false })
      .limit(5);

    if (submissionsError) {
      console.log('❌ Error fetching submissions:', submissionsError.message);
    } else {
      console.log(`✅ Recent Submissions: ${submissions.length} found`);
      submissions.forEach(sub => {
        const date = new Date(sub.created_at).toLocaleDateString();
        console.log(`   • ${sub.company_name} (${sub.status}) - ${date}`);
      });
    }

    // Check Lead Scores
    console.log('\n🎯 Checking Lead Scores...');
    const { count: scoresCount } = await supabase
      .from('lead_scores')
      .select('*', { count: 'exact', head: true });
    
    console.log(`✅ Lead Scores: ${scoresCount} scores calculated`);

    // Summary
    console.log('\n📋 DATABASE CONTENT SUMMARY');
    console.log('='.repeat(50));
    
    const hasTypes = assessmentTypes && assessmentTypes.length > 0;
    const hasQuestions = assessmentTypes && assessmentTypes.some(async type => {
      const { count } = await supabase
        .from('assessment_questions')
        .select('*', { count: 'exact', head: true })
        .eq('assessment_type_id', type.id);
      return count > 0;
    });

    if (hasTypes) {
      console.log('✅ Assessment Types: Properly populated');
    } else {
      console.log('❌ Assessment Types: Missing or empty');
    }

    if (optionsCount > 0) {
      console.log('✅ Question Options: Available');
    } else {
      console.log('❌ Question Options: Missing - assessments cannot be completed');
    }

    if (scoresCount > 0) {
      console.log('✅ Scoring System: Working (has calculated scores)');
    } else {
      console.log('⚠️  Scoring System: No scores yet (expected for new system)');
    }

    console.log('\n💡 NEXT STEPS:');
    if (!hasTypes) {
      console.log('  1. Populate assessment_types table with assessment definitions');
    }
    if (optionsCount === 0) {
      console.log('  2. Add question options to make assessments answerable');
    }
    console.log('  3. Test end-to-end assessment flow');
    console.log('  4. Verify email template rendering with real data');

  } catch (error) {
    console.error('💥 Database verification failed:', error.message);
  }
}

checkDatabaseContent();
