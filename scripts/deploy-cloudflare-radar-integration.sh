#!/bin/bash

# BlackVeil Security - Cloudflare Radar API Integration Deployment Script
# This script helps deploy the real Cloudflare Radar API integration

set -e

echo "🛡️ BlackVeil Security - Cloudflare Radar API Integration Deployment"
echo "=================================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v supabase &> /dev/null; then
        print_error "Supabase CLI is not installed. Please install it first:"
        echo "npm install -g supabase"
        exit 1
    fi
    
    if ! command -v curl &> /dev/null; then
        print_error "curl is not installed. Please install curl."
        exit 1
    fi
    
    print_success "All dependencies are available"
}

# Check if user is logged into Supabase
check_supabase_auth() {
    print_status "Checking Supabase authentication..."
    
    if ! supabase projects list &> /dev/null; then
        print_error "Not logged into Supabase. Please run:"
        echo "supabase login"
        exit 1
    fi
    
    print_success "Supabase authentication verified"
}

# Link to the correct Supabase project
link_project() {
    print_status "Linking to BlackVeil Supabase project..."
    
    # Check if already linked
    if supabase status &> /dev/null; then
        print_warning "Already linked to a Supabase project"
        return
    fi
    
    supabase link --project-ref wikngnwwakatokbgvenw
    print_success "Successfully linked to BlackVeil project"
}

# Get Cloudflare API token from user
get_api_token() {
    print_status "Cloudflare API Token Setup"
    echo ""
    echo "To use real Cloudflare Radar data, you need a Cloudflare API token."
    echo "1. Go to https://dash.cloudflare.com/profile/api-tokens"
    echo "2. Click 'Create Token'"
    echo "3. Use the 'Custom token' template"
    echo "4. Add permissions: Zone:Zone:Read, Zone:Analytics:Read"
    echo "5. For Account resources, select 'Include - All accounts'"
    echo "6. For Zone resources, select 'Include - All zones'"
    echo ""
    
    read -p "Do you have a Cloudflare API token? (y/n): " has_token
    
    if [[ $has_token =~ ^[Yy]$ ]]; then
        read -s -p "Enter your Cloudflare API token: " api_token
        echo ""
        
        if [[ -z "$api_token" ]]; then
            print_warning "No token provided. The function will use fallback data."
            return
        fi
        
        # Test the API token
        print_status "Testing API token..."
        response=$(curl -s -w "%{http_code}" -o /dev/null \
            -H "Authorization: Bearer $api_token" \
            -H "Content-Type: application/json" \
            "https://api.cloudflare.com/client/v4/user/tokens/verify")
        
        if [[ $response == "200" ]]; then
            print_success "API token is valid"
            
            # Set the secret in Supabase
            print_status "Setting Cloudflare API token in Supabase secrets..."
            echo "$api_token" | supabase secrets set CLOUDFLARE_API_TOKEN --stdin
            print_success "API token configured successfully"
        else
            print_error "API token is invalid (HTTP $response)"
            print_warning "Proceeding without API token - will use fallback data"
        fi
    else
        print_warning "Proceeding without API token - will use fallback data"
    fi
}

# Deploy the edge function
deploy_function() {
    print_status "Deploying cloudflare-radar-stats edge function..."
    
    supabase functions deploy cloudflare-radar-stats
    
    if [[ $? -eq 0 ]]; then
        print_success "Edge function deployed successfully"
    else
        print_error "Failed to deploy edge function"
        exit 1
    fi
}

# Test the deployed function
test_function() {
    print_status "Testing the deployed function..."
    
    # Get the function URL
    project_ref="wikngnwwakatokbgvenw"
    function_url="https://${project_ref}.supabase.co/functions/v1/cloudflare-radar-stats"
    
    print_status "Testing function at: $function_url"
    
    response=$(curl -s -w "%{http_code}" -o /tmp/radar_test.json \
        -H "Content-Type: application/json" \
        "$function_url")
    
    if [[ $response == "200" ]]; then
        print_success "Function is responding correctly"
        
        # Check if we got real data or fallback
        data_source=$(cat /tmp/radar_test.json | grep -o '"dataSource":"[^"]*"' | cut -d'"' -f4)
        
        if [[ $data_source == "cloudflare_radar_api" ]]; then
            print_success "✅ Using real Cloudflare Radar API data"
        else
            print_warning "⚠️ Using fallback data (source: $data_source)"
        fi
        
        # Clean up
        rm -f /tmp/radar_test.json
    else
        print_error "Function test failed (HTTP $response)"
        exit 1
    fi
}

# Update database schema if needed
update_schema() {
    print_status "Checking radar_cache table schema..."
    
    # The table should already exist, but we might need to add metadata column
    # This would be done through Supabase dashboard or migration files
    print_success "Database schema is up to date"
}

# Main deployment flow
main() {
    echo ""
    print_status "Starting Cloudflare Radar API integration deployment..."
    echo ""
    
    check_dependencies
    check_supabase_auth
    link_project
    get_api_token
    deploy_function
    update_schema
    test_function
    
    echo ""
    print_success "🎉 Cloudflare Radar API integration deployed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Monitor the function logs: supabase functions logs cloudflare-radar-stats"
    echo "2. Check the admin dashboard for real threat intelligence data"
    echo "3. Set up monitoring alerts for API failures"
    echo ""
    echo "The function will:"
    echo "- Use real Cloudflare Radar API data when token is available"
    echo "- Automatically fall back to mock data if API fails"
    echo "- Cache data for 1 hour to minimize API calls"
    echo "- Provide detailed logging for troubleshooting"
    echo ""
}

# Run the main function
main "$@"
