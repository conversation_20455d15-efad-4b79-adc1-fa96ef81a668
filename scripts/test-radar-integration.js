#!/usr/bin/env node

/**
 * BlackVeil Security - Cloudflare Radar Integration Test Script
 *
 * This script tests the Cloudflare Radar API integration to ensure
 * it's working correctly with both real API data and fallback scenarios.
 */

import https from 'https';
import { performance } from 'perf_hooks';

// Configuration
const FUNCTION_URL = 'https://wikngnwwakatokbgvenw.supabase.co/functions/v1/cloudflare-radar-stats';
const TIMEOUT_MS = 30000; // 30 seconds

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(color, prefix, message) {
  console.log(`${color}[${prefix}]${colors.reset} ${message}`);
}

function info(message) { log(colors.blue, 'INFO', message); }
function success(message) { log(colors.green, 'SUCCESS', message); }
function warning(message) { log(colors.yellow, 'WARNING', message); }
function error(message) { log(colors.red, 'ERROR', message); }

// HTTP request helper
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const startTime = performance.now();
    
    const req = https.request(url, {
      method: 'GET',
      timeout: TIMEOUT_MS,
      ...options
    }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const endTime = performance.now();
        const responseTime = Math.round(endTime - startTime);
        
        try {
          const jsonData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: jsonData,
            responseTime
          });
        } catch (parseError) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data,
            responseTime,
            parseError: parseError.message
          });
        }
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

// Test functions
async function testBasicConnectivity() {
  info('Testing basic connectivity...');
  
  try {
    const response = await makeRequest(FUNCTION_URL);
    
    if (response.statusCode === 200) {
      success(`Function is accessible (${response.responseTime}ms)`);
      return response;
    } else {
      error(`Function returned status ${response.statusCode}`);
      return null;
    }
  } catch (err) {
    error(`Connectivity test failed: ${err.message}`);
    return null;
  }
}

function validateDataStructure(data) {
  info('Validating data structure...');
  
  const requiredFields = [
    'phishing.total',
    'phishing.trend',
    'phishing.topTargets',
    'spoofing.total',
    'spoofing.trend',
    'spoofing.topMethods',
    'dmarc.adoptionRate',
    'dmarc.compliance',
    'dmarc.trend',
    'industryRisks',
    'lastUpdated'
  ];
  
  const errors = [];
  
  for (const field of requiredFields) {
    const value = getNestedValue(data, field);
    if (value === undefined || value === null) {
      errors.push(`Missing field: ${field}`);
    }
  }
  
  // Validate data types
  if (typeof data.phishing?.total !== 'number') {
    errors.push('phishing.total should be a number');
  }
  
  if (!Array.isArray(data.phishing?.topTargets)) {
    errors.push('phishing.topTargets should be an array');
  }
  
  if (typeof data.industryRisks !== 'object') {
    errors.push('industryRisks should be an object');
  }
  
  if (errors.length === 0) {
    success('Data structure is valid');
    return true;
  } else {
    error('Data structure validation failed:');
    errors.forEach(err => error(`  - ${err}`));
    return false;
  }
}

function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

function analyzeDataSource(data) {
  info('Analyzing data source...');
  
  const dataSource = data.dataSource;
  const lastUpdated = new Date(data.lastUpdated);
  const now = new Date();
  const ageMinutes = Math.round((now - lastUpdated) / (1000 * 60));
  
  console.log(`  Data Source: ${dataSource || 'unknown'}`);
  console.log(`  Last Updated: ${data.lastUpdated} (${ageMinutes} minutes ago)`);
  
  if (data.error) {
    warning(`  Error reported: ${data.error}`);
  }
  
  switch (dataSource) {
    case 'cloudflare_radar_api':
      success('✅ Using real Cloudflare Radar API data');
      break;
    case 'fallback_no_token':
      warning('⚠️ Using fallback data - no API token configured');
      break;
    case 'fallback_api_error':
      warning('⚠️ Using fallback data - API error occurred');
      break;
    case 'fallback_critical_error':
      error('❌ Using fallback data - critical error occurred');
      break;
    default:
      warning(`⚠️ Unknown data source: ${dataSource}`);
  }
  
  return dataSource;
}

function validateDataFreshness(data) {
  info('Checking data freshness...');
  
  const lastUpdated = new Date(data.lastUpdated);
  const now = new Date();
  const ageHours = (now - lastUpdated) / (1000 * 60 * 60);
  
  if (ageHours < 1) {
    success(`Data is fresh (${Math.round(ageHours * 60)} minutes old)`);
    return true;
  } else if (ageHours < 24) {
    warning(`Data is ${Math.round(ageHours)} hours old`);
    return true;
  } else {
    error(`Data is stale (${Math.round(ageHours)} hours old)`);
    return false;
  }
}

function generateReport(response, dataSource) {
  console.log('\n' + '='.repeat(60));
  console.log('📊 CLOUDFLARE RADAR INTEGRATION TEST REPORT');
  console.log('='.repeat(60));
  
  console.log(`\n🔗 Function URL: ${FUNCTION_URL}`);
  console.log(`⏱️  Response Time: ${response.responseTime}ms`);
  console.log(`📡 Data Source: ${dataSource}`);
  console.log(`📅 Test Time: ${new Date().toISOString()}`);
  
  console.log('\n📈 Sample Data:');
  console.log(`  Phishing Attacks: ${response.data.phishing?.total?.toLocaleString()}`);
  console.log(`  Spoofing Incidents: ${response.data.spoofing?.total?.toLocaleString()}`);
  console.log(`  DMARC Adoption: ${response.data.dmarc?.adoptionRate}%`);
  console.log(`  Top Risk Industry: ${Object.entries(response.data.industryRisks || {})
    .sort(([,a], [,b]) => b - a)[0]?.[0] || 'Unknown'}`);
  
  console.log('\n🎯 Recommendations:');
  
  if (dataSource === 'cloudflare_radar_api') {
    success('✅ Integration is working perfectly with real API data');
  } else if (dataSource === 'fallback_no_token') {
    warning('⚠️ Consider adding Cloudflare API token for real data:');
    console.log('   supabase secrets set CLOUDFLARE_API_TOKEN=your_token_here');
  } else if (dataSource?.includes('fallback')) {
    warning('⚠️ Check function logs for API issues:');
    console.log('   supabase functions logs cloudflare-radar-stats');
  }
  
  console.log('\n📋 Next Steps:');
  console.log('1. Monitor function logs for any errors');
  console.log('2. Check admin dashboard for threat intelligence display');
  console.log('3. Set up monitoring alerts for API failures');
  console.log('4. Consider implementing additional Radar endpoints');
  
  console.log('\n' + '='.repeat(60));
}

// Main test execution
async function runTests() {
  console.log('🛡️ BlackVeil Security - Cloudflare Radar Integration Test\n');
  
  try {
    // Test 1: Basic connectivity
    const response = await testBasicConnectivity();
    if (!response) {
      error('Basic connectivity test failed. Aborting further tests.');
      process.exit(1);
    }
    
    // Test 2: Data structure validation
    const isValidStructure = validateDataStructure(response.data);
    if (!isValidStructure) {
      error('Data structure validation failed.');
    }
    
    // Test 3: Data source analysis
    const dataSource = analyzeDataSource(response.data);
    
    // Test 4: Data freshness check
    const isFresh = validateDataFreshness(response.data);
    
    // Generate final report
    generateReport(response, dataSource);
    
    // Exit with appropriate code
    if (isValidStructure && isFresh) {
      success('\n🎉 All tests passed! Integration is working correctly.');
      process.exit(0);
    } else {
      warning('\n⚠️ Some tests failed, but integration is functional.');
      process.exit(1);
    }
    
  } catch (err) {
    error(`Test execution failed: ${err.message}`);
    process.exit(1);
  }
}

// Run the tests
runTests();
