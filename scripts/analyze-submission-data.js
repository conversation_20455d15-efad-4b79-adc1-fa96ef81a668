// Production Database Cleanup Script
// This script removes mock data and configures the platform for production lead generation

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env.local file
let supabaseUrl, supabaseKey;
try {
  const envPath = join(__dirname, '..', '.env.local');
  const envContent = readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  envLines.forEach(line => {
    const [key, value] = line.split('=');
    if (key === 'VITE_SUPABASE_URL') supabaseUrl = value;
    if (key === 'VITE_SUPABASE_ANON_KEY') supabaseKey = value;
  });
} catch (error) {
  console.error('❌ Could not load .env.local file:', error.message);
}

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function analyzeSubmissionData() {
  console.log('🔍 BlackVeil Security - Assessment Submission Analysis');
  console.log('📊 Analyzing 23 submissions to determine if they are real or mock data');
  console.log('=' .repeat(70));

  try {
    // Get all assessment submissions with detailed information
    const { data: submissions, error: submissionsError } = await supabase
      .from('assessment_submissions')
      .select(`
        id,
        company_name,
        email,
        contact_name,
        industry,
        employee_count,
        status,
        created_at,
        updated_at,
        assessment_type_id,
        assessment_types (
          title,
          slug
        )
      `)
      .order('created_at', { ascending: true });

    if (submissionsError) {
      console.error('❌ Error fetching submissions:', submissionsError.message);
      return;
    }

    console.log(`\n📋 Found ${submissions?.length || 0} total submissions`);
    console.log('\n🔍 Detailed Analysis:');
    console.log('=' .repeat(50));

    // Analyze patterns that indicate mock vs real data
    const mockIndicators = {
      sampleCompanyNames: 0,
      testEmails: 0,
      sequentialCreation: 0,
      identicalData: 0,
      suspiciousPatterns: []
    };

    const companyNames = new Set();
    const emails = new Set();
    const creationTimes = [];

    submissions?.forEach((submission, index) => {
      console.log(`\n📝 Submission ${index + 1}:`);
      console.log(`   ID: ${submission.id}`);
      console.log(`   Company: ${submission.company_name}`);
      console.log(`   Email: ${submission.email}`);
      console.log(`   Contact: ${submission.contact_name}`);
      console.log(`   Industry: ${submission.industry}`);
      console.log(`   Employees: ${submission.employee_count}`);
      console.log(`   Assessment: ${submission.assessment_types?.title || 'Unknown'}`);
      console.log(`   Status: ${submission.status}`);
      console.log(`   Created: ${new Date(submission.created_at).toLocaleString()}`);

      // Check for mock data indicators
      const companyName = submission.company_name?.toLowerCase() || '';
      const email = submission.email?.toLowerCase() || '';
      const contactName = submission.contact_name?.toLowerCase() || '';

      // Sample/Test company name patterns
      if (companyName.includes('sample') || 
          companyName.includes('test') || 
          companyName.includes('demo') ||
          companyName.includes('example') ||
          companyName.includes('acme') ||
          companyName.includes('corp') && companyName.length < 15) {
        mockIndicators.sampleCompanyNames++;
        mockIndicators.suspiciousPatterns.push(`Sample company name: ${submission.company_name}`);
      }

      // Test email patterns
      if (email.includes('test') || 
          email.includes('sample') || 
          email.includes('demo') ||
          email.includes('example') ||
          email.includes('fake') ||
          email.includes('noreply') ||
          email.includes('temp')) {
        mockIndicators.testEmails++;
        mockIndicators.suspiciousPatterns.push(`Test email: ${submission.email}`);
      }

      // Track for duplicate analysis
      companyNames.add(companyName);
      emails.add(email);
      creationTimes.push(new Date(submission.created_at));
    });

    // Analyze creation time patterns
    console.log('\n⏰ Creation Time Analysis:');
    console.log('=' .repeat(30));
    
    if (creationTimes.length > 1) {
      const timeDifferences = [];
      for (let i = 1; i < creationTimes.length; i++) {
        const diff = creationTimes[i] - creationTimes[i-1];
        timeDifferences.push(diff / 1000); // Convert to seconds
      }

      const avgTimeDiff = timeDifferences.reduce((a, b) => a + b, 0) / timeDifferences.length;
      console.log(`📊 Average time between submissions: ${(avgTimeDiff / 60).toFixed(2)} minutes`);

      // Check for suspiciously regular intervals (indicating batch creation)
      const regularIntervals = timeDifferences.filter(diff => diff < 60).length; // Less than 1 minute apart
      if (regularIntervals > 5) {
        mockIndicators.sequentialCreation = regularIntervals;
        mockIndicators.suspiciousPatterns.push(`${regularIntervals} submissions created within 1 minute of each other`);
      }

      // Show creation date range
      const firstSubmission = new Date(Math.min(...creationTimes));
      const lastSubmission = new Date(Math.max(...creationTimes));
      console.log(`📅 Date range: ${firstSubmission.toLocaleDateString()} to ${lastSubmission.toLocaleDateString()}`);
      console.log(`⏱️ Total time span: ${((lastSubmission - firstSubmission) / (1000 * 60 * 60 * 24)).toFixed(1)} days`);
    }

    // Analyze data diversity
    console.log('\n📊 Data Diversity Analysis:');
    console.log('=' .repeat(30));
    console.log(`🏢 Unique company names: ${companyNames.size} out of ${submissions?.length || 0}`);
    console.log(`📧 Unique email addresses: ${emails.size} out of ${submissions?.length || 0}`);

    // Industry distribution
    const industries = {};
    const employeeCounts = {};
    submissions?.forEach(sub => {
      industries[sub.industry] = (industries[sub.industry] || 0) + 1;
      employeeCounts[sub.employee_count] = (employeeCounts[sub.employee_count] || 0) + 1;
    });

    console.log('\n🏭 Industry Distribution:');
    Object.entries(industries).forEach(([industry, count]) => {
      console.log(`   ${industry}: ${count}`);
    });

    console.log('\n👥 Employee Count Distribution:');
    Object.entries(employeeCounts).forEach(([count, frequency]) => {
      console.log(`   ${count}: ${frequency} companies`);
    });

    // Assessment type distribution
    const assessmentTypes = {};
    submissions?.forEach(sub => {
      const type = sub.assessment_types?.title || 'Unknown';
      assessmentTypes[type] = (assessmentTypes[type] || 0) + 1;
    });

    console.log('\n📋 Assessment Type Distribution:');
    Object.entries(assessmentTypes).forEach(([type, count]) => {
      console.log(`   ${type}: ${count}`);
    });

    // Final analysis
    console.log('\n🎯 Mock Data Analysis Results:');
    console.log('=' .repeat(40));
    console.log(`🏢 Sample company names: ${mockIndicators.sampleCompanyNames}`);
    console.log(`📧 Test email addresses: ${mockIndicators.testEmails}`);
    console.log(`⏰ Sequential creations: ${mockIndicators.sequentialCreation}`);
    console.log(`🔄 Data uniqueness: ${((companyNames.size + emails.size) / (2 * (submissions?.length || 1)) * 100).toFixed(1)}%`);

    if (mockIndicators.suspiciousPatterns.length > 0) {
      console.log('\n⚠️ Suspicious Patterns Found:');
      mockIndicators.suspiciousPatterns.forEach(pattern => {
        console.log(`   • ${pattern}`);
      });
    }

    // Determine if data appears to be mock or real
    const totalMockIndicators = mockIndicators.sampleCompanyNames + mockIndicators.testEmails + 
                               (mockIndicators.sequentialCreation > 0 ? 1 : 0);
    
    const dataUniqueness = (companyNames.size + emails.size) / (2 * (submissions?.length || 1));

    console.log('\n🎯 CONCLUSION:');
    console.log('=' .repeat(20));
    
    if (totalMockIndicators > 5 || dataUniqueness < 0.7) {
      console.log('❌ DATA APPEARS TO BE MOCK/SAMPLE DATA');
      console.log('📋 Reasons:');
      if (mockIndicators.sampleCompanyNames > 3) console.log('   • Multiple sample company names detected');
      if (mockIndicators.testEmails > 3) console.log('   • Multiple test email addresses detected');
      if (mockIndicators.sequentialCreation > 0) console.log('   • Batch creation pattern detected');
      if (dataUniqueness < 0.7) console.log('   • Low data uniqueness (possible duplicates)');
    } else if (totalMockIndicators > 2) {
      console.log('⚠️ DATA APPEARS TO BE MIXED (SOME MOCK, SOME REAL)');
      console.log('📋 Contains both sample data and potentially real submissions');
    } else {
      console.log('✅ DATA APPEARS TO BE REAL USER SUBMISSIONS');
      console.log('📋 High data uniqueness and realistic patterns detected');
    }

    // Show sample of actual data for manual verification
    console.log('\n📋 Sample Data for Manual Verification:');
    console.log('=' .repeat(40));
    const sampleSubmissions = submissions?.slice(0, 5) || [];
    sampleSubmissions.forEach((sub, index) => {
      console.log(`${index + 1}. ${sub.company_name} (${sub.email}) - ${sub.industry}`);
    });

  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
  }
}

// Run the analysis
analyzeSubmissionData().then(() => {
  console.log('\n✅ Assessment submission analysis completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Analysis failed:', error);
  process.exit(1);
});
