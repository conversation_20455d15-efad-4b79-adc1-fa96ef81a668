// Production Readiness Verification Script
// Final verification that all systems are ready for production lead generation

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
let supabaseUrl, anonKey;
try {
  const envPath = join(__dirname, '..', '.env.local');
  const envContent = readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  envLines.forEach(line => {
    const [key, value] = line.split('=');
    if (key === 'VITE_SUPABASE_URL') supabaseUrl = value;
    if (key === 'VITE_SUPABASE_ANON_KEY') anonKey = value;
  });
} catch (error) {
  console.error('❌ Could not load .env.local file:', error.message);
}

if (!supabaseUrl || !anonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, anonKey);

async function verifyProductionReadiness() {
  console.log('🔍 BlackVeil Security - Production Readiness Verification');
  console.log('📊 Final verification of all systems for live lead generation');
  console.log('=' .repeat(70));

  let allSystemsReady = true;
  const results = {
    database: false,
    advancedBI: false,
    leadScoring: false,
    dataIntegrity: false,
    apiAccess: false
  };

  try {
    // Test 1: Database Core Functionality
    console.log('\n📊 Test 1: Core Database Functionality...');
    
    const { data: submissions, error: submissionsError } = await supabase
      .from('assessment_submissions')
      .select('id, company_name, email, status, created_at')
      .eq('status', 'completed');

    if (submissionsError) {
      console.log(`❌ Database access failed: ${submissionsError.message}`);
      allSystemsReady = false;
    } else {
      console.log(`✅ Database accessible: ${submissions?.length || 0} real submissions`);
      
      // Verify no mock data remains
      const mockData = submissions?.filter(sub => 
        sub.company_name.includes('Sample Company') || 
        sub.email.includes('samplecompany')
      ) || [];
      
      if (mockData.length > 0) {
        console.log(`❌ Mock data still present: ${mockData.length} submissions`);
        allSystemsReady = false;
      } else {
        console.log('✅ No mock data found - database is clean');
        results.database = true;
      }
    }

    // Test 2: Advanced BI Tables Access
    console.log('\n🚀 Test 2: Advanced BI Tables Access...');
    
    const biTables = [
      'enhanced_lead_scores',
      'user_journey_events',
      'ab_tests',
      'ab_test_variants',
      'ab_test_participations'
    ];

    let biTablesReady = true;
    for (const table of biTables) {
      const { count, error } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.log(`❌ ${table}: ${error.message}`);
        biTablesReady = false;
      } else {
        console.log(`✅ ${table}: Accessible (${count || 0} records)`);
      }
    }

    if (biTablesReady) {
      console.log('✅ All advanced BI tables are accessible');
      results.advancedBI = true;
    } else {
      console.log('❌ Some advanced BI tables have issues');
      allSystemsReady = false;
    }

    // Test 3: Lead Scoring System
    console.log('\n🎯 Test 3: Lead Scoring System...');
    
    const { data: leadScores, error: scoresError } = await supabase
      .from('lead_scores')
      .select('submission_id, risk_level, risk_percentage, lead_priority');

    if (scoresError) {
      console.log(`❌ Lead scoring access failed: ${scoresError.message}`);
      allSystemsReady = false;
    } else {
      console.log(`✅ Lead scoring accessible: ${leadScores?.length || 0} scores`);
      
      // Verify score distribution
      const scoreDistribution = leadScores?.reduce((acc, score) => {
        acc[score.risk_level] = (acc[score.risk_level] || 0) + 1;
        return acc;
      }, {}) || {};
      
      console.log('📊 Risk level distribution:');
      Object.entries(scoreDistribution).forEach(([level, count]) => {
        console.log(`   ${level}: ${count} submissions`);
      });
      
      results.leadScoring = true;
    }

    // Test 4: Data Integrity
    console.log('\n🔍 Test 4: Data Integrity Check...');
    
    if (submissions && leadScores) {
      const submissionIds = new Set(submissions.map(s => s.id));
      const scoreSubmissionIds = new Set(leadScores.map(s => s.submission_id));
      
      const missingScores = [...submissionIds].filter(id => !scoreSubmissionIds.has(id));
      const orphanedScores = [...scoreSubmissionIds].filter(id => !submissionIds.has(id));
      
      if (missingScores.length > 0) {
        console.log(`❌ Missing lead scores for ${missingScores.length} submissions`);
        allSystemsReady = false;
      } else if (orphanedScores.length > 0) {
        console.log(`❌ Orphaned lead scores found: ${orphanedScores.length}`);
        allSystemsReady = false;
      } else {
        console.log('✅ Data integrity verified - all submissions have corresponding lead scores');
        results.dataIntegrity = true;
      }
    }

    // Test 5: API Access and Performance
    console.log('\n⚡ Test 5: API Performance...');
    
    const startTime = Date.now();
    
    const { data: assessmentTypes, error: typesError } = await supabase
      .from('assessment_types')
      .select('id, title, slug, is_active')
      .eq('is_active', true);

    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    if (typesError) {
      console.log(`❌ API access failed: ${typesError.message}`);
      allSystemsReady = false;
    } else {
      console.log(`✅ API responsive: ${responseTime}ms response time`);
      console.log(`✅ Assessment types available: ${assessmentTypes?.length || 0}`);
      
      if (responseTime < 1000) {
        console.log('✅ API performance acceptable');
        results.apiAccess = true;
      } else {
        console.log('⚠️ API response time high - may need optimization');
      }
    }

    // Test 6: BI Dashboard Components
    console.log('\n📊 Test 6: BI Dashboard Data Availability...');
    
    // Check if we have enough data for meaningful analytics
    const dataChecks = {
      assessmentTypes: assessmentTypes?.length >= 5,
      realSubmissions: submissions?.length >= 1,
      leadScores: leadScores?.length >= 1,
      biTablesReady: results.advancedBI
    };

    console.log('📋 Dashboard data readiness:');
    Object.entries(dataChecks).forEach(([check, passed]) => {
      console.log(`   ${passed ? '✅' : '❌'} ${check}: ${passed ? 'Ready' : 'Not Ready'}`);
    });

    const dashboardReady = Object.values(dataChecks).every(check => check);
    
    if (dashboardReady) {
      console.log('✅ BI Dashboard ready for production use');
    } else {
      console.log('❌ BI Dashboard not ready - missing required data');
      allSystemsReady = false;
    }

    // Final Summary
    console.log('\n🎯 PRODUCTION READINESS SUMMARY');
    console.log('=' .repeat(40));
    
    console.log('📊 System Status:');
    Object.entries(results).forEach(([system, status]) => {
      console.log(`   ${status ? '✅' : '❌'} ${system}: ${status ? 'Ready' : 'Issues Found'}`);
    });

    if (allSystemsReady) {
      console.log('\n🎉 PRODUCTION READY!');
      console.log('✅ All systems operational');
      console.log('✅ Clean data foundation');
      console.log('✅ Advanced BI features enabled');
      console.log('✅ Lead funnel configured');
      
      console.log('\n📋 Ready for:');
      console.log('   • Live lead generation');
      console.log('   • Automated email sequences');
      console.log('   • A/B testing campaigns');
      console.log('   • Customer journey tracking');
      console.log('   • Enhanced lead scoring');
      
      console.log('\n🚀 Next Steps:');
      console.log('   1. Test BI dashboard at /admin');
      console.log('   2. Configure email automation');
      console.log('   3. Set up A/B testing campaigns');
      console.log('   4. Monitor lead generation metrics');
    } else {
      console.log('\n⚠️ PRODUCTION NOT READY');
      console.log('❌ Issues found that need resolution');
      console.log('📋 Review failed tests above');
    }

    return {
      ready: allSystemsReady,
      results: results,
      submissionCount: submissions?.length || 0,
      leadScoreCount: leadScores?.length || 0,
      responseTime: responseTime
    };

  } catch (error) {
    console.error('❌ Production readiness verification failed:', error.message);
    return {
      ready: false,
      error: error.message
    };
  }
}

// Run verification
verifyProductionReadiness().then((result) => {
  if (result.ready) {
    console.log('\n✅ BlackVeil Security platform is production-ready!');
    process.exit(0);
  } else {
    console.log('\n❌ Production readiness verification failed');
    if (result.error) {
      console.log(`Error: ${result.error}`);
    }
    process.exit(1);
  }
}).catch(error => {
  console.error('❌ Verification script failed:', error);
  process.exit(1);
});
