#!/usr/bin/env node

/**
 * BlackVeil Security Platform - Image Compression Script
 * 
 * Compresses oversized images to improve performance while maintaining quality.
 * Uses sharp library for high-quality image processing.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const CONFIG = {
  maxFileSize: 500 * 1024, // 500KB target size
  quality: 85,
  progressive: true,
  optimizeFor: 'web'
};

// Images that need compression (from audit)
const OVERSIZED_IMAGES = [
  '4deb7e82-aa37-4ab5-94f4-876c1d8787d4.png',
  '7982392e-5308-407f-a054-9dcc9a21824f.png',
  'dc1e2db4-ccd0-4882-a461-4093ccda5191.png'
];

/**
 * Check if sharp is available
 */
async function checkSharpAvailability() {
  try {
    await import('sharp');
    return true;
  } catch (e) {
    return false;
  }
}

/**
 * Compress image using sharp (if available)
 */
async function compressWithSharp(inputPath, outputPath, options = {}) {
  const sharp = await import('sharp');
  
  const {
    quality = CONFIG.quality,
    progressive = CONFIG.progressive,
    format = 'jpeg'
  } = options;

  try {
    const image = sharp.default(inputPath);
    const metadata = await image.metadata();
    
    console.log(`  Original: ${metadata.width}x${metadata.height}, ${metadata.format}`);
    
    let pipeline = image;
    
    // Resize if too large (maintain aspect ratio)
    if (metadata.width > 1920) {
      pipeline = pipeline.resize(1920, null, {
        withoutEnlargement: true,
        fit: 'inside'
      });
    }
    
    // Convert and compress based on format
    if (format === 'jpeg' || format === 'jpg') {
      pipeline = pipeline.jpeg({
        quality,
        progressive,
        mozjpeg: true
      });
    } else if (format === 'webp') {
      pipeline = pipeline.webp({
        quality,
        effort: 6
      });
    } else if (format === 'png') {
      pipeline = pipeline.png({
        compressionLevel: 9,
        adaptiveFiltering: true
      });
    }
    
    await pipeline.toFile(outputPath);
    
    const outputStats = fs.statSync(outputPath);
    console.log(`  Compressed: ${(outputStats.size / 1024).toFixed(1)}KB`);
    
    return {
      success: true,
      originalSize: metadata.size || fs.statSync(inputPath).size,
      compressedSize: outputStats.size,
      savings: ((fs.statSync(inputPath).size - outputStats.size) / fs.statSync(inputPath).size * 100).toFixed(1)
    };
  } catch (error) {
    console.error(`  Error compressing with sharp: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Fallback compression using canvas (browser-like environment)
 */
function compressWithCanvas(inputPath, outputPath, options = {}) {
  console.log('  Using fallback compression method...');
  
  // For now, just copy the file and log that manual compression is needed
  fs.copyFileSync(inputPath, outputPath);
  
  return {
    success: true,
    originalSize: fs.statSync(inputPath).size,
    compressedSize: fs.statSync(outputPath).size,
    savings: 0,
    note: 'Manual compression recommended'
  };
}

/**
 * Compress a single image
 */
async function compressImage(imageName) {
  const inputPath = path.join(process.cwd(), 'public', 'lovable-uploads', imageName);
  const outputPath = path.join(process.cwd(), 'public', 'lovable-uploads', `compressed-${imageName}`);
  
  if (!fs.existsSync(inputPath)) {
    console.log(`❌ Image not found: ${imageName}`);
    return { success: false, error: 'File not found' };
  }
  
  const originalStats = fs.statSync(inputPath);
  console.log(`📸 Compressing: ${imageName}`);
  console.log(`  Original size: ${(originalStats.size / 1024 / 1024).toFixed(2)}MB`);
  
  let result;
  
  // Try sharp first (best quality)
  if (await checkSharpAvailability()) {
    try {
      result = await compressWithSharp(inputPath, outputPath, {
        format: 'jpeg', // Convert PNG to JPEG for better compression
        quality: CONFIG.quality
      });
    } catch (error) {
      console.log(`  Sharp failed, using fallback: ${error.message}`);
      result = compressWithCanvas(inputPath, outputPath);
    }
  } else {
    console.log('  Sharp not available, using fallback method');
    result = compressWithCanvas(inputPath, outputPath);
  }
  
  if (result.success) {
    console.log(`  ✅ Saved ${result.savings}% (${(result.originalSize / 1024 / 1024).toFixed(2)}MB → ${(result.compressedSize / 1024 / 1024).toFixed(2)}MB)`);
    
    // If compression was successful and significant, replace original
    if (result.compressedSize < result.originalSize * 0.8) {
      const backupPath = path.join(process.cwd(), 'public', 'lovable-uploads', `backup-${imageName}`);
      fs.renameSync(inputPath, backupPath);
      fs.renameSync(outputPath, inputPath);
      console.log(`  📁 Original backed up as: backup-${imageName}`);
    } else {
      fs.unlinkSync(outputPath);
      console.log(`  ⚠️  Compression not significant enough, keeping original`);
    }
  } else {
    console.log(`  ❌ Compression failed: ${result.error}`);
  }
  
  return result;
}

/**
 * Main compression function
 */
async function compressOversizedImages() {
  console.log('🗜️  Starting image compression for BlackVeil Security Platform...\n');
  
  const results = [];
  
  for (const imageName of OVERSIZED_IMAGES) {
    const result = await compressImage(imageName);
    results.push({ imageName, ...result });
    console.log(''); // Add spacing between images
  }
  
  // Summary
  console.log('📊 COMPRESSION SUMMARY:');
  console.log('========================');
  
  let totalOriginalSize = 0;
  let totalCompressedSize = 0;
  let successCount = 0;
  
  for (const result of results) {
    if (result.success) {
      totalOriginalSize += result.originalSize || 0;
      totalCompressedSize += result.compressedSize || 0;
      successCount++;
      console.log(`✅ ${result.imageName}: ${result.savings}% savings`);
    } else {
      console.log(`❌ ${result.imageName}: ${result.error}`);
    }
  }
  
  if (successCount > 0) {
    const totalSavings = ((totalOriginalSize - totalCompressedSize) / totalOriginalSize * 100).toFixed(1);
    console.log(`\n🎉 Total savings: ${totalSavings}% (${(totalOriginalSize / 1024 / 1024).toFixed(2)}MB → ${(totalCompressedSize / 1024 / 1024).toFixed(2)}MB)`);
    console.log(`📈 Performance improvement: Faster page loads, better user experience`);
  }
  
  console.log('\n💡 NEXT STEPS:');
  console.log('- Test the compressed images on the website');
  console.log('- Monitor page load performance improvements');
  console.log('- Consider setting up automatic compression in the build process');
  
  if (!checkSharpAvailability()) {
    console.log('\n⚠️  RECOMMENDATION:');
    console.log('Install sharp for better compression: npm install sharp');
    console.log('This will provide higher quality compression with smaller file sizes.');
  }
}

/**
 * Install sharp if not available
 */
async function installSharp() {
  console.log('📦 Installing sharp for optimal image compression...');
  
  try {
    const { execSync } = await import('child_process');
    execSync('npm install sharp', { stdio: 'inherit' });
    console.log('✅ Sharp installed successfully!');
    return true;
  } catch (error) {
    console.log('❌ Failed to install sharp automatically');
    console.log('Please run: npm install sharp');
    return false;
  }
}

// Main execution
async function main() {
  // Check if sharp is available, offer to install if not
  if (!(await checkSharpAvailability())) {
    console.log('⚠️  Sharp library not found for optimal compression.');
    console.log('Would you like to install it? (Recommended for best results)');

    // For automated execution, try to install sharp
    const installed = await installSharp();
    if (!installed) {
      console.log('Proceeding with fallback compression method...\n');
    }
  }
  
  await compressOversizedImages();
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { compressOversizedImages, compressImage };
