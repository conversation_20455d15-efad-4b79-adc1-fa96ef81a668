-- BlackVeil Security - Fix Missing Question Options
-- This script identifies and creates missing answer options for assessment questions
-- Execute this script in Supabase SQL Editor after creating the advanced BI schema

-- 1. Identify questions without options
DO $$
DECLARE
    question_record RECORD;
    question_count INTEGER := 0;
BEGIN
    RAISE NOTICE 'Identifying questions without answer options...';
    
    FOR question_record IN 
        SELECT q.id, q.question_text, q.category, at.title as assessment_title
        FROM assessment_questions q
        JOIN assessment_types at ON q.assessment_type_id = at.id
        LEFT JOIN assessment_question_options o ON q.id = o.question_id
        WHERE o.question_id IS NULL
        ORDER BY at.title, q.order_index
    LOOP
        question_count := question_count + 1;
        RAISE NOTICE 'Question %: % - %', question_count, question_record.assessment_title, question_record.question_text;
    END LOOP;
    
    RAISE NOTICE 'Found % questions without options', question_count;
END $$;

-- 2. Create standard answer options for cybersecurity questions
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
    q.id,
    option_data.option_text,
    option_data.risk_score,
    option_data.order_index
FROM assessment_questions q
JOIN assessment_types at ON q.assessment_type_id = at.id
CROSS JOIN (
    VALUES 
        ('No measures in place', 4, 1),
        ('Basic measures implemented', 3, 2),
        ('Good security practices', 2, 3),
        ('Advanced security measures', 1, 4)
) AS option_data(option_text, risk_score, order_index)
WHERE at.slug = 'cybersecurity-maturity'
AND q.category IN ('Access Control', 'Network Security', 'Threat Detection', 'Incident Response')
AND NOT EXISTS (
    SELECT 1 FROM assessment_question_options o WHERE o.question_id = q.id
)
ON CONFLICT DO NOTHING;

-- 3. Create specific options for Access Control questions
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
    q.id,
    option_data.option_text,
    option_data.risk_score,
    option_data.order_index
FROM assessment_questions q
JOIN assessment_types at ON q.assessment_type_id = at.id
CROSS JOIN (
    VALUES 
        ('No access controls - shared accounts', 4, 1),
        ('Basic password protection only', 3, 2),
        ('Role-based access with regular reviews', 2, 3),
        ('Multi-factor authentication and zero trust', 1, 4)
) AS option_data(option_text, risk_score, order_index)
WHERE at.slug = 'cybersecurity-maturity'
AND q.category = 'Access Control'
AND q.question_text ILIKE '%access%'
AND NOT EXISTS (
    SELECT 1 FROM assessment_question_options o WHERE o.question_id = q.id
)
ON CONFLICT DO NOTHING;

-- 4. Create specific options for Network Security questions
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
    q.id,
    option_data.option_text,
    option_data.risk_score,
    option_data.order_index
FROM assessment_questions q
JOIN assessment_types at ON q.assessment_type_id = at.id
CROSS JOIN (
    VALUES 
        ('No firewall or network protection', 4, 1),
        ('Basic firewall with default settings', 3, 2),
        ('Configured firewall with monitoring', 2, 3),
        ('Advanced network security with IDS/IPS', 1, 4)
) AS option_data(option_text, risk_score, order_index)
WHERE at.slug = 'cybersecurity-maturity'
AND q.category = 'Network Security'
AND NOT EXISTS (
    SELECT 1 FROM assessment_question_options o WHERE o.question_id = q.id
)
ON CONFLICT DO NOTHING;

-- 5. Create specific options for Threat Detection questions
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
    q.id,
    option_data.option_text,
    option_data.risk_score,
    option_data.order_index
FROM assessment_questions q
JOIN assessment_types at ON q.assessment_type_id = at.id
CROSS JOIN (
    VALUES 
        ('No monitoring or detection systems', 4, 1),
        ('Basic antivirus software only', 3, 2),
        ('Security monitoring with alerts', 2, 3),
        ('Advanced threat detection and response', 1, 4)
) AS option_data(option_text, risk_score, order_index)
WHERE at.slug = 'cybersecurity-maturity'
AND q.category = 'Threat Detection'
AND NOT EXISTS (
    SELECT 1 FROM assessment_question_options o WHERE o.question_id = q.id
)
ON CONFLICT DO NOTHING;

-- 6. Create specific options for Incident Response questions
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
    q.id,
    option_data.option_text,
    option_data.risk_score,
    option_data.order_index
FROM assessment_questions q
JOIN assessment_types at ON q.assessment_type_id = at.id
CROSS JOIN (
    VALUES 
        ('No incident response plan', 4, 1),
        ('Basic plan but not tested', 3, 2),
        ('Documented plan with regular testing', 2, 3),
        ('Comprehensive plan with automated response', 1, 4)
) AS option_data(option_text, risk_score, order_index)
WHERE at.slug = 'cybersecurity-maturity'
AND q.category = 'Incident Response'
AND NOT EXISTS (
    SELECT 1 FROM assessment_question_options o WHERE o.question_id = q.id
)
ON CONFLICT DO NOTHING;

-- 7. Create options for other assessment types (Phishing, Ransomware, etc.)
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
    q.id,
    option_data.option_text,
    option_data.risk_score,
    option_data.order_index
FROM assessment_questions q
JOIN assessment_types at ON q.assessment_type_id = at.id
CROSS JOIN (
    VALUES 
        ('Not implemented', 4, 1),
        ('Partially implemented', 3, 2),
        ('Mostly implemented', 2, 3),
        ('Fully implemented', 1, 4)
) AS option_data(option_text, risk_score, order_index)
WHERE at.slug IN ('phishing-risk', 'ransomware-readiness', 'ai-security', 'supply-chain-security', 'cloud-security', 'zero-trust')
AND NOT EXISTS (
    SELECT 1 FROM assessment_question_options o WHERE o.question_id = q.id
)
ON CONFLICT DO NOTHING;

-- 8. Create specific DMARC compliance options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT 
    q.id,
    CASE 
        WHEN q.question_text ILIKE '%SPF%' THEN 
            CASE option_data.order_index
                WHEN 1 THEN 'No SPF record configured'
                WHEN 2 THEN 'SPF record exists but not optimized'
                WHEN 3 THEN 'SPF properly configured'
                WHEN 4 THEN 'SPF with comprehensive coverage'
            END
        WHEN q.question_text ILIKE '%DKIM%' THEN 
            CASE option_data.order_index
                WHEN 1 THEN 'No DKIM signing'
                WHEN 2 THEN 'DKIM partially configured'
                WHEN 3 THEN 'DKIM properly implemented'
                WHEN 4 THEN 'DKIM with key rotation'
            END
        WHEN q.question_text ILIKE '%DMARC%' THEN 
            CASE option_data.order_index
                WHEN 1 THEN 'No DMARC policy'
                WHEN 2 THEN 'DMARC policy set to none'
                WHEN 3 THEN 'DMARC policy set to quarantine'
                WHEN 4 THEN 'DMARC policy set to reject'
            END
        ELSE option_data.option_text
    END,
    option_data.risk_score,
    option_data.order_index
FROM assessment_questions q
JOIN assessment_types at ON q.assessment_type_id = at.id
CROSS JOIN (
    VALUES 
        ('Not configured', 4, 1),
        ('Basic configuration', 3, 2),
        ('Good configuration', 2, 3),
        ('Optimal configuration', 1, 4)
) AS option_data(option_text, risk_score, order_index)
WHERE at.slug = 'dmarc-compliance'
AND NOT EXISTS (
    SELECT 1 FROM assessment_question_options o WHERE o.question_id = q.id
)
ON CONFLICT DO NOTHING;

-- 9. Verification and summary
DO $$
DECLARE
    total_questions INTEGER;
    questions_with_options INTEGER;
    questions_without_options INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_questions FROM assessment_questions;
    
    SELECT COUNT(DISTINCT q.id) INTO questions_with_options
    FROM assessment_questions q
    JOIN assessment_question_options o ON q.id = o.question_id;
    
    questions_without_options := total_questions - questions_with_options;
    
    RAISE NOTICE 'Question Options Fix Summary:';
    RAISE NOTICE 'Total questions: %', total_questions;
    RAISE NOTICE 'Questions with options: %', questions_with_options;
    RAISE NOTICE 'Questions still missing options: %', questions_without_options;
    
    IF questions_without_options = 0 THEN
        RAISE NOTICE 'SUCCESS: All questions now have complete answer options!';
    ELSE
        RAISE NOTICE 'WARNING: % questions still need manual option creation', questions_without_options;
    END IF;
END $$;

-- 10. Final verification query
SELECT 
    at.title as assessment_type,
    COUNT(DISTINCT q.id) as total_questions,
    COUNT(DISTINCT CASE WHEN o.question_id IS NOT NULL THEN q.id END) as questions_with_options,
    COUNT(DISTINCT CASE WHEN o.question_id IS NULL THEN q.id END) as questions_missing_options
FROM assessment_types at
JOIN assessment_questions q ON at.id = q.assessment_type_id
LEFT JOIN assessment_question_options o ON q.id = o.question_id
GROUP BY at.id, at.title
ORDER BY at.title;
