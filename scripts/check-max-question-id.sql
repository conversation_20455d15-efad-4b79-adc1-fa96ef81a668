-- Check the current maximum question ID to avoid conflicts
SELECT 
  MAX(id) as max_question_id,
  COUNT(*) as total_questions
FROM assessment_questions;

-- Also check if our target assessments already have questions
SELECT 
  at.title,
  at.slug,
  COUNT(aq.id) as existing_questions
FROM assessment_types at
LEFT JOIN assessment_questions aq ON at.id = aq.assessment_type_id
WHERE at.slug IN ('cybersecurity-maturity', 'dmarc-compliance')
GROUP BY at.id, at.title, at.slug
ORDER BY at.title;
