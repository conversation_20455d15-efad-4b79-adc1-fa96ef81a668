// Debug Table Access Script
// This script investigates why tables appear to exist but can't be accessed

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env.local file
let supabaseUrl, supabaseKey;
try {
  const envPath = join(__dirname, '..', '.env.local');
  const envContent = readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  envLines.forEach(line => {
    const [key, value] = line.split('=');
    if (key === 'VITE_SUPABASE_URL') supabaseUrl = value;
    if (key === 'VITE_SUPABASE_ANON_KEY') supabaseKey = value;
  });
} catch (error) {
  console.error('❌ Could not load .env.local file:', error.message);
}

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function debugTableAccess() {
  console.log('🔍 BlackVeil Security - Table Access Debug');
  console.log('=' .repeat(50));
  
  const tables = [
    'enhanced_lead_scores',
    'user_journey_events', 
    'ab_tests',
    'ab_test_variants',
    'ab_test_participations'
  ];

  for (const tableName of tables) {
    console.log(`\n🔍 Debugging table: ${tableName}`);
    console.log('-' .repeat(30));
    
    // Test 1: Basic select with count
    try {
      console.log('📊 Test 1: Basic count query...');
      const { count, error: countError } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });
      
      if (countError) {
        console.log(`❌ Count error: ${countError.message}`);
        console.log(`   Code: ${countError.code}`);
        console.log(`   Details: ${countError.details}`);
        console.log(`   Hint: ${countError.hint}`);
      } else {
        console.log(`✅ Count successful: ${count} records`);
      }
    } catch (err) {
      console.log(`❌ Count exception: ${err.message}`);
    }

    // Test 2: Simple select
    try {
      console.log('📋 Test 2: Simple select query...');
      const { data, error: selectError } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);
      
      if (selectError) {
        console.log(`❌ Select error: ${selectError.message}`);
        console.log(`   Code: ${selectError.code}`);
        console.log(`   Details: ${selectError.details}`);
        console.log(`   Hint: ${selectError.hint}`);
      } else {
        console.log(`✅ Select successful: ${data?.length || 0} records returned`);
      }
    } catch (err) {
      console.log(`❌ Select exception: ${err.message}`);
    }

    // Test 3: Schema information
    try {
      console.log('🏗️ Test 3: Schema information...');
      const { data: schemaData, error: schemaError } = await supabase
        .from('information_schema.tables')
        .select('table_name, table_schema')
        .eq('table_name', tableName);
      
      if (schemaError) {
        console.log(`❌ Schema query error: ${schemaError.message}`);
      } else {
        console.log(`📋 Schema info: ${JSON.stringify(schemaData)}`);
      }
    } catch (err) {
      console.log(`❌ Schema query exception: ${err.message}`);
    }

    // Test 4: RLS policy check
    try {
      console.log('🔒 Test 4: RLS policy check...');
      const { data: rlsData, error: rlsError } = await supabase
        .from('pg_policies')
        .select('policyname, tablename')
        .eq('tablename', tableName);
      
      if (rlsError) {
        console.log(`❌ RLS query error: ${rlsError.message}`);
      } else {
        console.log(`🔒 RLS policies: ${JSON.stringify(rlsData)}`);
      }
    } catch (err) {
      console.log(`❌ RLS query exception: ${err.message}`);
    }
  }

  // Test 5: Check if we can access any system tables
  console.log('\n🔍 System Table Access Test:');
  try {
    const { data: sysData, error: sysError } = await supabase
      .from('pg_tables')
      .select('tablename, schemaname')
      .eq('schemaname', 'public')
      .like('tablename', '%lead%');
    
    if (sysError) {
      console.log(`❌ System table error: ${sysError.message}`);
    } else {
      console.log(`📋 Tables with 'lead' in name: ${JSON.stringify(sysData)}`);
    }
  } catch (err) {
    console.log(`❌ System table exception: ${err.message}`);
  }

  // Test 6: Check current user and role
  console.log('\n👤 User and Role Information:');
  try {
    const { data: userData, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      console.log(`❌ User info error: ${userError.message}`);
      console.log('📋 Using anonymous access (anon key)');
    } else {
      console.log(`✅ Authenticated user: ${userData.user?.email || 'Unknown'}`);
    }
  } catch (err) {
    console.log(`❌ User info exception: ${err.message}`);
  }

  // Test 7: Try to access a known working table for comparison
  console.log('\n✅ Control Test - Known Working Table:');
  try {
    const { count: controlCount, error: controlError } = await supabase
      .from('assessment_submissions')
      .select('*', { count: 'exact', head: true });
    
    if (controlError) {
      console.log(`❌ Control table error: ${controlError.message}`);
    } else {
      console.log(`✅ Control table (assessment_submissions): ${controlCount} records`);
    }
  } catch (err) {
    console.log(`❌ Control table exception: ${err.message}`);
  }
}

// Run the debug
debugTableAccess().then(() => {
  console.log('\n✅ Table access debug completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Debug failed:', error);
  process.exit(1);
});
