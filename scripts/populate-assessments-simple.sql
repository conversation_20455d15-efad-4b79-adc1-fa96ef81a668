-- Simple Assessment Population Script
-- This script uses the most straightforward approach to avoid any data type issues

-- First, let's insert questions for Cybersecurity Maturity Assessment
-- We'll let the database auto-generate the question IDs

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT id, 'How often does your organization conduct cybersecurity risk assessments?', 'Risk Management', 1, true
FROM assessment_types WHERE slug = 'cybersecurity-maturity';

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT id, 'What type of cybersecurity training do you provide to employees?', 'Security Awareness', 2, true
FROM assessment_types WHERE slug = 'cybersecurity-maturity';

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT id, 'How does your organization manage software updates and patches?', 'Patch Management', 3, true
FROM assessment_types WHERE slug = 'cybersecurity-maturity';

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT id, 'What backup and recovery procedures do you have in place?', 'Business Continuity', 4, true
FROM assessment_types WHERE slug = 'cybersecurity-maturity';

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT id, 'How do you control access to sensitive systems and data?', 'Access Control', 5, true
FROM assessment_types WHERE slug = 'cybersecurity-maturity';

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT id, 'Do you have an incident response plan for cybersecurity breaches?', 'Incident Response', 6, true
FROM assessment_types WHERE slug = 'cybersecurity-maturity';

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT id, 'How do you monitor your network for security threats?', 'Threat Detection', 7, true
FROM assessment_types WHERE slug = 'cybersecurity-maturity';

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT id, 'What is your approach to vendor and third-party security?', 'Third-Party Risk', 8, true
FROM assessment_types WHERE slug = 'cybersecurity-maturity';

-- Now insert questions for DMARC Compliance Assessment
INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT id, 'Have you implemented SPF (Sender Policy Framework) for your domain?', 'Email Authentication', 1, true
FROM assessment_types WHERE slug = 'dmarc-compliance';

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT id, 'Do you have DKIM (DomainKeys Identified Mail) configured?', 'Email Authentication', 2, true
FROM assessment_types WHERE slug = 'dmarc-compliance';

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT id, 'What is your current DMARC policy setting?', 'DMARC Policy', 3, true
FROM assessment_types WHERE slug = 'dmarc-compliance';

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT id, 'How do you monitor DMARC reports and failures?', 'Monitoring', 4, true
FROM assessment_types WHERE slug = 'dmarc-compliance';

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT id, 'Do you have email security awareness training for employees?', 'Security Awareness', 5, true
FROM assessment_types WHERE slug = 'dmarc-compliance';

INSERT INTO assessment_questions (assessment_type_id, question_text, category, order_index, is_active)
SELECT id, 'How do you handle email from external domains that fail authentication?', 'Email Filtering', 6, true
FROM assessment_types WHERE slug = 'dmarc-compliance';

-- Now insert options for Cybersecurity Maturity Assessment questions
-- Question 1 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Never or rarely (annually or less)', 4, 1 
FROM assessment_questions aq 
JOIN assessment_types at ON aq.assessment_type_id = at.id 
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 1;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Occasionally (every 6-12 months)', 3, 2 
FROM assessment_questions aq 
JOIN assessment_types at ON aq.assessment_type_id = at.id 
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 1;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Regularly (quarterly)', 2, 3 
FROM assessment_questions aq 
JOIN assessment_types at ON aq.assessment_type_id = at.id 
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 1;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Continuously (monthly or more)', 1, 4 
FROM assessment_questions aq 
JOIN assessment_types at ON aq.assessment_type_id = at.id 
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 1;

-- Question 2 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'No formal training provided', 4, 1 
FROM assessment_questions aq 
JOIN assessment_types at ON aq.assessment_type_id = at.id 
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 2;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Basic annual training only', 3, 2 
FROM assessment_questions aq 
JOIN assessment_types at ON aq.assessment_type_id = at.id 
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 2;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Regular training with updates', 2, 3 
FROM assessment_questions aq 
JOIN assessment_types at ON aq.assessment_type_id = at.id 
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 2;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Comprehensive ongoing training with simulations', 1, 4 
FROM assessment_questions aq 
JOIN assessment_types at ON aq.assessment_type_id = at.id 
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 2;

-- Question 3 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Manual updates when remembered', 4, 1 
FROM assessment_questions aq 
JOIN assessment_types at ON aq.assessment_type_id = at.id 
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 3;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Scheduled monthly updates', 3, 2 
FROM assessment_questions aq 
JOIN assessment_types at ON aq.assessment_type_id = at.id 
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 3;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Automated updates for most systems', 2, 3 
FROM assessment_questions aq 
JOIN assessment_types at ON aq.assessment_type_id = at.id 
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 3;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Comprehensive automated patch management', 1, 4 
FROM assessment_questions aq 
JOIN assessment_types at ON aq.assessment_type_id = at.id 
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 3;

-- Question 4 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'No formal backup procedures', 4, 1 
FROM assessment_questions aq 
JOIN assessment_types at ON aq.assessment_type_id = at.id 
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 4;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Basic local backups', 3, 2 
FROM assessment_questions aq 
JOIN assessment_types at ON aq.assessment_type_id = at.id 
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 4;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Regular offsite backups', 2, 3 
FROM assessment_questions aq 
JOIN assessment_types at ON aq.assessment_type_id = at.id 
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 4;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Comprehensive backup with tested recovery', 1, 4
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 4;

-- Continue with remaining questions for cybersecurity-maturity (questions 5-8)
-- Question 5 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Basic password protection only', 4, 1
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 5;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Role-based access with passwords', 3, 2
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 5;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Multi-factor authentication for some systems', 2, 3
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 5;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Comprehensive MFA and zero-trust approach', 1, 4
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 5;

-- Question 6 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'No formal incident response plan', 4, 1
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 6;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Basic plan but not tested', 3, 2
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 6;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Documented plan with some testing', 2, 3
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 6;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Comprehensive tested plan with regular drills', 1, 4
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 6;

-- Question 7 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'No active monitoring', 4, 1
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 7;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Basic antivirus software only', 3, 2
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 7;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Network monitoring with some alerting', 2, 3
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 7;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Advanced threat detection and response', 1, 4
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 7;

-- Question 8 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'No vendor security assessments', 4, 1
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 8;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Basic vendor questionnaires', 3, 2
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 8;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Regular vendor security reviews', 2, 3
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 8;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Comprehensive third-party risk management', 1, 4
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'cybersecurity-maturity' AND aq.order_index = 8;

-- Now add options for DMARC Compliance Assessment questions
-- DMARC Question 1 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'No SPF record configured', 4, 1
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 1;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Basic SPF record with some gaps', 3, 2
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 1;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Comprehensive SPF record configured', 2, 3
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 1;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'SPF with strict policy and monitoring', 1, 4
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 1;

-- DMARC Question 2 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'No DKIM signing configured', 4, 1
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 2;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'DKIM for some email services only', 3, 2
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 2;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'DKIM configured for all outbound email', 2, 3
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 2;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'DKIM with key rotation and monitoring', 1, 4
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 2;

-- DMARC Question 3 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'No DMARC record published', 4, 1
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 3;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'DMARC with none policy (monitoring only)', 3, 2
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 3;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'DMARC with quarantine policy', 2, 3
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 3;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'DMARC with reject policy', 1, 4
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 3;

-- DMARC Question 4 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'No DMARC report monitoring', 4, 1
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 4;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Occasional manual review of reports', 3, 2
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 4;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Regular monitoring with basic analysis', 2, 3
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 4;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Automated monitoring with alerting', 1, 4
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 4;

-- DMARC Question 5 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'No email security training', 4, 1
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 5;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Basic annual training', 3, 2
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 5;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Regular training with phishing simulations', 2, 3
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 5;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Comprehensive ongoing training program', 1, 4
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 5;

-- DMARC Question 6 options
INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'No special handling for failed authentication', 4, 1
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 6;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Basic spam filtering only', 3, 2
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 6;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Enhanced filtering with some authentication checks', 2, 3
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 6;

INSERT INTO assessment_question_options (question_id, option_text, risk_score, order_index)
SELECT aq.id, 'Strict authentication-based filtering', 1, 4
FROM assessment_questions aq
JOIN assessment_types at ON aq.assessment_type_id = at.id
WHERE at.slug = 'dmarc-compliance' AND aq.order_index = 6;

-- Verification query to check what we have added
SELECT
  at.title as assessment_title,
  at.slug,
  COUNT(DISTINCT aq.id) as question_count,
  COUNT(aqo.id) as option_count
FROM assessment_types at
LEFT JOIN assessment_questions aq ON at.id = aq.assessment_type_id
LEFT JOIN assessment_question_options aqo ON aq.id = aqo.question_id
WHERE at.slug IN ('cybersecurity-maturity', 'dmarc-compliance')
GROUP BY at.id, at.title, at.slug
ORDER BY at.title;
