// Populate Sample BI Data Script
// This script adds sample data to the advanced BI tables

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://wikngnwwakatokbgvenw.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODU2NjQ1NCwiZXhwIjoyMDY0MTQyNDU0fQ.j1sjkEHL2LR78ChzOL6MdieomYgSk1CNXXR0n5sq9eo';

const supabase = createClient(supabaseUrl, serviceKey);

async function populateSampleData() {
  console.log('📝 Populating Sample Advanced BI Data');
  console.log('=' .repeat(40));

  try {
    // 1. Create sample A/B tests
    console.log('\n🧪 Creating sample A/B tests...');
    const { data: abTests, error: abTestsError } = await supabase
      .from('ab_tests')
      .upsert([
        {
          id: '550e8400-e29b-41d4-a716-446655440001',
          name: 'Assessment CTA Button Test',
          description: 'Testing different call-to-action button colors and text',
          test_type: 'cta_button',
          hypothesis: 'Green buttons will have higher conversion than blue buttons',
          success_metric: 'assessment_completion_rate',
          is_active: true
        },
        {
          id: '550e8400-e29b-41d4-a716-446655440002',
          name: 'Landing Page Layout Test',
          description: 'Testing hero section layouts for better engagement',
          test_type: 'landing_page',
          hypothesis: 'Centered layout will perform better than left-aligned',
          success_metric: 'time_on_page',
          is_active: true
        },
        {
          id: '550e8400-e29b-41d4-a716-446655440003',
          name: 'Email Template Test',
          description: 'Testing follow-up email templates for lead nurturing',
          test_type: 'email_template',
          hypothesis: 'Personalized emails will have higher open rates',
          success_metric: 'email_open_rate',
          is_active: false
        }
      ], { onConflict: 'id' });

    if (abTestsError) {
      console.error('❌ Error creating A/B tests:', abTestsError.message);
    } else {
      console.log(`✅ Created ${abTests?.length || 0} A/B tests`);
    }

    // 2. Create sample A/B test variants
    console.log('\n🔀 Creating sample A/B test variants...');
    const { data: variants, error: variantsError } = await supabase
      .from('ab_test_variants')
      .upsert([
        {
          test_id: '550e8400-e29b-41d4-a716-446655440001',
          name: 'Control - Blue Button',
          description: 'Original blue CTA button',
          config: { button_color: 'blue', button_text: 'Start Assessment' },
          traffic_percentage: 50,
          participants: 120,
          conversions: 18,
          is_control: true
        },
        {
          test_id: '550e8400-e29b-41d4-a716-446655440001',
          name: 'Variant - Green Button',
          description: 'Green CTA button with action text',
          config: { button_color: 'green', button_text: 'Get My Security Score' },
          traffic_percentage: 50,
          participants: 115,
          conversions: 23,
          is_control: false
        },
        {
          test_id: '550e8400-e29b-41d4-a716-446655440002',
          name: 'Control - Left Layout',
          description: 'Original left-aligned hero section',
          config: { layout: 'left', hero_position: 'left' },
          traffic_percentage: 50,
          participants: 95,
          conversions: 12,
          is_control: true
        },
        {
          test_id: '550e8400-e29b-41d4-a716-446655440002',
          name: 'Variant - Center Layout',
          description: 'Centered hero section layout',
          config: { layout: 'center', hero_position: 'center' },
          traffic_percentage: 50,
          participants: 98,
          conversions: 16,
          is_control: false
        }
      ], { onConflict: 'test_id,name' });

    if (variantsError) {
      console.error('❌ Error creating A/B test variants:', variantsError.message);
    } else {
      console.log(`✅ Created ${variants?.length || 0} A/B test variants`);
    }

    // 3. Create sample enhanced lead scores
    console.log('\n🎯 Creating sample enhanced lead scores...');
    
    // Get some submission IDs to work with
    const { data: submissions } = await supabase
      .from('assessment_submissions')
      .select('id')
      .eq('status', 'completed')
      .limit(5);

    if (submissions && submissions.length > 0) {
      const enhancedScores = submissions.map((submission, index) => ({
        submission_id: submission.id,
        completion_score: 85 + (index * 3),
        industry_score: 70 + (index * 5),
        size_score: 60 + (index * 4),
        engagement_score: 80 + (index * 2),
        urgency_score: 65 + (index * 6),
        total_score: 360 + (index * 20),
        conversion_probability: 72 + (index * 4),
        priority_level: ['MEDIUM', 'HIGH', 'URGENT', 'HIGH', 'MEDIUM'][index] || 'MEDIUM',
        recommended_actions: {
          immediate: ['Schedule security assessment call', 'Send detailed proposal'],
          follow_up: ['Weekly check-in', 'Share case studies'],
          timeline: '7-14 days'
        }
      }));

      const { data: enhancedData, error: enhancedError } = await supabase
        .from('enhanced_lead_scores')
        .upsert(enhancedScores, { onConflict: 'submission_id' });

      if (enhancedError) {
        console.error('❌ Error creating enhanced lead scores:', enhancedError.message);
      } else {
        console.log(`✅ Created ${enhancedData?.length || 0} enhanced lead scores`);
      }
    } else {
      console.log('⚠️ No completed submissions found for enhanced lead scores');
    }

    // 4. Create sample user journey events
    console.log('\n🛤️ Creating sample user journey events...');
    const journeyEvents = [
      {
        event_type: 'page_view',
        page_url: '/',
        session_id: 'session_001',
        event_data: { referrer: 'google.com', utm_source: 'organic' }
      },
      {
        event_type: 'assessment_start',
        page_url: '/assessment/phishing-risk',
        session_id: 'session_001',
        event_data: { assessment_type: 'phishing-risk' }
      },
      {
        event_type: 'assessment_progress',
        page_url: '/assessment/phishing-risk',
        session_id: 'session_001',
        event_data: { question_number: 3, total_questions: 8 }
      },
      {
        event_type: 'assessment_complete',
        page_url: '/assessment/phishing-risk/results',
        session_id: 'session_001',
        submission_id: submissions?.[0]?.id,
        event_data: { completion_time: 180, risk_score: 75 }
      },
      {
        event_type: 'result_view',
        page_url: '/assessment/phishing-risk/results',
        session_id: 'session_001',
        submission_id: submissions?.[0]?.id,
        event_data: { time_on_results: 45 }
      },
      {
        event_type: 'page_view',
        page_url: '/',
        session_id: 'session_002',
        event_data: { referrer: 'linkedin.com', utm_source: 'social' }
      },
      {
        event_type: 'assessment_start',
        page_url: '/assessment/cybersecurity-maturity',
        session_id: 'session_002',
        event_data: { assessment_type: 'cybersecurity-maturity' }
      }
    ];

    const { data: journeyData, error: journeyError } = await supabase
      .from('user_journey_events')
      .insert(journeyEvents);

    if (journeyError) {
      console.error('❌ Error creating user journey events:', journeyError.message);
    } else {
      console.log(`✅ Created ${journeyEvents.length} user journey events`);
    }

    // 5. Create sample A/B test participations
    console.log('\n👥 Creating sample A/B test participations...');
    const participations = [
      {
        test_id: '550e8400-e29b-41d4-a716-446655440001',
        variant_id: variants?.[0]?.id,
        session_id: 'session_001',
        converted: true,
        conversion_value: 1,
        conversion_timestamp: new Date().toISOString()
      },
      {
        test_id: '550e8400-e29b-41d4-a716-446655440001',
        variant_id: variants?.[1]?.id,
        session_id: 'session_002',
        converted: false
      },
      {
        test_id: '550e8400-e29b-41d4-a716-446655440002',
        variant_id: variants?.[2]?.id,
        session_id: 'session_003',
        converted: true,
        conversion_value: 1,
        conversion_timestamp: new Date().toISOString()
      }
    ];

    if (variants && variants.length > 0) {
      const { data: participationData, error: participationError } = await supabase
        .from('ab_test_participations')
        .upsert(participations, { onConflict: 'test_id,session_id' });

      if (participationError) {
        console.error('❌ Error creating A/B test participations:', participationError.message);
      } else {
        console.log(`✅ Created ${participations.length} A/B test participations`);
      }
    }

    console.log('\n🎉 Sample data population completed!');
    return true;

  } catch (error) {
    console.error('❌ Sample data population failed:', error.message);
    return false;
  }
}

// Run the population
populateSampleData().then((success) => {
  if (success) {
    console.log('\n✅ All sample data created successfully!');
    console.log('📋 Next steps:');
    console.log('   1. Run: node scripts/database-inspection.js');
    console.log('   2. Test the BI dashboard at /admin');
    console.log('   3. Verify all advanced features work');
    process.exit(0);
  } else {
    console.log('\n❌ Sample data creation failed');
    process.exit(1);
  }
}).catch(error => {
  console.error('❌ Script execution failed:', error);
  process.exit(1);
});
